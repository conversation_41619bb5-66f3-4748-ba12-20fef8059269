"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[784],{6221:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},4972:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]])},5302:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},8906:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},7829:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},5805:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},1239:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},6741:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},8068:function(e,t,n){n.d(t,{B:function(){return l}});var r=n(2265),o=n(3966),u=n(8575),i=n(7053),a=n(7437);function l(e){let t=e+"CollectionProvider",[n,l]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:u,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",p=(0,i.Z8)(d),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),i=(0,u.e)(t,o.collectionRef);return(0,a.jsx)(p,{ref:i,children:r})});m.displayName=d;let v=e+"CollectionItemSlot",h="data-radix-collection-item",y=(0,i.Z8)(v),b=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,l=r.useRef(null),c=(0,u.e)(t,l),f=s(v,n);return r.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,a.jsx)(y,{[h]:"",ref:c,children:o})});return b.displayName=v,[{Provider:f,Slot:m,ItemSlot:b},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},l]}},3966:function(e,t,n){n.d(t,{b:function(){return u}});var r=n(2265),o=n(7437);function u(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),a=n.length;n=[...n,u];let l=t=>{let{scope:n,children:u,...l}=t,c=n?.[e]?.[a]||i,s=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:s,children:u})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[a]||i,c=r.useContext(l);if(c)return c;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},9114:function(e,t,n){n.d(t,{gm:function(){return u}});var r=n(2265);n(7437);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},9255:function(e,t,n){n.d(t,{M:function(){return l}});var r,o=n(2265),u=n(1188),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function l(e){let[t,n]=o.useState(i());return(0,u.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1599:function(e,t,n){n.d(t,{z:function(){return i}});var r=n(2265),o=n(8575),u=n(1188),i=e=>{var t,n;let i,l;let{present:c,children:s}=e,f=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(l.current);s.current="mounted"===f?e:"none"},[f]),(0,u.b)(()=>{let t=l.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,u.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=a(l.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=a(l.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(c),d="function"==typeof s?s({present:f.isPresent}):r.Children.only(s),p=(0,o.e)(f.ref,(i=null===(t=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?d.ref:(i=null===(n=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof s||f.isPresent?r.cloneElement(d,{ref:p}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},6840:function(e,t,n){n.d(t,{WV:function(){return a},jH:function(){return l}});var r=n(2265),o=n(4887),u=n(7053),i=n(7437),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,a=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1353:function(e,t,n){n.d(t,{Pc:function(){return g},ck:function(){return S},fC:function(){return I}});var r=n(2265),o=n(6741),u=n(8068),i=n(8575),a=n(3966),l=n(9255),c=n(6840),s=n(6606),f=n(886),d=n(9114),p=n(7437),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[y,b,M]=(0,u.B)(h),[w,g]=(0,a.b)(h,[M]),[N,k]=w(h),x=r.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));x.displayName=h;var R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:u,loop:a=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:M,onCurrentTabStopIdChange:w,onEntryFocus:g,preventScrollOnEntryFocus:k=!1,...x}=e,R=r.useRef(null),C=(0,i.e)(t,R),T=(0,d.gm)(l),[A,I]=(0,f.T)({prop:y,defaultProp:null!=M?M:null,onChange:w,caller:h}),[S,D]=r.useState(!1),j=(0,s.W)(g),F=b(n),P=r.useRef(!1),[Z,O]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(m,j),()=>e.removeEventListener(m,j)},[j]),(0,p.jsx)(N,{scope:n,orientation:u,dir:T,loop:a,currentTabStopId:A,onItemFocus:r.useCallback(e=>I(e),[I]),onItemShiftTab:r.useCallback(()=>D(!0),[]),onFocusableItemAdd:r.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>O(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:S||0===Z?-1:0,"data-orientation":u,...x,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),k)}}P.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>D(!1))})})}),C="RovingFocusGroupItem",T=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:u=!0,active:i=!1,tabStopId:a,children:s,...f}=e,d=(0,l.M)(),m=a||d,v=k(C,n),h=v.currentTabStopId===m,M=b(n),{onFocusableItemAdd:w,onFocusableItemRemove:g,currentTabStopId:N}=v;return r.useEffect(()=>{if(u)return w(),()=>g()},[u,w,g]),(0,p.jsx)(y.ItemSlot,{scope:n,id:m,focusable:u,active:i,children:(0,p.jsx)(c.WV.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=M().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let u=o.indexOf(e.currentTarget);o=v.loop?(n=o,r=u+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(u+1)}setTimeout(()=>E(o))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=N}):s})})});T.displayName=C;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=x,S=T},271:function(e,t,n){n.d(t,{VY:function(){return S},aV:function(){return E},fC:function(){return A},xz:function(){return I}});var r=n(2265),o=n(6741),u=n(3966),i=n(1353),a=n(1599),l=n(6840),c=n(9114),s=n(886),f=n(9255),d=n(7437),p="Tabs",[m,v]=(0,u.b)(p,[i.Pc]),h=(0,i.Pc)(),[y,b]=m(p),M=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:u,orientation:i="horizontal",dir:a,activationMode:m="automatic",...v}=e,h=(0,c.gm)(a),[b,M]=(0,s.T)({prop:r,onChange:o,defaultProp:null!=u?u:"",caller:p});return(0,d.jsx)(y,{scope:n,baseId:(0,f.M)(),value:b,onValueChange:M,orientation:i,dir:h,activationMode:m,children:(0,d.jsx)(l.WV.div,{dir:h,"data-orientation":i,...v,ref:t})})});M.displayName=p;var w="TabsList",g=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,u=b(w,n),a=h(n);return(0,d.jsx)(i.fC,{asChild:!0,...a,orientation:u.orientation,dir:u.dir,loop:r,children:(0,d.jsx)(l.WV.div,{role:"tablist","aria-orientation":u.orientation,...o,ref:t})})});g.displayName=w;var N="TabsTrigger",k=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:u=!1,...a}=e,c=b(N,n),s=h(n),f=C(c.baseId,r),p=T(c.baseId,r),m=r===c.value;return(0,d.jsx)(i.ck,{asChild:!0,...s,focusable:!u,active:m,children:(0,d.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":u?"":void 0,disabled:u,id:f,...a,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||u||!e||c.onValueChange(r)})})})});k.displayName=N;var x="TabsContent",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:u,children:i,...c}=e,s=b(x,n),f=C(s.baseId,o),p=T(s.baseId,o),m=o===s.value,v=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(a.z,{present:u||m,children:n=>{let{present:r}=n;return(0,d.jsx)(l.WV.div,{"data-state":m?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&i})}})});function C(e,t){return"".concat(e,"-trigger-").concat(t)}function T(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=x;var A=M,E=g,I=k,S=R},6606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},886:function(e,t,n){n.d(t,{T:function(){return a}});var r,o=n(2265),u=n(1188),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,a,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),a=o.useRef(t);return i(()=>{a.current=t},[t]),o.useEffect(()=>{u.current!==n&&(a.current?.(n),u.current=n)},[n,u]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else a(t)},[c,e,a,l])]}Symbol("RADIX:SYNC_STATE")},1188:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);