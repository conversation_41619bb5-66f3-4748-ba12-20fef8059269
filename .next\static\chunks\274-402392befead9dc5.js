"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{4401:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},2252:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7476:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2660:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3618:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},401:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},875:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},407:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},2135:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},519:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},2735:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},3506:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Microscope",[["path",{d:"M6 18h8",key:"1borvv"}],["path",{d:"M3 22h18",key:"8prr45"}],["path",{d:"M14 22a7 7 0 1 0 0-14h-1",key:"1jwaiy"}],["path",{d:"M9 14h2",key:"197e7h"}],["path",{d:"M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z",key:"1bmzmy"}],["path",{d:"M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3",key:"1drr47"}]])},9397:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3229:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},3247:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8930:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2489:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(9763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9376:function(e,t,n){var r=n(5475);n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},9270:function(e,t,n){n.d(t,{fC:function(){return x},z$:function(){return k}});var r=n(2265),o=n(8575),i=n(3966),l=n(6741),a=n(886),u=n(6718),c=n(420),s=n(1599),d=n(6840),f=n(7437),p="Checkbox",[v,h]=(0,i.b)(p),[m,y]=v(p);function g(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:i,disabled:l,form:u,name:c,onCheckedChange:s,required:d,value:v="on",internal_do_not_use_render:h}=e,[y,g]=(0,a.T)({prop:n,defaultProp:null!=i&&i,onChange:s,caller:p}),[w,b]=r.useState(null),[x,E]=r.useState(null),k=r.useRef(!1),C=!w||!!u||!!w.closest("form"),S={checked:y,disabled:l,setChecked:g,control:w,setControl:b,name:c,form:u,value:v,hasConsumerStoppedPropagationRef:k,required:d,defaultChecked:!R(i)&&i,isFormControl:C,bubbleInput:x,setBubbleInput:E};return(0,f.jsx)(m,{scope:t,...S,children:"function"==typeof h?h(S):o})}var w="CheckboxTrigger",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:i,onClick:a,...u}=e,{control:c,value:s,disabled:p,checked:v,required:h,setControl:m,setChecked:g,hasConsumerStoppedPropagationRef:b,isFormControl:x,bubbleInput:E}=y(w,n),k=(0,o.e)(t,m),C=r.useRef(v);return r.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>g(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,g]),(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":R(v)?"mixed":v,"aria-required":h,"data-state":M(v),"data-disabled":p?"":void 0,disabled:p,value:s,...u,ref:k,onKeyDown:(0,l.M)(i,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(a,e=>{g(e=>!!R(e)||!e),E&&x&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});b.displayName=w;var x=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:l,disabled:a,value:u,onCheckedChange:c,form:s,...d}=e;return(0,f.jsx)(g,{__scopeCheckbox:n,checked:o,defaultChecked:i,disabled:a,required:l,onCheckedChange:c,name:r,form:s,value:u,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(b,{...d,ref:t,__scopeCheckbox:n}),r&&(0,f.jsx)(S,{__scopeCheckbox:n})]})}})});x.displayName=p;var E="CheckboxIndicator",k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=y(E,n);return(0,f.jsx)(s.z,{present:r||R(i.checked)||!0===i.checked,children:(0,f.jsx)(d.WV.span,{"data-state":M(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=E;var C="CheckboxBubbleInput",S=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...i}=e,{control:l,hasConsumerStoppedPropagationRef:a,checked:s,defaultChecked:p,required:v,disabled:h,name:m,value:g,form:w,bubbleInput:b,setBubbleInput:x}=y(C,n),E=(0,o.e)(t,x),k=(0,u.D)(s),S=(0,c.t)(l);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(k!==s&&e){let n=new Event("click",{bubbles:t});b.indeterminate=R(s),e.call(b,!R(s)&&s),b.dispatchEvent(n)}},[b,k,s,a]);let M=r.useRef(!R(s)&&s);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:M.current,required:v,disabled:h,name:m,value:g,form:w,...i,tabIndex:-1,ref:E,style:{...i.style,...S,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function M(e){return R(e)?"indeterminate":e?"checked":"unchecked"}S.displayName=C},8985:function(e,t,n){n.d(t,{Fw:function(){return E},wy:function(){return b},fC:function(){return S}});var r=n(2265);"undefined"!=typeof window&&window.document&&window.document.createElement;var o=n(3966),i=n(886),l=n(1188),a=n(8575),u=n(6840),c=e=>{var t,n;let o,i;let{present:u,children:c}=e,d=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(a.current);c.current="mounted"===d?e:"none"},[d]),(0,l.b)(()=>{let t=a.current,n=u.current;if(n!==e){let r=c.current,o=s(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,l.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=s(a.current).includes(CSS.escape(e.animationName));if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=s(a.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(u),f="function"==typeof c?c({present:d.isPresent}):r.Children.only(c),p=(0,a.e)(d.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof c||d.isPresent?r.cloneElement(f,{ref:p}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}c.displayName="Presence";var d=n(9255),f=n(7437),p="Collapsible",[v,h]=(0,o.b)(p),[m,y]=v(p),g=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:l,disabled:a,onOpenChange:c,...s}=e,[v,h]=(0,i.T)({prop:o,defaultProp:null!=l&&l,onChange:c,caller:p});return(0,f.jsx)(m,{scope:n,disabled:a,contentId:(0,d.M)(),open:v,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),children:(0,f.jsx)(u.WV.div,{"data-state":C(v),"data-disabled":a?"":void 0,...s,ref:t})})});g.displayName=p;var w="CollapsibleTrigger",b=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,o=y(w,n);return(0,f.jsx)(u.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":C(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:function(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}(e.onClick,o.onOpenToggle)})});b.displayName=w;var x="CollapsibleContent",E=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=y(x,e.__scopeCollapsible);return(0,f.jsx)(c,{present:n||o.open,children:e=>{let{present:n}=e;return(0,f.jsx)(k,{...r,ref:t,present:n})}})});E.displayName=x;var k=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:i,...c}=e,s=y(x,n),[d,p]=r.useState(o),v=r.useRef(null),h=(0,a.e)(t,v),m=r.useRef(0),g=m.current,w=r.useRef(0),b=w.current,E=s.open||d,k=r.useRef(E),S=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.b)(()=>{let e=v.current;if(e){S.current=S.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,w.current=t.width,k.current||(e.style.transitionDuration=S.current.transitionDuration,e.style.animationName=S.current.animationName),p(o)}},[s.open,o]),(0,f.jsx)(u.WV.div,{"data-state":C(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!E,...c,ref:h,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":b?"".concat(b,"px"):void 0,...e.style},children:E&&i})});function C(e){return e?"open":"closed"}var S=g},6394:function(e,t,n){n.d(t,{f:function(){return a}});var r=n(2265),o=n(6840),i=n(7437),l=r.forwardRef((e,t)=>(0,i.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},610:function(e,t,n){n.d(t,{fC:function(){return b},z$:function(){return x}});var r=n(2265),o=n(3966),i=n(6840),l=n(7437),a="Progress",[u,c]=(0,o.b)(a),[s,d]=u(a),f=r.forwardRef((e,t)=>{var n,r,o,a;let{__scopeProgress:u,value:c=null,max:d,getValueLabel:f=h,...p}=e;(d||0===d)&&!g(d)&&console.error((n="".concat(d),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let v=g(d)?d:100;null===c||w(c,v)||console.error((o="".concat(c),a="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(a,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=w(c,v)?c:null,x=y(b)?f(b,v):void 0;return(0,l.jsx)(s,{scope:u,value:b,max:v,children:(0,l.jsx)(i.WV.div,{"aria-valuemax":v,"aria-valuemin":0,"aria-valuenow":y(b)?b:void 0,"aria-valuetext":x,role:"progressbar","data-state":m(b,v),"data-value":null!=b?b:void 0,"data-max":v,...p,ref:t})})});f.displayName=a;var p="ProgressIndicator",v=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...o}=e,a=d(p,r);return(0,l.jsx)(i.WV.div,{"data-state":m(a.value,a.max),"data-value":null!==(n=a.value)&&void 0!==n?n:void 0,"data-max":a.max,...o,ref:t})});function h(e,t){return"".concat(Math.round(e/t*100),"%")}function m(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function g(e){return y(e)&&!isNaN(e)&&e>0}function w(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}v.displayName=p;var b=f,x=v},2325:function(e,t,n){n.d(t,{ck:function(){return V},fC:function(){return I},z$:function(){return F}});var r=n(2265),o=n(6741),i=n(8575),l=n(3966),a=n(6840),u=n(1353),c=n(886),s=n(9114),d=n(420),f=n(6718),p=n(1599),v=n(7437),h="Radio",[m,y]=(0,l.b)(h),[g,w]=m(h),b=r.forwardRef((e,t)=>{let{__scopeRadio:n,name:l,checked:u=!1,required:c,disabled:s,value:d="on",onCheck:f,form:p,...h}=e,[m,y]=r.useState(null),w=(0,i.e)(t,e=>y(e)),b=r.useRef(!1),x=!m||p||!!m.closest("form");return(0,v.jsxs)(g,{scope:n,checked:u,disabled:s,children:[(0,v.jsx)(a.WV.button,{type:"button",role:"radio","aria-checked":u,"data-state":C(u),"data-disabled":s?"":void 0,disabled:s,value:d,...h,ref:w,onClick:(0,o.M)(e.onClick,e=>{u||null==f||f(),x&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),x&&(0,v.jsx)(k,{control:m,bubbles:!b.current,name:l,value:d,checked:u,required:c,disabled:s,form:p,style:{transform:"translateX(-100%)"}})]})});b.displayName=h;var x="RadioIndicator",E=r.forwardRef((e,t)=>{let{__scopeRadio:n,forceMount:r,...o}=e,i=w(x,n);return(0,v.jsx)(p.z,{present:r||i.checked,children:(0,v.jsx)(a.WV.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});E.displayName=x;var k=r.forwardRef((e,t)=>{let{__scopeRadio:n,control:o,checked:l,bubbles:u=!0,...c}=e,s=r.useRef(null),p=(0,i.e)(s,t),h=(0,f.D)(l),m=(0,d.t)(o);return r.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==l&&t){let n=new Event("click",{bubbles:u});t.call(e,l),e.dispatchEvent(n)}},[h,l,u]),(0,v.jsx)(a.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:p,style:{...c.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}k.displayName="RadioBubbleInput";var S=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],R="RadioGroup",[M,T]=(0,l.b)(R,[u.Pc,y]),N=(0,u.Pc)(),P=y(),[A,L]=M(R),j=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,name:r,defaultValue:o,value:i,required:l=!1,disabled:d=!1,orientation:f,dir:p,loop:h=!0,onValueChange:m,...y}=e,g=N(n),w=(0,s.gm)(p),[b,x]=(0,c.T)({prop:i,defaultProp:null!=o?o:null,onChange:m,caller:R});return(0,v.jsx)(A,{scope:n,name:r,required:l,disabled:d,value:b,onValueChange:x,children:(0,v.jsx)(u.fC,{asChild:!0,...g,orientation:f,dir:w,loop:h,children:(0,v.jsx)(a.WV.div,{role:"radiogroup","aria-required":l,"aria-orientation":f,"data-disabled":d?"":void 0,dir:w,...y,ref:t})})})});j.displayName=R;var D="RadioGroupItem",O=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,disabled:l,...a}=e,c=L(D,n),s=c.disabled||l,d=N(n),f=P(n),p=r.useRef(null),h=(0,i.e)(t,p),m=c.value===a.value,y=r.useRef(!1);return r.useEffect(()=>{let e=e=>{S.includes(e.key)&&(y.current=!0)},t=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,v.jsx)(u.ck,{asChild:!0,...d,focusable:!s,active:m,children:(0,v.jsx)(b,{disabled:s,required:c.required,checked:m,...f,...a,name:c.name,ref:h,onCheck:()=>c.onValueChange(a.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(a.onFocus,()=>{var e;y.current&&(null===(e=p.current)||void 0===e||e.click())})})})});O.displayName=D;var W=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,...r}=e,o=P(n);return(0,v.jsx)(E,{...o,...r,ref:t})});W.displayName="RadioGroupIndicator";var I=j,V=O,F=W},7864:function(e,t,n){let r;n.d(t,{VY:function(){return rv},ZA:function(){return rm},JO:function(){return rf},ck:function(){return rg},wU:function(){return rb},eT:function(){return rw},__:function(){return ry},h_:function(){return rp},fC:function(){return rc},$G:function(){return rE},u_:function(){return rx},Z0:function(){return rk},xz:function(){return rs},B4:function(){return rd},l_:function(){return rh}});var o,i,l,a,u,c,s,d,f=n(2265),p=n(4887);function v(e,[t,n]){return Math.min(n,Math.max(t,e))}var h=n(6741),m=n(8068),y=n(8575),g=n(3966),w=n(9114),b=n(6840),x=n(6606),E=n(7437),k="dismissableLayer.update",C=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=f.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:a,onInteractOutside:u,onDismiss:c,...d}=e,p=f.useContext(C),[v,m]=f.useState(null),g=null!==(r=null==v?void 0:v.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,w]=f.useState({}),S=(0,y.e)(t,e=>m(e)),T=Array.from(p.layers),[N]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),P=T.indexOf(N),A=v?T.indexOf(v):-1,L=p.layersWithOutsidePointerEventsDisabled.size>0,j=A>=P,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,x.W)(e),o=f.useRef(!1),i=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){M("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));!j||n||(null==l||l(e),null==u||u(e),e.defaultPrevented||null==c||c())},g),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,x.W)(e),o=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!o.current&&M("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...p.branches].some(e=>e.contains(t))||(null==a||a(e),null==u||u(e),e.defaultPrevented||null==c||c())},g);return!function(e,t=globalThis?.document){let n=(0,x.W)(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{A!==p.layers.size-1||(null==i||i(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},g),f.useEffect(()=>{if(v)return o&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(s=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(v)),p.layers.add(v),R(),()=>{o&&1===p.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=s)}},[v,g,o,p]),f.useEffect(()=>()=>{v&&(p.layers.delete(v),p.layersWithOutsidePointerEventsDisabled.delete(v),R())},[v,p]),f.useEffect(()=>{let e=()=>w({});return document.addEventListener(k,e),()=>document.removeEventListener(k,e)},[]),(0,E.jsx)(b.WV.div,{...d,ref:S,style:{pointerEvents:L?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,h.M)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,h.M)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,h.M)(e.onPointerDownCapture,D.onPointerDownCapture)})});function R(){let e=new CustomEvent(k);document.dispatchEvent(e)}function M(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,b.jH)(i,l):i.dispatchEvent(l)}S.displayName="DismissableLayer",f.forwardRef((e,t)=>{let n=f.useContext(C),r=f.useRef(null),o=(0,y.e)(t,r);return f.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,E.jsx)(b.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var T=0;function N(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",L={bubbles:!1,cancelable:!0},j=f.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[a,u]=f.useState(null),c=(0,x.W)(o),s=(0,x.W)(i),d=f.useRef(null),p=(0,y.e)(t,e=>u(e)),v=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let e=function(e){if(v.paused||!a)return;let t=e.target;a.contains(t)?d.current=t:W(d.current,{select:!0})},t=function(e){if(v.paused||!a)return;let t=e.relatedTarget;null===t||a.contains(t)||W(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&W(a)});return a&&n.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,a,v.paused]),f.useEffect(()=>{if(a){I.add(v);let e=document.activeElement;if(!a.contains(e)){let t=new CustomEvent(P,L);a.addEventListener(P,c),a.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(W(r,{select:t}),document.activeElement!==n)return}(D(a).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&W(a))}return()=>{a.removeEventListener(P,c),setTimeout(()=>{let t=new CustomEvent(A,L);a.addEventListener(A,s),a.dispatchEvent(t),t.defaultPrevented||W(null!=e?e:document.body,{select:!0}),a.removeEventListener(A,s),I.remove(v)},0)}}},[a,c,s,v]);let h=f.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=D(e);return[O(t,e),O(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&W(i,{select:!0})):(e.preventDefault(),n&&W(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,E.jsx)(b.WV.div,{tabIndex:-1,...l,ref:p,onKeyDown:h})});function D(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function O(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function W(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}j.displayName="FocusScope";var I=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=V(r,e)).unshift(e)},remove(e){var t;null===(t=(r=V(r,e))[0])||void 0===t||t.resume()}});function V(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=n(9255);let H=["top","right","bottom","left"],B=Math.min,_=Math.max,Z=Math.round,z=Math.floor,U=e=>({x:e,y:e}),K={left:"right",right:"left",bottom:"top",top:"bottom"},q={start:"end",end:"start"};function X(e,t){return"function"==typeof e?e(t):e}function Y(e){return e.split("-")[0]}function $(e){return e.split("-")[1]}function G(e){return"x"===e?"y":"x"}function J(e){return"y"===e?"height":"width"}let Q=new Set(["top","bottom"]);function ee(e){return Q.has(Y(e))?"y":"x"}function et(e){return e.replace(/start|end/g,e=>q[e])}let en=["left","right"],er=["right","left"],eo=["top","bottom"],ei=["bottom","top"];function el(e){return e.replace(/left|right|bottom|top/g,e=>K[e])}function ea(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eu(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ec(e,t,n){let r,{reference:o,floating:i}=e,l=ee(t),a=G(ee(t)),u=J(a),c=Y(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch($(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let es=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=ec(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:y,data:g,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=y?y:d,p={...p,[i]:{...p[i],...g}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=ec(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ed(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=X(t,e),v=ea(p),h=a[f?"floating"===d?"reference":"floating":d],m=eu(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,g=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(g))&&await (null==i.getScale?void 0:i.getScale(g))||{x:1,y:1},b=eu(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:g,strategy:u}):y);return{top:(m.top-b.top+v.top)/w.y,bottom:(b.bottom-m.bottom+v.bottom)/w.y,left:(m.left-b.left+v.left)/w.x,right:(b.right-m.right+v.right)/w.x}}function ef(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ep(e){return H.some(t=>e[t]>=0)}let ev=new Set(["left","top"]);async function eh(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=Y(n),a=$(n),u="y"===ee(n),c=ev.has(l)?-1:1,s=i&&u?-1:1,d=X(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(p="end"===a?-1*v:v),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function em(){return"undefined"!=typeof window}function ey(e){return eb(e)?(e.nodeName||"").toLowerCase():"#document"}function eg(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ew(e){var t;return null==(t=(eb(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eb(e){return!!em()&&(e instanceof Node||e instanceof eg(e).Node)}function ex(e){return!!em()&&(e instanceof Element||e instanceof eg(e).Element)}function eE(e){return!!em()&&(e instanceof HTMLElement||e instanceof eg(e).HTMLElement)}function ek(e){return!!em()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eg(e).ShadowRoot)}let eC=new Set(["inline","contents"]);function eS(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eW(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!eC.has(o)}let eR=new Set(["table","td","th"]),eM=[":popover-open",":modal"];function eT(e){return eM.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eN=["transform","translate","scale","rotate","perspective"],eP=["transform","translate","scale","rotate","perspective","filter"],eA=["paint","layout","strict","content"];function eL(e){let t=ej(),n=ex(e)?eW(e):e;return eN.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eP.some(e=>(n.willChange||"").includes(e))||eA.some(e=>(n.contain||"").includes(e))}function ej(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eD=new Set(["html","body","#document"]);function eO(e){return eD.has(ey(e))}function eW(e){return eg(e).getComputedStyle(e)}function eI(e){return ex(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eV(e){if("html"===ey(e))return e;let t=e.assignedSlot||e.parentNode||ek(e)&&e.host||ew(e);return ek(t)?t.host:t}function eF(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eV(t);return eO(n)?t.ownerDocument?t.ownerDocument.body:t.body:eE(n)&&eS(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=eg(o);if(i){let e=eH(l);return t.concat(l,l.visualViewport||[],eS(o)?o:[],e&&n?eF(e):[])}return t.concat(o,eF(o,[],n))}function eH(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eB(e){let t=eW(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eE(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=Z(n)!==i||Z(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function e_(e){return ex(e)?e:e.contextElement}function eZ(e){let t=e_(e);if(!eE(t))return U(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eB(t),l=(i?Z(n.width):n.width)/r,a=(i?Z(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ez=U(0);function eU(e){let t=eg(e);return ej()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ez}function eK(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=e_(e),a=U(1);t&&(r?ex(r)&&(a=eZ(r)):a=eZ(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eg(l))&&o)?eU(l):U(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=eg(l),t=r&&ex(r)?eg(r):r,n=e,o=eH(n);for(;o&&r&&t!==n;){let e=eZ(o),t=o.getBoundingClientRect(),r=eW(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eH(n=eg(o))}}return eu({width:d,height:f,x:c,y:s})}function eq(e,t){let n=eI(e).scrollLeft;return t?t.left+n:eK(ew(e)).left+n}function eX(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eq(e,r)),y:r.top+t.scrollTop}}let eY=new Set(["absolute","fixed"]);function e$(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eg(e),r=ew(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=ej();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ew(e),n=eI(e),r=e.ownerDocument.body,o=_(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=_(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eq(e),a=-n.scrollTop;return"rtl"===eW(r).direction&&(l+=_(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ew(e));else if(ex(t))r=function(e,t){let n=eK(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eE(e)?eZ(e):U(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eU(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return eu(r)}function eG(e){return"static"===eW(e).position}function eJ(e,t){if(!eE(e)||"fixed"===eW(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ew(e)===n&&(n=n.ownerDocument.body),n}function eQ(e,t){var n;let r=eg(e);if(eT(e))return r;if(!eE(e)){let t=eV(e);for(;t&&!eO(t);){if(ex(t)&&!eG(t))return t;t=eV(t)}return r}let o=eJ(e,t);for(;o&&(n=o,eR.has(ey(n)))&&eG(o);)o=eJ(o,t);return o&&eO(o)&&eG(o)&&!eL(o)?r:o||function(e){let t=eV(e);for(;eE(t)&&!eO(t);){if(eL(t))return t;if(eT(t))break;t=eV(t)}return null}(e)||r}let e0=async function(e){let t=this.getOffsetParent||eQ,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eE(t),o=ew(t),i="fixed"===n,l=eK(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=U(0);if(r||!r&&!i){if(("body"!==ey(t)||eS(o))&&(a=eI(t)),r){let e=eK(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eq(o))}i&&!r&&o&&(u.x=eq(o));let c=!o||r||i?U(0):eX(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e1={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=ew(r),a=!!t&&eT(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=U(1),s=U(0),d=eE(r);if((d||!d&&!i)&&(("body"!==ey(r)||eS(l))&&(u=eI(r)),eE(r))){let e=eK(r);c=eZ(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?U(0):eX(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:ew,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eT(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eF(e,[],!1).filter(e=>ex(e)&&"body"!==ey(e)),o=null,i="fixed"===eW(e).position,l=i?eV(e):e;for(;ex(l)&&!eO(l);){let t=eW(l),n=eL(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eY.has(o.position)||eS(l)&&!n&&function e(t,n){let r=eV(t);return!(r===n||!ex(r)||eO(r))&&("fixed"===eW(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eV(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=e$(t,n,o);return e.top=_(r.top,e.top),e.right=B(r.right,e.right),e.bottom=B(r.bottom,e.bottom),e.left=_(r.left,e.left),e},e$(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eQ,getElementRects:e0,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eB(e);return{width:t,height:n}},getScale:eZ,isElement:ex,isRTL:function(e){return"rtl"===eW(e).direction}};function e2(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e6=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=X(e,t)||{};if(null==c)return{};let d=ea(s),f={x:n,y:r},p=G(ee(o)),v=J(p),h=await l.getDimensions(c),m="y"===p,y=m?"clientHeight":"clientWidth",g=i.reference[v]+i.reference[p]-f[p]-i.floating[v],w=f[p]-i.reference[p],b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),x=b?b[y]:0;x&&await (null==l.isElement?void 0:l.isElement(b))||(x=a.floating[y]||i.floating[v]);let E=x/2-h[v]/2-1,k=B(d[m?"top":"left"],E),C=B(d[m?"bottom":"right"],E),S=x-h[v]-C,R=x/2-h[v]/2+(g/2-w/2),M=_(k,B(R,S)),T=!u.arrow&&null!=$(o)&&R!==M&&i.reference[v]/2-(R<k?k:C)-h[v]/2<0,N=T?R<k?R-k:R-S:0;return{[p]:f[p]+N,data:{[p]:M,centerOffset:R-M-N,...T&&{alignmentOffset:N}},reset:T}}}),e7=(e,t,n)=>{let r=new Map,o={platform:e1,...n},i={...o.platform,_c:r};return es(e,t,{...o,platform:i})};var e3="undefined"!=typeof document?f.useLayoutEffect:function(){};function e8(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e8(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e8(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e5(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e4(e,t){let n=e5(e);return Math.round(t*n)/n}function e9(e){let t=f.useRef(e);return e3(()=>{t.current=e}),t}let te=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e6({element:n.current,padding:r}).fn(t):{}:n?e6({element:n,padding:r}).fn(t):{}}}),tt=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,u=await eh(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}),options:[e,t]}},tn=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=X(n,e),c={x:t,y:r},s=await ed(e,u),d=ee(Y(o)),f=G(d),p=c[f],v=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=_(n,B(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+s[e],r=v-s[t];v=_(n,B(v,r))}let h=a.fn({...e,[f]:p,[d]:v});return{...h,data:{x:h.x-t,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}),options:[e,t]}},tr=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=X(n,e),s={x:t,y:r},d=ee(o),f=G(d),p=s[f],v=s[d],h=X(a,e),m="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var y,g;let e="y"===f?"width":"height",t=ev.has(Y(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(y=l.offset)?void 0:y[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(g=l.offset)?void 0:g[d])||0)-(t?m.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}),options:[e,t]}},to=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:v=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:g=!0,...w}=X(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let b=Y(a),x=ee(s),E=Y(s)===s,k=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=h||(E||!g?[el(s)]:function(e){let t=el(e);return[et(e),t,et(t)]}(s)),S="none"!==y;!h&&S&&C.push(...function(e,t,n,r){let o=$(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?er:en;return t?en:er;case"left":case"right":return t?eo:ei;default:return[]}}(Y(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(et)))),i}(s,g,y,k));let R=[s,...C],M=await ed(e,w),T=[],N=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&T.push(M[b]),v){let e=function(e,t,n){void 0===n&&(n=!1);let r=$(e),o=G(ee(e)),i=J(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=el(l)),[l,el(l)]}(a,c,k);T.push(M[e[0]],M[e[1]])}if(N=[...N,{placement:a,overflows:T}],!T.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=R[e];if(t&&(!("alignment"===v&&x!==ee(t))||N.every(e=>ee(e.placement)!==x||e.overflows[0]>0)))return{data:{index:e,overflows:N},reset:{placement:t}};let n=null==(i=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=N.filter(e=>{if(S){let t=ee(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},ti=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=e,{apply:s=()=>{},...d}=X(n,e),f=await ed(e,d),p=Y(l),v=$(l),h="y"===ee(l),{width:m,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,i=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===v?"top":"bottom");let g=y-f.top-f.bottom,w=m-f.left-f.right,b=B(y-f[o],g),x=B(m-f[i],w),E=!e.middlewareData.shift,k=b,C=x;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(C=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(k=g),E&&!v){let e=_(f.left,0),t=_(f.right,0),n=_(f.top,0),r=_(f.bottom,0);h?C=m-2*(0!==e||0!==t?e+t:_(f.left,f.right)):k=y-2*(0!==n||0!==r?n+r:_(f.top,f.bottom))}await s({...e,availableWidth:C,availableHeight:k});let S=await u.getDimensions(c.floating);return m!==S.width||y!==S.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},tl=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=X(n,e);switch(r){case"referenceHidden":{let n=ef(await ed(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:ep(n)}}}case"escaped":{let n=ef(await ed(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:ep(n)}}}default:return{}}}}),options:[e,t]}},ta=(e,t)=>({...te(e),options:[e,t]});var tu=f.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,E.jsx)(b.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,E.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tu.displayName="Arrow";var tc=n(1188),ts=n(420),td="Popper",[tf,tp]=(0,g.b)(td),[tv,th]=tf(td),tm=e=>{let{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return(0,E.jsx)(tv,{scope:t,anchor:r,onAnchorChange:o,children:n})};tm.displayName=td;var ty="PopperAnchor",tg=f.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=th(ty,n),l=f.useRef(null),a=(0,y.e)(t,l);return f.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,E.jsx)(b.WV.div,{...o,ref:a})});tg.displayName=ty;var tw="PopperContent",[tb,tx]=tf(tw),tE=f.forwardRef((e,t)=>{var n,r,o,i,l,a,u,c;let{__scopePopper:s,side:d="bottom",sideOffset:v=0,align:h="center",alignOffset:m=0,arrowPadding:g=0,avoidCollisions:w=!0,collisionBoundary:k=[],collisionPadding:C=0,sticky:S="partial",hideWhenDetached:R=!1,updatePositionStrategy:M="optimized",onPlaced:T,...N}=e,P=th(tw,s),[A,L]=f.useState(null),j=(0,y.e)(t,e=>L(e)),[D,O]=f.useState(null),W=(0,ts.t)(D),I=null!==(u=null==W?void 0:W.width)&&void 0!==u?u:0,V=null!==(c=null==W?void 0:W.height)&&void 0!==c?c:0,F="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},H=Array.isArray(k)?k:[k],Z=H.length>0,U={padding:F,boundary:H.filter(tR),altBoundary:Z},{refs:K,floatingStyles:q,placement:X,isPositioned:Y,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[s,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=f.useState(r);e8(v,r)||h(r);let[m,y]=f.useState(null),[g,w]=f.useState(null),b=f.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=f.useCallback(e=>{e!==S.current&&(S.current=e,w(e))},[]),E=i||m,k=l||g,C=f.useRef(null),S=f.useRef(null),R=f.useRef(s),M=null!=u,T=e9(u),N=e9(o),P=e9(c),A=f.useCallback(()=>{if(!C.current||!S.current)return;let e={placement:t,strategy:n,middleware:v};N.current&&(e.platform=N.current),e7(C.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};L.current&&!e8(R.current,t)&&(R.current=t,p.flushSync(()=>{d(t)}))})},[v,t,n,N,P]);e3(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let L=f.useRef(!1);e3(()=>(L.current=!0,()=>{L.current=!1}),[]),e3(()=>{if(E&&(C.current=E),k&&(S.current=k),E&&k){if(T.current)return T.current(E,k,A);A()}},[E,k,A,T,M]);let j=f.useMemo(()=>({reference:C,floating:S,setReference:b,setFloating:x}),[b,x]),D=f.useMemo(()=>({reference:E,floating:k}),[E,k]),O=f.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=e4(D.floating,s.x),r=e4(D.floating,s.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...e5(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,D.floating,s.x,s.y]);return f.useMemo(()=>({...s,update:A,refs:j,elements:D,floatingStyles:O}),[s,A,j,D,O])}({strategy:"fixed",placement:d+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=e_(e),d=i||l?[...s?eF(s):[],...eF(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=ew(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let v=z(d),h=z(o.clientWidth-(s+f)),m={rootMargin:-v+"px "+-h+"px "+-z(o.clientHeight-(d+p))+"px "+-z(s)+"px",threshold:_(0,B(1,u))||1},y=!0;function g(t){let r=t[0].intersectionRatio;if(r!==u){if(!y)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e2(c,e.getBoundingClientRect())||l(),y=!1}try{r=new IntersectionObserver(g,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(g,m)}r.observe(e)}(!0),i}(s,n):null,p=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),s&&!c&&v.observe(s),v.observe(t));let h=c?eK(e):null;return c&&function t(){let r=eK(e);h&&!e2(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===M})},elements:{reference:P.anchor},middleware:[tt({mainAxis:v+V,alignmentAxis:m}),w&&tn({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?tr():void 0,...U}),w&&to({...U}),ti({...U,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&ta({element:D,padding:g}),tM({arrowWidth:I,arrowHeight:V}),R&&tl({strategy:"referenceHidden",...U})]}),[G,J]=tT(X),Q=(0,x.W)(T);(0,tc.b)(()=>{Y&&(null==Q||Q())},[Y,Q]);let ee=null===(n=$.arrow)||void 0===n?void 0:n.x,et=null===(r=$.arrow)||void 0===r?void 0:r.y,en=(null===(o=$.arrow)||void 0===o?void 0:o.centerOffset)!==0,[er,eo]=f.useState();return(0,tc.b)(()=>{A&&eo(window.getComputedStyle(A).zIndex)},[A]),(0,E.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:Y?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null===(i=$.transformOrigin)||void 0===i?void 0:i.x,null===(l=$.transformOrigin)||void 0===l?void 0:l.y].join(" "),...(null===(a=$.hide)||void 0===a?void 0:a.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,E.jsx)(tb,{scope:s,placedSide:G,onArrowChange:O,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,E.jsx)(b.WV.div,{"data-side":G,"data-align":J,...N,ref:j,style:{...N.style,animation:Y?void 0:"none"}})})})});tE.displayName=tw;var tk="PopperArrow",tC={top:"bottom",right:"left",bottom:"top",left:"right"},tS=f.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tx(tk,n),i=tC[o.placedSide];return(0,E.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,E.jsx)(tu,{...r,ref:t,style:{...r.style,display:"block"}})})});function tR(e){return null!==e}tS.displayName=tk;var tM=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=tT(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,y=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,g="",w="";return"bottom"===p?(g=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(g=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=s?h:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),w=s?h:"".concat(y,"px")),{data:{x:g,y:w}}}});function tT(e){let[t,n="center"]=e.split("-");return[t,n]}var tN=f.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,a]=f.useState(!1);(0,tc.b)(()=>a(!0),[]);let u=o||l&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return u?p.createPortal((0,E.jsx)(b.WV.div,{...i,ref:t}),u):null});tN.displayName="Portal";var tP=n(7053),tA=n(886),tL=n(6718),tj=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});f.forwardRef((e,t)=>(0,E.jsx)(b.WV.span,{...e,ref:t,style:{...tj,...e.style}})).displayName="VisuallyHidden";var tD=new WeakMap,tO=new WeakMap,tW={},tI=0,tV=function(e){return e&&(e.host||tV(e.parentNode))},tF=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tV(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tW[n]||(tW[n]=new WeakMap);var i=tW[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tD.get(e)||0)+1,c=(i.get(e)||0)+1;tD.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tO.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tI++,function(){l.forEach(function(e){var t=tD.get(e)-1,o=i.get(e)-1;tD.set(e,t),i.set(e,o),t||(tO.has(e)||e.removeAttribute(r),tO.delete(e)),o||e.removeAttribute(n)}),--tI||(tD=new WeakMap,tD=new WeakMap,tO=new WeakMap,tW={})}},tH=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tF(r,o,n,"aria-hidden")):function(){return null}},tB=function(){return(tB=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function t_(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var tZ="right-scroll-bar-position",tz="width-before-scroll-bar";function tU(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tK="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tq=new WeakMap,tX=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=tB({async:!0,ssr:!1},o),u),tY=function(){},t$=f.forwardRef(function(e,t){var n,r,o,i,l=f.useRef(null),a=f.useState({onScrollCapture:tY,onWheelCapture:tY,onTouchMoveCapture:tY}),u=a[0],c=a[1],s=e.forwardProps,d=e.children,p=e.className,v=e.removeScrollBar,h=e.enabled,m=e.shards,y=e.sideCar,g=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,k=e.gapMode,C=t_(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[l,t],r=function(e){return n.forEach(function(t){return tU(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tK(function(){var e=tq.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tU(e,null)}),r.forEach(function(e){t.has(e)||tU(e,o)})}tq.set(i,n)},[n]),i),R=tB(tB({},C),u);return f.createElement(f.Fragment,null,h&&f.createElement(y,{sideCar:tX,removeScrollBar:v,shards:m,noRelative:g,noIsolation:w,inert:b,setCallbacks:c,allowPinchZoom:!!x,lockRef:l,gapMode:k}),s?f.cloneElement(f.Children.only(d),tB(tB({},R),{ref:S})):f.createElement(void 0===E?"div":E,tB({},R,{className:p,ref:S}),d))});t$.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t$.classNames={fullWidth:tz,zeroRight:tZ};var tG=function(e){var t=e.sideCar,n=t_(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,tB({},n))};tG.isSideCarExport=!0;var tJ=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=d||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tQ=function(){var e=tJ();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t0=function(){var e=tQ();return function(t){return e(t.styles,t.dynamic),null}},t1={left:0,top:0,right:0,gap:0},t2=function(e){return parseInt(e||"",10)||0},t6=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t2(n),t2(r),t2(o)]},t7=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t1;var t=t6(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t3=t0(),t8="data-scroll-locked",t5=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t8,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tZ," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tz," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tz," .").concat(tz," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t8,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t4=function(){var e=parseInt(document.body.getAttribute(t8)||"0",10);return isFinite(e)?e:0},t9=function(){f.useEffect(function(){return document.body.setAttribute(t8,(t4()+1).toString()),function(){var e=t4()-1;e<=0?document.body.removeAttribute(t8):document.body.setAttribute(t8,e.toString())}},[])},ne=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t9();var i=f.useMemo(function(){return t7(o)},[o]);return f.createElement(t3,{styles:t5(i,!t,o,n?"":"!important")})},nt=!1;if("undefined"!=typeof window)try{var nn=Object.defineProperty({},"passive",{get:function(){return nt=!0,!0}});window.addEventListener("test",nn,nn),window.removeEventListener("test",nn,nn)}catch(e){nt=!1}var nr=!!nt&&{passive:!1},no=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},ni=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nl(e,r)){var o=na(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nl=function(e,t){return"v"===e?no(t,"overflowY"):no(t,"overflowX")},na=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nu=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=na(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&nl(e,u)&&(f+=m,p+=h);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},nc=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ns=function(e){return[e.deltaX,e.deltaY]},nd=function(e){return e&&"current"in e?e.current:e},nf=0,np=[],nv=(c=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(nf++)[0],i=f.useState(t0)[0],l=f.useRef(e);f.useEffect(function(){l.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nd),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nc(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ni(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ni(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nu(p,t,e,"h"===p?u:c,!0)},[]),u=f.useCallback(function(e){if(np.length&&np[np.length-1]===i){var n="deltaY"in e?ns(e):nc(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(nd).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=f.useCallback(function(e){n.current=nc(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,ns(t),t.target,a(t,e.lockRef.current))},[]),p=f.useCallback(function(t){c(t.type,nc(t),t.target,a(t,e.lockRef.current))},[]);f.useEffect(function(){return np.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,nr),document.addEventListener("touchmove",u,nr),document.addEventListener("touchstart",s,nr),function(){np=np.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,nr),document.removeEventListener("touchmove",u,nr),document.removeEventListener("touchstart",s,nr)}},[]);var v=e.removeScrollBar,h=e.inert;return f.createElement(f.Fragment,null,h?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?f.createElement(ne,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tX.useMedium(c),tG),nh=f.forwardRef(function(e,t){return f.createElement(t$,tB({},e,{ref:t,sideCar:nv}))});nh.classNames=t$.classNames;var nm=[" ","Enter","ArrowUp","ArrowDown"],ny=[" ","Enter"],ng="Select",[nw,nb,nx]=(0,m.B)(ng),[nE,nk]=(0,g.b)(ng,[nx,tp]),nC=tp(),[nS,nR]=nE(ng),[nM,nT]=nE(ng),nN=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:a,onValueChange:u,dir:c,name:s,autoComplete:d,disabled:p,required:v,form:h}=e,m=nC(t),[y,g]=f.useState(null),[b,x]=f.useState(null),[k,C]=f.useState(!1),S=(0,w.gm)(c),[R,M]=(0,tA.T)({prop:r,defaultProp:null!=o&&o,onChange:i,caller:ng}),[T,N]=(0,tA.T)({prop:l,defaultProp:a,onChange:u,caller:ng}),P=f.useRef(null),A=!y||h||!!y.closest("form"),[L,j]=f.useState(new Set),D=Array.from(L).map(e=>e.props.value).join(";");return(0,E.jsx)(tm,{...m,children:(0,E.jsxs)(nS,{required:v,scope:t,trigger:y,onTriggerChange:g,valueNode:b,onValueNodeChange:x,valueNodeHasChildren:k,onValueNodeHasChildrenChange:C,contentId:(0,F.M)(),value:T,onValueChange:N,open:R,onOpenChange:M,dir:S,triggerPointerDownPosRef:P,disabled:p,children:[(0,E.jsx)(nw.Provider,{scope:t,children:(0,E.jsx)(nM,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(e=>{j(t=>new Set(t).add(e))},[]),onNativeOptionRemove:f.useCallback(e=>{j(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),A?(0,E.jsxs)(ri,{"aria-hidden":!0,required:v,tabIndex:-1,name:s,autoComplete:d,value:T,onChange:e=>N(e.target.value),disabled:p,form:h,children:[void 0===T?(0,E.jsx)("option",{value:""}):null,Array.from(L)]},D):null]})})};nN.displayName=ng;var nP="SelectTrigger",nA=f.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nC(n),l=nR(nP,n),a=l.disabled||r,u=(0,y.e)(t,l.onTriggerChange),c=nb(n),s=f.useRef("touch"),[d,p,v]=ra(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=ru(t,e,n);void 0!==r&&l.onValueChange(r.value)}),m=e=>{a||(l.onOpenChange(!0),v()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,E.jsx)(tg,{asChild:!0,...i,children:(0,E.jsx)(b.WV.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":rl(l.value)?"":void 0,...o,ref:u,onClick:(0,h.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==s.current&&m(e)}),onPointerDown:(0,h.M)(o.onPointerDown,e=>{s.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())}),onKeyDown:(0,h.M)(o.onKeyDown,e=>{let t=""!==d.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),(!t||" "!==e.key)&&nm.includes(e.key)&&(m(),e.preventDefault())})})})});nA.displayName=nP;var nL="SelectValue",nj=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nR(nL,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,d=(0,y.e)(t,u.onValueNodeChange);return(0,tc.b)(()=>{c(s)},[c,s]),(0,E.jsx)(b.WV.span,{...a,ref:d,style:{pointerEvents:"none"},children:rl(u.value)?(0,E.jsx)(E.Fragment,{children:l}):i})});nj.displayName=nL;var nD=f.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,E.jsx)(b.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nD.displayName="SelectIcon";var nO=e=>(0,E.jsx)(tN,{asChild:!0,...e});nO.displayName="SelectPortal";var nW="SelectContent",nI=f.forwardRef((e,t)=>{let n=nR(nW,e.__scopeSelect),[r,o]=f.useState();return((0,tc.b)(()=>{o(new DocumentFragment)},[]),n.open)?(0,E.jsx)(nB,{...e,ref:t}):r?p.createPortal((0,E.jsx)(nV,{scope:e.__scopeSelect,children:(0,E.jsx)(nw.Slot,{scope:e.__scopeSelect,children:(0,E.jsx)("div",{children:e.children})})}),r):null});nI.displayName=nW;var[nV,nF]=nE(nW),nH=(0,tP.Z8)("SelectContent.RemoveScroll"),nB=f.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:v,sticky:m,hideWhenDetached:g,avoidCollisions:w,...b}=e,x=nR(nW,n),[k,C]=f.useState(null),[R,M]=f.useState(null),P=(0,y.e)(t,e=>C(e)),[A,L]=f.useState(null),[D,O]=f.useState(null),W=nb(n),[I,V]=f.useState(!1),F=f.useRef(!1);f.useEffect(()=>{if(k)return tH(k)},[k]),f.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:N()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:N()),T++,()=>{1===T&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),T--}},[]);let H=f.useCallback(e=>{let[t,...n]=W().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[W,R]),B=f.useCallback(()=>H([A,k]),[H,A,k]);f.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:_,triggerPointerDownPosRef:Z}=x;f.useEffect(()=>{if(k){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=Z.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=Z.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():k.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[k,_,Z]),f.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[z,U]=ra(e=>{let t=W().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ru(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=f.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==x.value&&x.value===t||r)&&(L(e),r&&(F.current=!0))},[x.value]),q=f.useCallback(()=>null==k?void 0:k.focus(),[k]),X=f.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==x.value&&x.value===t||r)&&O(e)},[x.value]),Y="popper"===r?nZ:n_,$=Y===nZ?{side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:v,sticky:m,hideWhenDetached:g,avoidCollisions:w}:{};return(0,E.jsx)(nV,{scope:n,content:k,viewport:R,onViewportChange:M,itemRefCallback:K,selectedItem:A,onItemLeave:q,itemTextRefCallback:X,focusSelectedItem:B,selectedItemText:D,position:r,isPositioned:I,searchRef:z,children:(0,E.jsx)(nh,{as:nH,allowPinchZoom:!0,children:(0,E.jsx)(j,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,h.M)(o,e=>{var t;null===(t=x.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,E.jsx)(S,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:(0,E.jsx)(Y,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault(),...b,...$,onPlaced:()=>V(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,h.M)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});nB.displayName="SelectContentImpl";var n_=f.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nR(nW,n),l=nF(nW,n),[a,u]=f.useState(null),[c,s]=f.useState(null),d=(0,y.e)(t,e=>s(e)),p=nb(n),h=f.useRef(!1),m=f.useRef(!0),{viewport:g,selectedItem:w,selectedItemText:x,focusSelectedItem:k}=l,C=f.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&c&&g&&w&&x){let e=i.trigger.getBoundingClientRect(),t=c.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,u=e.width+l,c=Math.max(u,t.width),s=v(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.left=s+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,u=e.width+l,c=Math.max(u,t.width),s=v(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.right=s+"px"}let l=p(),u=window.innerHeight-20,s=g.scrollHeight,d=window.getComputedStyle(c),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),y=parseInt(d.borderBottomWidth,10),b=f+m+s+parseInt(d.paddingBottom,10)+y,E=Math.min(5*w.offsetHeight,b),k=window.getComputedStyle(g),C=parseInt(k.paddingTop,10),S=parseInt(k.paddingBottom,10),R=e.top+e.height/2-10,M=w.offsetHeight/2,T=f+m+(w.offsetTop+M);if(T<=R){let e=l.length>0&&w===l[l.length-1].ref.current;a.style.bottom="0px";let t=c.clientHeight-g.offsetTop-g.offsetHeight;a.style.height=T+Math.max(u-R,M+(e?S:0)+t+y)+"px"}else{let e=l.length>0&&w===l[0].ref.current;a.style.top="0px";let t=Math.max(R,f+g.offsetTop+(e?C:0)+M);a.style.height=t+(b-T)+"px",g.scrollTop=T-R+g.offsetTop}a.style.margin="".concat(10,"px 0"),a.style.minHeight=E+"px",a.style.maxHeight=u+"px",null==r||r(),requestAnimationFrame(()=>h.current=!0)}},[p,i.trigger,i.valueNode,a,c,g,w,x,i.dir,r]);(0,tc.b)(()=>C(),[C]);let[S,R]=f.useState();(0,tc.b)(()=>{c&&R(window.getComputedStyle(c).zIndex)},[c]);let M=f.useCallback(e=>{e&&!0===m.current&&(C(),null==k||k(),m.current=!1)},[C,k]);return(0,E.jsx)(nz,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:M,children:(0,E.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,E.jsx)(b.WV.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});n_.displayName="SelectItemAlignedPosition";var nZ=f.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nC(n);return(0,E.jsx)(tE,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nZ.displayName="SelectPopperPosition";var[nz,nU]=nE(nW,{}),nK="SelectViewport",nq=f.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nF(nK,n),l=nU(nK,n),a=(0,y.e)(t,i.onViewportChange),u=f.useRef(0);return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,E.jsx)(nw.Slot,{scope:n,children:(0,E.jsx)(b.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,h.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if((null==r?void 0:r.current)&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nq.displayName=nK;var nX="SelectGroup",[nY,n$]=nE(nX),nG=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,F.M)();return(0,E.jsx)(nY,{scope:n,id:o,children:(0,E.jsx)(b.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nG.displayName=nX;var nJ="SelectLabel",nQ=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=n$(nJ,n);return(0,E.jsx)(b.WV.div,{id:o.id,...r,ref:t})});nQ.displayName=nJ;var n0="SelectItem",[n1,n2]=nE(n0),n6=f.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,a=nR(n0,n),u=nF(n0,n),c=a.value===r,[s,d]=f.useState(null!=i?i:""),[p,v]=f.useState(!1),m=(0,y.e)(t,e=>{var t;return null===(t=u.itemRefCallback)||void 0===t?void 0:t.call(u,e,r,o)}),g=(0,F.M)(),w=f.useRef("touch"),x=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,E.jsx)(n1,{scope:n,value:r,disabled:o,textId:g,isSelected:c,onItemTextChange:f.useCallback(e=>{d(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,E.jsx)(nw.ItemSlot,{scope:n,value:r,disabled:o,textValue:s,children:(0,E.jsx)(b.WV.div,{role:"option","aria-labelledby":g,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:m,onFocus:(0,h.M)(l.onFocus,()=>v(!0)),onBlur:(0,h.M)(l.onBlur,()=>v(!1)),onClick:(0,h.M)(l.onClick,()=>{"mouse"!==w.current&&x()}),onPointerUp:(0,h.M)(l.onPointerUp,()=>{"mouse"===w.current&&x()}),onPointerDown:(0,h.M)(l.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,h.M)(l.onPointerMove,e=>{if(w.current=e.pointerType,o){var t;null===(t=u.onItemLeave)||void 0===t||t.call(u)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,h.M)(l.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=u.onItemLeave)||void 0===t||t.call(u)}}),onKeyDown:(0,h.M)(l.onKeyDown,e=>{var t;(null===(t=u.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(ny.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});n6.displayName=n0;var n7="SelectItemText",n3=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nR(n7,n),a=nF(n7,n),u=n2(n7,n),c=nT(n7,n),[s,d]=f.useState(null),v=(0,y.e)(t,e=>d(e),u.onItemTextChange,e=>{var t;return null===(t=a.itemTextRefCallback)||void 0===t?void 0:t.call(a,e,u.value,u.disabled)}),h=null==s?void 0:s.textContent,m=f.useMemo(()=>(0,E.jsx)("option",{value:u.value,disabled:u.disabled,children:h},u.value),[u.disabled,u.value,h]),{onNativeOptionAdd:g,onNativeOptionRemove:w}=c;return(0,tc.b)(()=>(g(m),()=>w(m)),[g,w,m]),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(b.WV.span,{id:u.textId,...i,ref:v}),u.isSelected&&l.valueNode&&!l.valueNodeHasChildren?p.createPortal(i.children,l.valueNode):null]})});n3.displayName=n7;var n8="SelectItemIndicator",n5=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return n2(n8,n).isSelected?(0,E.jsx)(b.WV.span,{"aria-hidden":!0,...r,ref:t}):null});n5.displayName=n8;var n4="SelectScrollUpButton",n9=f.forwardRef((e,t)=>{let n=nF(n4,e.__scopeSelect),r=nU(n4,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,y.e)(t,r.onScrollButtonChange);return(0,tc.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(rn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n9.displayName=n4;var re="SelectScrollDownButton",rt=f.forwardRef((e,t)=>{let n=nF(re,e.__scopeSelect),r=nU(re,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,y.e)(t,r.onScrollButtonChange);return(0,tc.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(rn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});rt.displayName=re;var rn=f.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nF("SelectScrollButton",n),l=f.useRef(null),a=nb(n),u=f.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return f.useEffect(()=>()=>u(),[u]),(0,tc.b)(()=>{var e;let t=a().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[a]),(0,E.jsx)(b.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,h.M)(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:(0,h.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:(0,h.M)(o.onPointerLeave,()=>{u()})})}),rr=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,E.jsx)(b.WV.div,{"aria-hidden":!0,...r,ref:t})});rr.displayName="SelectSeparator";var ro="SelectArrow";f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nC(n),i=nR(ro,n),l=nF(ro,n);return i.open&&"popper"===l.position?(0,E.jsx)(tS,{...o,...r,ref:t}):null}).displayName=ro;var ri=f.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,i=f.useRef(null),l=(0,y.e)(t,i),a=(0,tL.D)(r);return f.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,E.jsx)(b.WV.select,{...o,style:{...tj,...o.style},ref:l,defaultValue:r})});function rl(e){return""===e||void 0===e}function ra(e){let t=(0,x.W)(e),n=f.useRef(""),r=f.useRef(0),o=f.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=f.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function ru(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}ri.displayName="SelectBubbleInput";var rc=nN,rs=nA,rd=nj,rf=nD,rp=nO,rv=nI,rh=nq,rm=nG,ry=nQ,rg=n6,rw=n3,rb=n5,rx=n9,rE=rt,rk=rr},6718:function(e,t,n){n.d(t,{D:function(){return o}});var r=n(2265);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},420:function(e,t,n){n.d(t,{t:function(){return i}});var r=n(2265),o=n(1188);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);