(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{8872:function(e,s,t){Promise.resolve().then(t.bind(t,694))},1723:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(9763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},694:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return b}});var a=t(7437),l=t(2265),r=t(2381),i=t(9820),n=t(7168),c=t(4972),d=t(1239),o=t(5302),m=t(8906),x=t(91),h=t(5805),f=t(6221),u=t(1723);let j=(0,t(9763).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var g=t(8736),N=t(7829),p=t(7648);function b(){let[e,s]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"assessment-container py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,a.jsx)("div",{className:"p-3 bg-gradient-to-br from-blue-500 to-teal-600 rounded-xl shadow-lg mr-4",children:(0,a.jsx)(c.Z,{className:"h-12 w-12 text-white"})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-2",children:"Psychiatric Assessment System"}),(0,a.jsx)("p",{className:"text-lg text-slate-600",children:"Professional clinical assessment platform"})]})]}),(0,a.jsx)("p",{className:"text-xl text-slate-600 max-w-3xl mx-auto",children:"Fast, reliable, and optimized for ML training data collection with anonymous patient codes"})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8 mb-12",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(d.Z,{className:"h-5 w-5 mr-2 text-yellow-500"}),"Key Features"]})}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(o.Z,{className:"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Lightning Fast"}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Debounced 2-second autosave with optimized performance"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Data Protection"}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Local storage backup prevents data loss"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(x.Z,{className:"h-5 w-5 text-purple-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"ML-Ready Export"}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"CSV and JSON export for machine learning"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(h.Z,{className:"h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Predefined Options"}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Ready-to-click education, occupation, and living options"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(f.Z,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Smart Diagnosis"}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Searchable diagnosis database with filtering"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(u.Z,{className:"h-5 w-5 text-indigo-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Real-time Progress"}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Track completion status across all sections"})]})]})]})]})})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i.Zb,{className:"h-full",children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Get Started"}),(0,a.jsx)(i.SZ,{children:"Begin a new psychiatric assessment session"})]}),(0,a.jsxs)(i.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-teal-50 rounded-lg border border-blue-100",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700",children:"Assessment Sections"}),(0,a.jsx)("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium",children:"7"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Estimated Time"}),(0,a.jsx)("span",{className:"text-sm bg-slate-200 px-2 py-1 rounded",children:"15-30 min"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Auto-save"}),(0,a.jsx)("span",{className:"text-sm bg-green-100 text-green-800 px-2 py-1 rounded",children:"Enabled"})]})]}),(0,a.jsx)(r.z,{onClick:()=>{s(!0),window.location.href="/assessment"},disabled:e,className:"w-full mb-3 bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white shadow-lg",size:"lg",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Starting..."]}):(0,a.jsxs)(a.Fragment,{children:["Start New Assessment",(0,a.jsx)(j,{className:"ml-2 h-4 w-4"})]})}),(0,a.jsx)(p.default,{href:"/patients",children:(0,a.jsxs)(r.z,{variant:"outline",className:"w-full mb-3 border-blue-200 hover:bg-blue-50 hover:border-blue-300",size:"lg",children:[(0,a.jsx)(h.Z,{className:"mr-2 h-4 w-4 text-blue-600"}),"Manage Patients"]})}),(0,a.jsx)(p.default,{href:"/data",children:(0,a.jsxs)(r.z,{variant:"outline",className:"w-full border-teal-200 hover:bg-teal-50 hover:border-teal-300",size:"lg",children:[(0,a.jsx)(x.Z,{className:"mr-2 h-4 w-4 text-teal-600"}),"View Data & Export"]})}),(0,a.jsxs)("div",{className:"text-xs text-slate-500 text-center mt-4 p-2 bg-white/60 rounded",children:[(0,a.jsx)(m.Z,{className:"h-3 w-3 inline mr-1"}),"Anonymous patient codes • Auto-save enabled"]})]})]})})]}),(0,a.jsxs)(i.Zb,{children:[(0,a.jsx)(i.Ol,{children:(0,a.jsxs)(i.ll,{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-5 w-5 mr-2"}),"Assessment Overview"]})}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)(n.mQ,{defaultValue:"sections",className:"w-full",children:[(0,a.jsxs)(n.dr,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(n.SP,{value:"sections",children:"Sections"}),(0,a.jsx)(n.SP,{value:"features",children:"Features"}),(0,a.jsx)(n.SP,{value:"export",children:"Data Export"})]}),(0,a.jsx)(n.nU,{value:"sections",className:"mt-6",children:(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{title:"Demographics",desc:"Patient information and background",icon:h.Z,color:"text-orange-600"},{title:"Symptoms",desc:"DSM-5-TR aligned symptom categories",icon:c.Z,color:"text-purple-600"},{title:"Risk Assessment",desc:"Safety and risk evaluation",icon:m.Z,color:"text-red-600"},{title:"Medical History",desc:"Medical conditions and medications",icon:g.Z,color:"text-blue-600"},{title:"Mental Status Exam",desc:"Current mental state evaluation",icon:f.Z,color:"text-green-600"},{title:"Diagnosis",desc:"Diagnostic formulation and coding",icon:x.Z,color:"text-indigo-600"},{title:"Laboratory Tests",desc:"Lab results and psychological assessments",icon:N.Z,color:"text-teal-600"}].map((e,s)=>(0,a.jsxs)("div",{className:"p-4 border border-slate-200 rounded-lg hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 ".concat(e.color," mr-2")}),(0,a.jsx)("h3",{className:"font-semibold text-slate-800",children:e.title})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:e.desc})]},s))})}),(0,a.jsx)(n.nU,{value:"features",className:"mt-6",children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-3",children:"Performance Optimizations"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Debounced autosave (2 seconds)"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Virtualized symptom selection"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Optimized state management"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Efficient data persistence"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-3",children:"User Experience"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Intuitive form navigation"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Real-time progress tracking"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Predefined dropdown options"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Responsive design"]})]})]})]})})}),(0,a.jsx)(n.nU,{value:"export",className:"mt-6",children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-3",children:"Export Formats"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"CSV format for ML training"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"JSON format for applications"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Structured data format"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Metadata included"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-3",children:"Data Quality"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Validated and clean data"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Consistent formatting"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Complete field coverage"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Timestamp tracking"]})]})]})]})})})]})})]})]})}},2381:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var a=t(7437),l=t(2265),r=t(7053),i=t(535),n=t(3448);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,s)=>{let{className:t,variant:l,size:i,asChild:d=!1,...o}=e,m=d?r.g7:"button";return(0,a.jsx)(m,{className:(0,n.cn)(c({variant:l,size:i,className:t})),ref:s,...o})});d.displayName="Button"},9820:function(e,s,t){"use strict";t.d(s,{Ol:function(){return n},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},ll:function(){return c}});var a=t(7437),l=t(2265),r=t(3448);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});i.displayName="Card";let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...l})});n.displayName="CardHeader";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("h3",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})});c.displayName="CardTitle";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",t),...l})});d.displayName="CardDescription";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",t),...l})});o.displayName="CardContent",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",t),...l})}).displayName="CardFooter"},7168:function(e,s,t){"use strict";t.d(s,{SP:function(){return d},dr:function(){return c},mQ:function(){return n},nU:function(){return o}});var a=t(7437),l=t(2265),r=t(271),i=t(3448);let n=r.fC,c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.aV,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...l})});c.displayName=r.aV.displayName;let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.xz,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...l})});d.displayName=r.xz.displayName;let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.VY,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...l})});o.displayName=r.VY.displayName},3448:function(e,s,t){"use strict";t.d(s,{cn:function(){return r}});var a=t(1994),l=t(3335);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.m6)((0,a.W)(s))}}},function(e){e.O(0,[773,784,971,117,744],function(){return e(e.s=8872)}),_N_E=e.O()}]);