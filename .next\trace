[{"name": "generate-buildid", "duration": 835, "timestamp": 272444211004, "id": 4, "parentId": 1, "tags": {}, "startTime": 1755635998314, "traceId": "ba873912dc554184"}, {"name": "load-custom-routes", "duration": 861, "timestamp": 272444212606, "id": 5, "parentId": 1, "tags": {}, "startTime": 1755635998316, "traceId": "ba873912dc554184"}, {"name": "create-pages-mapping", "duration": 1198, "timestamp": 272444473472, "id": 6, "parentId": 1, "tags": {}, "startTime": 1755635998577, "traceId": "ba873912dc554184"}, {"name": "collect-app-paths", "duration": 10431, "timestamp": 272444474908, "id": 7, "parentId": 1, "tags": {}, "startTime": 1755635998578, "traceId": "ba873912dc554184"}, {"name": "create-app-mapping", "duration": 7600, "timestamp": 272444485394, "id": 8, "parentId": 1, "tags": {}, "startTime": 1755635998588, "traceId": "ba873912dc554184"}, {"name": "public-dir-conflict-check", "duration": 1506, "timestamp": 272444496347, "id": 9, "parentId": 1, "tags": {}, "startTime": 1755635998599, "traceId": "ba873912dc554184"}, {"name": "generate-routes-manifest", "duration": 9155, "timestamp": 272444498263, "id": 10, "parentId": 1, "tags": {}, "startTime": 1755635998601, "traceId": "ba873912dc554184"}, {"name": "create-dist-dir", "duration": 2613, "timestamp": 272444511946, "id": 11, "parentId": 1, "tags": {}, "startTime": 1755635998615, "traceId": "ba873912dc554184"}, {"name": "write-routes-manifest", "duration": 3498, "timestamp": 272444573485, "id": 12, "parentId": 1, "tags": {}, "startTime": 1755635998677, "traceId": "ba873912dc554184"}, {"name": "generate-required-server-files", "duration": 1178, "timestamp": 272444577235, "id": 13, "parentId": 1, "tags": {}, "startTime": 1755635998680, "traceId": "ba873912dc554184"}, {"name": "create-entrypoints", "duration": 292876, "timestamp": 272447618075, "id": 17, "parentId": 15, "tags": {}, "startTime": 1755636001720, "traceId": "ba873912dc554184"}, {"name": "generate-webpack-config", "duration": 2746966, "timestamp": 272447911232, "id": 18, "parentId": 16, "tags": {}, "startTime": 1755636002013, "traceId": "ba873912dc554184"}, {"name": "next-trace-entrypoint-plugin", "duration": 3554, "timestamp": 272450927427, "id": 20, "parentId": 19, "tags": {}, "startTime": 1755636005030, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1429438, "timestamp": 272450946171, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1755636005048, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1429314, "timestamp": 272450946358, "id": 30, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1755636005049, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1429483, "timestamp": 272450946228, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1755636005048, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1461749, "timestamp": 272450945433, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005048, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1460912, "timestamp": 272450946322, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fdata%2Fpage&name=app%2Fdata%2Fpage&pagePath=private-next-app-dir%2Fdata%2Fpage.tsx&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=%2Fdata%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005049, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1460910, "timestamp": 272450946340, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005049, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1462311, "timestamp": 272450946304, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpatients%2Fpage&name=app%2Fpatients%2Fpage&pagePath=private-next-app-dir%2Fpatients%2Fpage.tsx&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=%2Fpatients%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005049, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1867061, "timestamp": 272450946286, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fassessment%2Fpage&name=app%2Fassessment%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fpage.tsx&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=%2Fassessment%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005048, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1966405, "timestamp": 272450946631, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fexport%2Froute&name=app%2Fapi%2Fexport%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexport%2Froute.ts&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=%2Fapi%2Fexport%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005049, "traceId": "ba873912dc554184"}, {"name": "next-swc-transform", "duration": 285711, "timestamp": 272452878137, "id": 34, "parentId": 33, "tags": {}, "startTime": 1755636006980, "traceId": "ba873912dc554184"}, {"name": "next-swc-loader", "duration": 290055, "timestamp": 272452873843, "id": 33, "parentId": 32, "tags": {}, "startTime": 1755636006976, "traceId": "ba873912dc554184"}, {"name": "build-module-ts", "duration": 506641, "timestamp": 272452831192, "id": 32, "parentId": 19, "tags": {"name": "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\assessments\\route.ts", "layer": "rsc"}, "startTime": 1755636006933, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 2399281, "timestamp": 272450946258, "id": 25, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fassessments%2Froute&name=app%2Fapi%2Fassessments%2Froute&pagePath=private-next-app-dir%2Fapi%2Fassessments%2Froute.ts&appDir=C%3A%5CUsers%5C40%5Cprojects%5Cpsychiatric-assessment%5Csrc%5Capp&appPaths=%2Fapi%2Fassessments%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1755636005048, "traceId": "ba873912dc554184"}, {"name": "make", "duration": 3548546, "timestamp": 272450944832, "id": 21, "parentId": 19, "tags": {}, "startTime": 1755636005047, "traceId": "ba873912dc554184"}, {"name": "get-entries", "duration": 10327, "timestamp": 272454497772, "id": 59, "parentId": 58, "tags": {}, "startTime": 1755636008600, "traceId": "ba873912dc554184"}, {"name": "node-file-trace-plugin", "duration": 313710, "timestamp": 272454513081, "id": 60, "parentId": 58, "tags": {"traceEntryCount": "16"}, "startTime": 1755636008615, "traceId": "ba873912dc554184"}, {"name": "collect-traced-files", "duration": 1018, "timestamp": 272454826808, "id": 61, "parentId": 58, "tags": {}, "startTime": 1755636008929, "traceId": "ba873912dc554184"}, {"name": "finish-modules", "duration": 330414, "timestamp": 272454497423, "id": 58, "parentId": 20, "tags": {}, "startTime": 1755636008600, "traceId": "ba873912dc554184"}, {"name": "chunk-graph", "duration": 50041, "timestamp": 272454927787, "id": 63, "parentId": 62, "tags": {}, "startTime": 1755636009030, "traceId": "ba873912dc554184"}, {"name": "optimize-modules", "duration": 82, "timestamp": 272454978188, "id": 65, "parentId": 62, "tags": {}, "startTime": 1755636009080, "traceId": "ba873912dc554184"}, {"name": "optimize-chunks", "duration": 51882, "timestamp": 272454978472, "id": 66, "parentId": 62, "tags": {}, "startTime": 1755636009081, "traceId": "ba873912dc554184"}, {"name": "optimize-tree", "duration": 387, "timestamp": 272455030557, "id": 67, "parentId": 62, "tags": {}, "startTime": 1755636009133, "traceId": "ba873912dc554184"}, {"name": "optimize-chunk-modules", "duration": 56374, "timestamp": 272455031298, "id": 68, "parentId": 62, "tags": {}, "startTime": 1755636009134, "traceId": "ba873912dc554184"}, {"name": "optimize", "duration": 110280, "timestamp": 272454978012, "id": 64, "parentId": 62, "tags": {}, "startTime": 1755636009080, "traceId": "ba873912dc554184"}, {"name": "module-hash", "duration": 55083, "timestamp": 272456578509, "id": 69, "parentId": 62, "tags": {}, "startTime": 1755636010681, "traceId": "ba873912dc554184"}, {"name": "code-generation", "duration": 193329, "timestamp": 272456634099, "id": 70, "parentId": 62, "tags": {}, "startTime": 1755636010736, "traceId": "ba873912dc554184"}, {"name": "hash", "duration": 19985, "timestamp": 272456856887, "id": 71, "parentId": 62, "tags": {}, "startTime": 1755636010959, "traceId": "ba873912dc554184"}, {"name": "code-generation-jobs", "duration": 605, "timestamp": 272456876867, "id": 72, "parentId": 62, "tags": {}, "startTime": 1755636010979, "traceId": "ba873912dc554184"}, {"name": "module-assets", "duration": 573, "timestamp": 272456877276, "id": 73, "parentId": 62, "tags": {}, "startTime": 1755636010979, "traceId": "ba873912dc554184"}, {"name": "create-chunk-assets", "duration": 12276, "timestamp": 272456877876, "id": 74, "parentId": 62, "tags": {}, "startTime": 1755636010980, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 12300, "timestamp": 272456913944, "id": 76, "parentId": 75, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1755636011016, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 8686, "timestamp": 272456917570, "id": 77, "parentId": 75, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1755636011020, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 8618, "timestamp": 272456917640, "id": 78, "parentId": 75, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1755636011020, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 348, "timestamp": 272456925912, "id": 80, "parentId": 75, "tags": {"name": "../app/assessment/page.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 306, "timestamp": 272456925958, "id": 81, "parentId": 75, "tags": {"name": "../app/patients/page.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 289, "timestamp": 272456925977, "id": 82, "parentId": 75, "tags": {"name": "../app/data/page.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 276, "timestamp": 272456925991, "id": 83, "parentId": 75, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 264, "timestamp": 272456926004, "id": 84, "parentId": 75, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 254, "timestamp": 272456926015, "id": 85, "parentId": 75, "tags": {"name": "../app/api/export/route.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 111, "timestamp": 272456926159, "id": 86, "parentId": 75, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 85, "timestamp": 272456926186, "id": 87, "parentId": 75, "tags": {"name": "276.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 72, "timestamp": 272456926202, "id": 88, "parentId": 75, "tags": {"name": "349.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 64, "timestamp": 272456926211, "id": 89, "parentId": 75, "tags": {"name": "566.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 57, "timestamp": 272456926220, "id": 90, "parentId": 75, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 50, "timestamp": 272456926228, "id": 91, "parentId": 75, "tags": {"name": "972.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 43, "timestamp": 272456926235, "id": 92, "parentId": 75, "tags": {"name": "503.js", "cache": "HIT"}, "startTime": 1755636011028, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 3033508, "timestamp": 272456917693, "id": 79, "parentId": 75, "tags": {"name": "../app/api/assessments/route.js", "cache": "MISS"}, "startTime": 1755636011020, "traceId": "ba873912dc554184"}, {"name": "terser-webpack-plugin-optimize", "duration": 3050602, "timestamp": 272456900635, "id": 75, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1755636011003, "traceId": "ba873912dc554184"}, {"name": "css-minimizer-plugin", "duration": 849, "timestamp": 272459951517, "id": 93, "parentId": 19, "tags": {}, "startTime": 1755636014054, "traceId": "ba873912dc554184"}, {"name": "create-trace-assets", "duration": 1920, "timestamp": 272459952882, "id": 94, "parentId": 20, "tags": {}, "startTime": 1755636014055, "traceId": "ba873912dc554184"}, {"name": "seal", "duration": 5092350, "timestamp": 272454879171, "id": 62, "parentId": 19, "tags": {}, "startTime": 1755636008981, "traceId": "ba873912dc554184"}, {"name": "webpack-compilation", "duration": 9062982, "timestamp": 272450920610, "id": 19, "parentId": 16, "tags": {"name": "server"}, "startTime": 1755636005023, "traceId": "ba873912dc554184"}, {"name": "emit", "duration": 52327, "timestamp": 272459985991, "id": 95, "parentId": 16, "tags": {}, "startTime": 1755636014088, "traceId": "ba873912dc554184"}, {"name": "webpack-close", "duration": 236508, "timestamp": 272460040246, "id": 96, "parentId": 16, "tags": {"name": "server"}, "startTime": 1755636014142, "traceId": "ba873912dc554184"}, {"name": "webpack-generate-error-stats", "duration": 15059, "timestamp": 272460276907, "id": 97, "parentId": 96, "tags": {}, "startTime": 1755636014379, "traceId": "ba873912dc554184"}, {"name": "run-webpack-compiler", "duration": 12674663, "timestamp": 272447618069, "id": 16, "parentId": 15, "tags": {}, "startTime": 1755636001720, "traceId": "ba873912dc554184"}, {"name": "format-webpack-messages", "duration": 205, "timestamp": 272460292754, "id": 98, "parentId": 15, "tags": {}, "startTime": 1755636014395, "traceId": "ba873912dc554184"}, {"name": "worker-main-server", "duration": 12675731, "timestamp": 272447617555, "id": 15, "parentId": 1, "tags": {}, "startTime": 1755636001720, "traceId": "ba873912dc554184"}, {"name": "create-entrypoints", "duration": 83143, "timestamp": 272463176936, "id": 102, "parentId": 100, "tags": {}, "startTime": 1755636017280, "traceId": "ba873912dc554184"}, {"name": "generate-webpack-config", "duration": 1250048, "timestamp": 272463260346, "id": 103, "parentId": 101, "tags": {}, "startTime": 1755636017363, "traceId": "ba873912dc554184"}, {"name": "make", "duration": 2867, "timestamp": 272464868012, "id": 105, "parentId": 104, "tags": {}, "startTime": 1755636018971, "traceId": "ba873912dc554184"}, {"name": "chunk-graph", "duration": 3236, "timestamp": 272464880578, "id": 107, "parentId": 106, "tags": {}, "startTime": 1755636018983, "traceId": "ba873912dc554184"}, {"name": "optimize-modules", "duration": 163, "timestamp": 272464884321, "id": 109, "parentId": 106, "tags": {}, "startTime": 1755636018987, "traceId": "ba873912dc554184"}, {"name": "optimize-chunks", "duration": 2569, "timestamp": 272464884835, "id": 110, "parentId": 106, "tags": {}, "startTime": 1755636018987, "traceId": "ba873912dc554184"}, {"name": "optimize-tree", "duration": 261, "timestamp": 272464887562, "id": 111, "parentId": 106, "tags": {}, "startTime": 1755636018990, "traceId": "ba873912dc554184"}, {"name": "optimize-chunk-modules", "duration": 1166, "timestamp": 272464888303, "id": 112, "parentId": 106, "tags": {}, "startTime": 1755636018991, "traceId": "ba873912dc554184"}, {"name": "optimize", "duration": 5600, "timestamp": 272464884125, "id": 108, "parentId": 106, "tags": {}, "startTime": 1755636018987, "traceId": "ba873912dc554184"}, {"name": "module-hash", "duration": 305, "timestamp": 272464891911, "id": 113, "parentId": 106, "tags": {}, "startTime": 1755636018995, "traceId": "ba873912dc554184"}, {"name": "code-generation", "duration": 805, "timestamp": 272464892377, "id": 114, "parentId": 106, "tags": {}, "startTime": 1755636018995, "traceId": "ba873912dc554184"}, {"name": "hash", "duration": 4294, "timestamp": 272464893927, "id": 115, "parentId": 106, "tags": {}, "startTime": 1755636018997, "traceId": "ba873912dc554184"}, {"name": "code-generation-jobs", "duration": 686, "timestamp": 272464898197, "id": 116, "parentId": 106, "tags": {}, "startTime": 1755636019001, "traceId": "ba873912dc554184"}, {"name": "module-assets", "duration": 231, "timestamp": 272464898794, "id": 117, "parentId": 106, "tags": {}, "startTime": 1755636019001, "traceId": "ba873912dc554184"}, {"name": "create-chunk-assets", "duration": 570, "timestamp": 272464899050, "id": 118, "parentId": 106, "tags": {}, "startTime": 1755636019002, "traceId": "ba873912dc554184"}, {"name": "minify-js", "duration": 448, "timestamp": 272465056638, "id": 120, "parentId": 119, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1755636019159, "traceId": "ba873912dc554184"}, {"name": "terser-webpack-plugin-optimize", "duration": 147014, "timestamp": 272464910086, "id": 119, "parentId": 104, "tags": {"compilationName": "edge-server", "swcMinify": true}, "startTime": 1755636019013, "traceId": "ba873912dc554184"}, {"name": "css-minimizer-plugin", "duration": 200, "timestamp": 272465057240, "id": 121, "parentId": 104, "tags": {}, "startTime": 1755636019160, "traceId": "ba873912dc554184"}, {"name": "seal", "duration": 189373, "timestamp": 272464875920, "id": 106, "parentId": 104, "tags": {}, "startTime": 1755636018979, "traceId": "ba873912dc554184"}, {"name": "webpack-compilation", "duration": 227034, "timestamp": 272464839039, "id": 104, "parentId": 101, "tags": {"name": "edge-server"}, "startTime": 1755636018942, "traceId": "ba873912dc554184"}, {"name": "emit", "duration": 5923, "timestamp": 272465067664, "id": 122, "parentId": 101, "tags": {}, "startTime": 1755636019170, "traceId": "ba873912dc554184"}, {"name": "webpack-close", "duration": 1205, "timestamp": 272465074832, "id": 123, "parentId": 101, "tags": {"name": "edge-server"}, "startTime": 1755636019177, "traceId": "ba873912dc554184"}, {"name": "webpack-generate-error-stats", "duration": 10404, "timestamp": 272465076121, "id": 124, "parentId": 123, "tags": {}, "startTime": 1755636019179, "traceId": "ba873912dc554184"}, {"name": "run-webpack-compiler", "duration": 1909868, "timestamp": 272463176929, "id": 101, "parentId": 100, "tags": {}, "startTime": 1755636017280, "traceId": "ba873912dc554184"}, {"name": "format-webpack-messages", "duration": 130, "timestamp": 272465086807, "id": 125, "parentId": 100, "tags": {}, "startTime": 1755636019189, "traceId": "ba873912dc554184"}, {"name": "worker-main-edge-server", "duration": 1910649, "timestamp": 272463176435, "id": 100, "parentId": 1, "tags": {}, "startTime": 1755636017279, "traceId": "ba873912dc554184"}, {"name": "create-entrypoints", "duration": 134003, "timestamp": 272466879505, "id": 128, "parentId": 126, "tags": {}, "startTime": 1755636020983, "traceId": "ba873912dc554184"}, {"name": "generate-webpack-config", "duration": 1338278, "timestamp": 272467014336, "id": 129, "parentId": 127, "tags": {}, "startTime": 1755636021117, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1859310, "timestamp": 272468587241, "id": 135, "parentId": 131, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1755636022690, "traceId": "ba873912dc554184"}, {"name": "add-entry", "duration": 1903014, "timestamp": 272468587378, "id": 136, "parentId": 131, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1755636022690, "traceId": "ba873912dc554184"}]