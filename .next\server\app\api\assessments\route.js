"use strict";(()=>{var e={};e.id=388,e.ids=[388],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5476:(e,s,t)=>{t.r(s),t.d(s,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>p,staticGenerationAsyncStorage:()=>u});var a={};t.r(a),t.d(a,{GET:()=>c,POST:()=>l});var i=t(9303),o=t(8716),r=t(670),n=t(7070),d=t(8194);async function l(e){try{let s;let t=await e.json();if(!t||"object"!=typeof t)return n.NextResponse.json({error:"Invalid request body",details:"Request body must be a valid JSON object"},{status:400});if(s=t.assessmentId?await d.db.assessment.update({where:{id:t.assessmentId},data:{assessorName:t.assessorName||"Anonymous",status:t.status||"in_progress",updatedAt:new Date}}):await d.db.assessment.create({data:{assessorName:t.assessorName||"Anonymous",status:"in_progress"}}),t.demographics&&Object.keys(t.demographics).length>0){let e={...t.demographics};if(void 0!==e.dateOfBirth){let s=e.dateOfBirth;if(null===s||"string"==typeof s&&""===s.trim())delete e.dateOfBirth;else if("string"==typeof s){if(/^\d{4}-\d{2}-\d{2}$/.test(s))e.dateOfBirth=new Date(s);else{let t=new Date(s);isNaN(t.getTime())?delete e.dateOfBirth:e.dateOfBirth=t}}}await d.db.demographics.upsert({where:{assessmentId:s.id},update:e,create:{assessmentId:s.id,...e}})}if(t.riskAssessment&&Object.keys(t.riskAssessment).length>0&&await d.db.riskAssessment.create({data:{assessmentId:s.id,...t.riskAssessment}}),t.medicalHistory&&Object.keys(t.medicalHistory).length>0){let e={...t.medicalHistory};e.structuredMedicalConditions&&(e.structuredMedicalConditions=JSON.stringify(e.structuredMedicalConditions)),e.substanceUseHistory&&(e.substanceUseHistory=JSON.stringify(e.substanceUseHistory));let{psychiatricEpisodes:a,medicationHistory:i,testsData:o,...r}=e;if(await d.db.medicalHistory.upsert({where:{assessmentId:s.id},update:r,create:{assessmentId:s.id,...r}}),t.medicalHistory.psychiatricEpisodes&&Array.isArray(t.medicalHistory.psychiatricEpisodes))for(let e of(await d.db.psychiatricEpisode.deleteMany({where:{assessmentId:s.id}}),t.medicalHistory.psychiatricEpisodes))await d.db.psychiatricEpisode.create({data:{assessmentId:s.id,episodeType:e.episodeType,duration:e.duration,durationUnit:e.durationUnit,startDate:e.startDate,endDate:e.endDate,severity:e.severity,treatmentReceived:JSON.stringify(e.treatmentReceived||[]),treatmentResponse:e.treatmentResponse,notes:e.notes}});if(t.medicalHistory.medicationHistory&&Array.isArray(t.medicalHistory.medicationHistory))for(let e of(await d.db.medicationHistory.deleteMany({where:{assessmentId:s.id}}),t.medicalHistory.medicationHistory))await d.db.medicationHistory.create({data:{assessmentId:s.id,medicationName:e.medicationName,category:e.category,dosage:e.dosage,startDate:e.startDate,endDate:e.endDate,effectiveness:e.effectiveness,sideEffects:e.sideEffects,discontinuationReason:e.discontinuationReason,discontinuationOther:e.discontinuationOther,notes:e.notes}});if(t.medicalHistory.testsData){let e=t.medicalHistory.testsData;if(await d.db.laboratoryTest.deleteMany({where:{assessmentId:s.id}}),await d.db.psychologicalAssessment.deleteMany({where:{assessmentId:s.id}}),e.testResults&&Array.isArray(e.testResults))for(let t of e.testResults)"Psychological Assessment"===t.category?await d.db.psychologicalAssessment.create({data:{assessmentId:s.id,testName:t.testName,datePerformed:t.datePerformed,score:t.result,interpretation:t.normalRange,notes:t.notes}}):await d.db.laboratoryTest.create({data:{assessmentId:s.id,testName:t.testName,category:t.category,datePerformed:t.datePerformed,result:t.result,normalRange:t.normalRange,notes:t.notes}})}}if(t.mentalStatusExam&&Object.keys(t.mentalStatusExam).length>0&&await d.db.mentalStatusExam.create({data:{assessmentId:s.id,...t.mentalStatusExam}}),t.symptoms){let e=Array.isArray(t.symptoms.selectedSymptoms)?Array.from(new Set(t.symptoms.selectedSymptoms.filter(e=>e&&"string"==typeof e&&e.trim().length>0).map(e=>e.trim()))):[];console.log(`Processing ${e.length} unique symptoms for assessment ${s.id}`),await d.db.$transaction(async a=>{if(await a.symptomAssessment.deleteMany({where:{assessmentId:s.id}}),0===e.length)return;let i=await a.symptom.findMany({where:{name:{in:e}}}),o=new Map(i.map(e=>[e.name,e])),r=e.filter(e=>!o.has(e));r.length>0&&(await a.symptom.createMany({data:r.map(e=>({name:e,category:"Other",description:""}))}),(await a.symptom.findMany({where:{name:{in:r}}})).forEach(e=>o.set(e.name,e)));let n=e.map(e=>{let a=o.get(e),i=t.symptoms.symptomDetails?.[e]||{};return{assessmentId:s.id,symptomId:a.id,severity:i.severity||null,duration:i.duration||null,frequency:i.frequency||null,notes:i.notes||null}});n.length>0&&await a.symptomAssessment.createMany({data:n})})}if(t.diagnosis&&(console.log(`Processing diagnosis data for assessment ${s.id}:`,JSON.stringify(t.diagnosis,null,2)),await d.db.$transaction(async e=>{await e.diagnosisAssessment.deleteMany({where:{assessmentId:s.id}});let a=[];if(t.diagnosis.primaryDiagnosis&&t.diagnosis.primaryDiagnosisCode){let e=t.diagnosis.primaryDiagnosisCode.trim(),s=t.diagnosis.primaryDiagnosis.trim();e&&s&&a.push({code:e,name:s,type:"primary",confidence:"definite"})}t.diagnosis.secondaryDiagnoses&&Array.isArray(t.diagnosis.secondaryDiagnoses)&&t.diagnosis.secondaryDiagnoses.filter(e=>e&&e.diagnosis&&e.code).filter(e=>e.diagnosis.trim()&&e.code.trim()).forEach(e=>{a.push({code:e.code.trim(),name:e.diagnosis.trim(),type:e.type||"secondary",confidence:"probable"})});let i=a.filter((e,s,t)=>t.findIndex(s=>s.code===e.code)===s);if(0===i.length)return;let o=await e.diagnosis.findMany({where:{code:{in:i.map(e=>e.code)}}}),r=new Map(o.map(e=>[e.code,e])),n=i.filter(e=>!r.has(e.code));n.length>0&&(await e.diagnosis.createMany({data:n.map(e=>({code:e.code,name:e.name,category:"Other",description:""}))}),(await e.diagnosis.findMany({where:{code:{in:n.map(e=>e.code)}}})).forEach(e=>r.set(e.code,e)));let d=i.map(e=>{let t=r.get(e.code);return{assessmentId:s.id,diagnosisId:t.id,type:e.type,confidence:e.confidence}});d.length>0&&await e.diagnosisAssessment.createMany({data:d}),console.log(`Successfully processed ${i.length} diagnoses in batch`)})),t.laboratoryTests){if(await d.db.laboratoryTest.deleteMany({where:{assessmentId:s.id}}),await d.db.psychologicalAssessment.deleteMany({where:{assessmentId:s.id}}),await d.db.bloodTestComponent.deleteMany({where:{assessmentId:s.id}}),await d.db.bloodTestNote.deleteMany({where:{assessmentId:s.id}}),await d.db.imagingStudy.deleteMany({where:{assessmentId:s.id}}),await d.db.neurologicalTest.deleteMany({where:{assessmentId:s.id}}),t.laboratoryTests.bloodTestComponents)for(let[e,a]of Object.entries(t.laboratoryTests.bloodTestComponents))for(let[t,i]of Object.entries(a))i.value&&await d.db.bloodTestComponent.create({data:{assessmentId:s.id,testName:e,componentName:t,value:i.value,unit:i.unit||"",normalRange:i.normalRange||"",notes:i.notes||null}});if(t.laboratoryTests.bloodTestNotes)for(let[e,a]of Object.entries(t.laboratoryTests.bloodTestNotes))a&&"string"==typeof a&&a.trim()&&await d.db.bloodTestNote.create({data:{assessmentId:s.id,testName:e,notes:a.trim()}});if(t.laboratoryTests.psychologicalAssessments)for(let[e,a]of Object.entries(t.laboratoryTests.psychologicalAssessments))await d.db.psychologicalAssessment.create({data:{assessmentId:s.id,testName:e,responses:JSON.stringify(a.responses||{}),totalScore:a.totalScore||0,interpretation:a.interpretation||"",isCompleted:a.isCompleted||!1,notes:a.notes||null}});if(t.laboratoryTests.imagingStudies)for(let[e,a]of Object.entries(t.laboratoryTests.imagingStudies))(a.findings||a.impression||a.recommendations)&&await d.db.imagingStudy.create({data:{assessmentId:s.id,testName:e,findings:a.findings||null,impression:a.impression||null,recommendations:a.recommendations||null,notes:a.notes||null}});if(t.laboratoryTests.neurologicalTests)for(let[e,a]of Object.entries(t.laboratoryTests.neurologicalTests))(a.findings||a.interpretation||a.recommendations)&&await d.db.neurologicalTest.create({data:{assessmentId:s.id,testName:e,findings:a.findings||null,interpretation:a.interpretation||null,recommendations:a.recommendations||null,notes:a.notes||null}});if(t.laboratoryTests.testResults&&Array.isArray(t.laboratoryTests.testResults))for(let e of t.laboratoryTests.testResults)"Psychological Assessments"===e.category?await d.db.psychologicalAssessment.create({data:{assessmentId:s.id,testName:e.testName,datePerformed:e.datePerformed,score:e.resultValue,interpretation:e.interpretation,notes:e.notes}}):await d.db.laboratoryTest.create({data:{assessmentId:s.id,testName:e.testName,category:e.category,datePerformed:e.datePerformed,result:e.resultValue,normalRange:e.normalRange,notes:e.notes}});if(t.laboratoryTests.psychologicalTests?.psychologicalAssessments)for(let[e,a]of Object.entries(t.laboratoryTests.psychologicalTests.psychologicalAssessments))(a.testName||a.totalScore||a.interpretation||a.notes)&&await d.db.psychologicalAssessment.create({data:{assessmentId:s.id,testName:a.testName||e,datePerformed:a.datePerformed||null,totalScore:a.totalScore||null,score:a.score||null,interpretation:a.interpretation||null,notes:a.notes||null,responses:a.responses?JSON.stringify(a.responses):null,subscaleScores:a.subscaleScores||null,isCompleted:a.isCompleted||!1}});if(t.laboratoryTests.imagingAndNeurological){if(t.laboratoryTests.imagingAndNeurological.imagingStudies)for(let[e,a]of Object.entries(t.laboratoryTests.imagingAndNeurological.imagingStudies))(a.testName||a.findings||a.impression||a.notes)&&await d.db.imagingStudy.create({data:{assessmentId:s.id,testName:a.testName||e,category:a.category||null,datePerformed:a.datePerformed||null,facility:a.facility||null,findings:a.findings||null,impression:a.impression||null,notes:a.notes||null,isNormal:a.isNormal||!0,isUrgent:a.isUrgent||!1,followUpRequired:a.followUpRequired||!1}});if(t.laboratoryTests.imagingAndNeurological.neurologicalTests)for(let[e,a]of Object.entries(t.laboratoryTests.imagingAndNeurological.neurologicalTests))(a.testName||a.results||a.interpretation||a.notes)&&await d.db.neurologicalTest.create({data:{assessmentId:s.id,testName:a.testName||e,category:a.category||null,datePerformed:a.datePerformed||null,facility:a.facility||null,results:a.results||null,interpretation:a.interpretation||null,notes:a.notes||null,isNormal:a.isNormal||!0,followUpRequired:a.followUpRequired||!1}})}if(t.laboratoryTests.toxicologyScreen){let e=t.laboratoryTests.toxicologyScreen;(e.substances||e.comments||e.datePerformed)&&(await d.db.toxicologyScreen.deleteMany({where:{assessmentId:s.id}}),await d.db.toxicologyScreen.create({data:{assessmentId:s.id,datePerformed:e.datePerformed||null,cannabis:e.substances?.cannabis?"Positive":"Negative",cocaine:e.substances?.cocaine?"Positive":"Negative",amphetamines:e.substances?.amphetamines?"Positive":"Negative",opiates:e.substances?.opiates?"Positive":"Negative",oxycodone:e.substances?.oxycodone?"Positive":"Negative",benzodiazepines:e.substances?.benzodiazepines?"Positive":"Negative",barbiturates:e.substances?.barbiturates?"Positive":"Negative",pcp:e.substances?.pcp?"Positive":"Negative",alcohol:e.substances?.alcohol?"Positive":"Negative",syntheticDrugs:e.substances?.syntheticDrugs?"Positive":"Negative",prescriptionDrugs:e.substances?.prescriptionDrugs?"Positive":"Negative",notes:e.comments||null}}))}}return n.NextResponse.json({success:!0,assessmentId:s.id})}catch(t){console.error("Error saving assessment:",t);let e="Failed to save assessment",s=500;return t instanceof Error&&(t.message.includes("Unique constraint")?(e="Assessment with this ID already exists",s=409):t.message.includes("Foreign key constraint")?(e="Invalid reference data provided",s=400):t.message.includes("Required field")&&(e="Missing required field",s=400)),n.NextResponse.json({error:e,details:void 0},{status:s})}}async function c(e){try{let{searchParams:s}=new URL(e.url),t=s.get("id");if(t){let e=await d.db.assessment.findUnique({where:{id:t},include:{demographics:!0,riskAssessment:!0,medicalHistory:!0,mentalStatusExam:!0,symptoms:{include:{symptom:!0}},diagnoses:{include:{diagnosis:!0}},psychiatricEpisodes:!0,medicationHistory:!0,laboratoryTests:!0,psychologicalAssessments:!0,bloodTestComponents:!0,bloodTestNotes:!0,imagingStudies:!0,neurologicalTests:!0,thyroidFunction:!0,vitaminLevels:!0,liverFunction:!0,completeBloodCount:!0,metabolicPanel:!0,lipidPanel:!0,inflammatoryMarkers:!0,hormonalTests:!0,toxicologyScreen:!0}});if(!e)return n.NextResponse.json({error:"Assessment not found"},{status:404});if(e.medicalHistory){if(e.medicalHistory.structuredMedicalConditions)try{e.medicalHistory.structuredMedicalConditions=JSON.parse(e.medicalHistory.structuredMedicalConditions)}catch(s){e.medicalHistory.structuredMedicalConditions={}}if(e.medicalHistory.substanceUseHistory)try{e.medicalHistory.substanceUseHistory=JSON.parse(e.medicalHistory.substanceUseHistory)}catch(s){e.medicalHistory.substanceUseHistory=[]}let s=e.medicalHistory;s.psychiatricEpisodes=e.psychiatricEpisodes?.map(e=>({...e,treatmentReceived:e.treatmentReceived?JSON.parse(e.treatmentReceived):[]}))||[],s.medicationHistory=e.medicationHistory||[];let t=[...e.laboratoryTests?.map(e=>({testName:e.testName,category:e.category,datePerformed:e.datePerformed,result:e.result,normalRange:e.normalRange,notes:e.notes}))||[],...e.psychologicalAssessments?.map(e=>({testName:e.testName,category:"Psychological Assessment",datePerformed:e.datePerformed,result:e.score,normalRange:e.interpretation,notes:e.notes}))||[]],a={psychologicalAssessments:{}};e.psychologicalAssessments?.forEach(e=>{a.psychologicalAssessments[e.testName]={testName:e.testName,datePerformed:e.datePerformed,totalScore:e.totalScore,score:e.score,interpretation:e.interpretation,notes:e.notes,responses:e.responses?JSON.parse(e.responses):{},subscaleScores:e.subscaleScores,isCompleted:e.isCompleted||!1}});let i={imagingStudies:{},neurologicalTests:{}};e.imagingStudies?.forEach(e=>{i.imagingStudies[e.testName]={testName:e.testName,category:e.category||null,datePerformed:e.datePerformed,facility:e.facility||null,findings:e.findings,impression:e.impression,notes:e.notes,isNormal:e.isNormal||!0,isUrgent:e.isUrgent||!1,followUpRequired:e.followUpRequired||!1}}),e.neurologicalTests?.forEach(e=>{i.neurologicalTests[e.testName]={testName:e.testName,category:e.category||null,datePerformed:e.datePerformed,facility:e.facility||null,results:e.results||null,interpretation:e.interpretation,notes:e.notes,isNormal:e.isNormal||!0,followUpRequired:e.followUpRequired||!1}});let o={};if(e.toxicologyScreen&&Array.isArray(e.toxicologyScreen)&&e.toxicologyScreen.length>0){let s=e.toxicologyScreen[0];o={datePerformed:s.datePerformed,substances:{cannabis:"Positive"===s.cannabis,cocaine:"Positive"===s.cocaine,amphetamines:"Positive"===s.amphetamines,opiates:"Positive"===s.opiates,oxycodone:"Positive"===s.oxycodone,benzodiazepines:"Positive"===s.benzodiazepines,barbiturates:"Positive"===s.barbiturates,pcp:"Positive"===s.pcp,alcohol:"Positive"===s.alcohol,syntheticDrugs:"Positive"===s.syntheticDrugs,prescriptionDrugs:"Positive"===s.prescriptionDrugs},comments:s.notes||""}}s.testsData={laboratoryTests:{},psychologicalTests:a,imagingAndNeurological:i,toxicologyScreen:o,testResults:t}}return n.NextResponse.json(e)}{let e=await d.db.assessment.findMany({include:{demographics:!0,_count:{select:{symptoms:!0,diagnoses:!0}}},orderBy:{createdAt:"desc"}});return n.NextResponse.json(e)}}catch(e){return console.error("Error fetching assessments:",e),n.NextResponse.json({error:"Failed to fetch assessments"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/assessments/route",pathname:"/api/assessments",filename:"route",bundlePath:"app/api/assessments/route"},resolvedPagePath:"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\assessments\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:u,serverHooks:p}=m,g="/api/assessments/route";function f(){return(0,r.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:u})}},8194:(e,s,t)=>{t.d(s,{db:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient({log:["query"]})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,972],()=>t(5476));module.exports=a})();