"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { TestTube, Microscope, Brain, Activity } from "lucide-react"
import BloodWorkPanel from "./BloodWorkPanel"
import ToxicologyPanel from "./SimpleToxicologyPanel"
import PsychologicalTestsPanel, { PsychologicalTestsData } from "./PsychologicalTestsPanel"
import ImagingStudiesPanel, { ImagingStudiesData } from "./ImagingStudiesPanel"

// Enhanced hierarchical test structure with conditional displays
const TEST_CATEGORIES = {
  "Blood Tests": [
    "CBC (Complete Blood Count)",
    "Comprehensive Metabolic Panel",
    "Lipid Panel",
    "TSH (Thyroid Stimulating Hormone)",
    "T3 (Triiodothyronine)",
    "T4 (Thyroxine)",
    "Vitamin B12",
    "Vitamin D",
    "Folate"
  ],
  "Imaging Studies": [
    "MRI Brain",
    "CT Head",
    "PET Scan",
    "X-Ray Chest"
  ],
  "Psychological Assessments": [
    "MMSE (Mini-Mental State Exam)",
    "PHQ-9 (Patient Health Questionnaire)",
    "GAD-7 (Generalized Anxiety Disorder Scale)",
    "AUDIT (Alcohol Use Disorders Test)",
    "Beck Depression Inventory"
  ],
  "Neurological Tests": [
    "EEG (Electroencephalogram)",
    "EMG (Electromyography)",
    "Nerve Conduction Studies"
  ]
}

// Blood test component values for all tests
const BLOOD_TEST_COMPONENTS = {
  "CBC (Complete Blood Count)": [
    { name: "WBC", label: "White Blood Cells", unit: "K/μL", normalRange: "4.5-11.0" },
    { name: "Hemoglobin", label: "Hemoglobin", unit: "g/dL", normalRange: "12.0-16.0" },
    { name: "Platelets", label: "Platelets", unit: "K/μL", normalRange: "150-450" },
    { name: "Neutrophils", label: "Neutrophils", unit: "%", normalRange: "50-70" },
    { name: "Lymphocytes", label: "Lymphocytes", unit: "%", normalRange: "20-40" },
    { name: "Monocytes", label: "Monocytes", unit: "%", normalRange: "2-8" },
    { name: "Eosinophils", label: "Eosinophils", unit: "%", normalRange: "1-4" },
    { name: "Basophils", label: "Basophils", unit: "%", normalRange: "0-2" }
  ],
  "Comprehensive Metabolic Panel": [
    { name: "Glucose", label: "Glucose", unit: "mg/dL", normalRange: "70-100" },
    { name: "BUN", label: "Blood Urea Nitrogen", unit: "mg/dL", normalRange: "7-20" },
    { name: "Creatinine", label: "Creatinine", unit: "mg/dL", normalRange: "0.6-1.2" },
    { name: "Sodium", label: "Sodium", unit: "mEq/L", normalRange: "136-145" },
    { name: "Potassium", label: "Potassium", unit: "mEq/L", normalRange: "3.5-5.0" },
    { name: "Chloride", label: "Chloride", unit: "mEq/L", normalRange: "98-107" },
    { name: "CO2", label: "Carbon Dioxide", unit: "mEq/L", normalRange: "22-29" },
    { name: "Calcium", label: "Calcium", unit: "mg/dL", normalRange: "8.5-10.5" }
  ],
  "Lipid Panel": [
    { name: "TotalCholesterol", label: "Total Cholesterol", unit: "mg/dL", normalRange: "<200" },
    { name: "LDL", label: "LDL Cholesterol", unit: "mg/dL", normalRange: "<100" },
    { name: "HDL", label: "HDL Cholesterol", unit: "mg/dL", normalRange: ">40 (M), >50 (F)" },
    { name: "Triglycerides", label: "Triglycerides", unit: "mg/dL", normalRange: "<150" }
  ],
  "TSH (Thyroid Stimulating Hormone)": [
    { name: "TSH", label: "TSH", unit: "mIU/L", normalRange: "0.4-4.0" }
  ],
  "T3 (Triiodothyronine)": [
    { name: "T3", label: "T3", unit: "ng/dL", normalRange: "80-200" }
  ],
  "T4 (Thyroxine)": [
    { name: "T4", label: "T4", unit: "μg/dL", normalRange: "4.5-12.0" }
  ],
  "Vitamin B12": [
    { name: "B12", label: "Vitamin B12", unit: "pg/mL", normalRange: "200-900" }
  ],
  "Vitamin D": [
    { name: "VitaminD", label: "25-Hydroxy Vitamin D", unit: "ng/mL", normalRange: "30-100" }
  ],
  "Folate": [
    { name: "Folate", label: "Folate", unit: "ng/mL", normalRange: "2.7-17.0" }
  ]
}

// Psychological assessment questionnaires
const PSYCHOLOGICAL_QUESTIONNAIRES = {
  "PHQ-9 (Patient Health Questionnaire)": {
    questions: [
      "Little interest or pleasure in doing things",
      "Feeling down, depressed, or hopeless",
      "Trouble falling or staying asleep, or sleeping too much",
      "Feeling tired or having little energy",
      "Poor appetite or overeating",
      "Feeling bad about yourself or that you are a failure",
      "Trouble concentrating on things",
      "Moving or speaking slowly or being fidgety/restless",
      "Thoughts that you would be better off dead or hurting yourself"
    ],
    scoring: "0-4: Minimal depression, 5-9: Mild depression, 10-14: Moderate depression, 15-19: Moderately severe depression, 20-27: Severe depression"
  },
  "GAD-7 (Generalized Anxiety Disorder Scale)": {
    questions: [
      "Feeling nervous, anxious, or on edge",
      "Not being able to stop or control worrying",
      "Worrying too much about different things",
      "Trouble relaxing",
      "Being so restless that it's hard to sit still",
      "Becoming easily annoyed or irritable",
      "Feeling afraid as if something awful might happen"
    ],
    scoring: "0-4: Minimal anxiety, 5-9: Mild anxiety, 10-14: Moderate anxiety, 15-21: Severe anxiety"
  },
  "MMSE (Mini-Mental State Exam)": {
    questions: [
      "What year is it? (1 point)",
      "What season is it? (1 point)",
      "What date is it? (1 point)",
      "What day of the week is it? (1 point)",
      "What month is it? (1 point)",
      "What state are we in? (1 point)",
      "What county are we in? (1 point)",
      "What town/city are we in? (1 point)",
      "What building are we in? (1 point)",
      "What floor are we on? (1 point)",
      "Name three objects (apple, penny, table) - Registration (3 points)",
      "Serial 7s: 100-7, 93-7, 86-7, 79-7, 72-7 (5 points)",
      "Recall the three objects named earlier (3 points)",
      "Name a pencil (1 point)",
      "Name a watch (1 point)",
      "Repeat: 'No ifs, ands, or buts' (1 point)",
      "Follow 3-stage command: Take paper, fold it, put it on floor (3 points)",
      "Read and obey: 'Close your eyes' (1 point)",
      "Write a sentence (1 point)",
      "Copy intersecting pentagons (1 point)"
    ],
    scoring: "24-30: Normal cognition, 18-23: Mild cognitive impairment, 0-17: Severe cognitive impairment",
    maxScore: 30,
    type: "mmse"
  },
  "DASS-21 (Depression, Anxiety and Stress Scale)": {
    questions: [
      "I found it hard to wind down (Stress)",
      "I was aware of dryness of my mouth (Anxiety)",
      "I couldn't seem to experience any positive feeling at all (Depression)",
      "I experienced breathing difficulty (Anxiety)",
      "I found it difficult to work up the initiative to do things (Depression)",
      "I tended to over-react to situations (Stress)",
      "I experienced trembling (eg, in the hands) (Anxiety)",
      "I felt that I was using a lot of nervous energy (Stress)",
      "I was worried about situations in which I might panic (Anxiety)",
      "I felt that I had nothing to look forward to (Depression)",
      "I found myself getting agitated (Stress)",
      "I found it difficult to relax (Stress)",
      "I felt down-hearted and blue (Depression)",
      "I was intolerant of anything that kept me from getting on with what I was doing (Stress)",
      "I felt I was close to panic (Anxiety)",
      "I was unable to become enthusiastic about anything (Depression)",
      "I felt I wasn't worth much as a person (Depression)",
      "I felt that I was rather touchy (Stress)",
      "I was aware of the action of my heart in the absence of physical exertion (Anxiety)",
      "I felt scared without any good reason (Anxiety)",
      "I felt that life was meaningless (Depression)"
    ],
    scoring: "Depression: 0-9 Normal, 10-13 Mild, 14-20 Moderate, 21-27 Severe, 28+ Extremely Severe | Anxiety: 0-7 Normal, 8-9 Mild, 10-14 Moderate, 15-19 Severe, 20+ Extremely Severe | Stress: 0-14 Normal, 15-18 Mild, 19-25 Moderate, 26-33 Severe, 34+ Extremely Severe",
    subscales: {
      depression: [2, 4, 9, 12, 15, 16, 20],
      anxiety: [1, 3, 6, 8, 14, 18, 19],
      stress: [0, 5, 7, 10, 11, 13, 17]
    },
    type: "dass21"
  }
}



interface BloodTestComponent {
  name: string
  value: string
  unit: string
  normalRange: string
  notes?: string
}

interface PsychologicalAssessmentResult {
  testName: string
  responses: Record<string, number>
  totalScore: number
  interpretation: string
  isCompleted: boolean
}

interface ImagingStudyResult {
  testName: string
  findings: string
  impression: string
  recommendations: string
}

interface NeurologicalTestResult {
  testName: string
  findings: string
  interpretation: string
  recommendations: string
}

interface LaboratoryTestsData {
  // Enhanced Blood Work Data
  bloodWork?: {
    thyroidFunction?: any
    vitaminLevels?: any
    liverFunction?: any
    completeBloodCount?: any
    metabolicPanel?: any
    lipidPanel?: any
    inflammatoryMarkers?: any
    hormonalTests?: any
  }

  // Toxicology Screening Data
  toxicologyScreen?: any

  // New Component Data
  psychologicalTests?: PsychologicalTestsData
  imagingAndNeurological?: ImagingStudiesData

  // Legacy data for backward compatibility
  selectedTests?: Record<string, boolean>
  bloodTestComponents?: Record<string, Record<string, BloodTestComponent>>
  bloodTestNotes?: Record<string, string>
  psychologicalAssessments?: Record<string, PsychologicalAssessmentResult>
  imagingStudies?: Record<string, ImagingStudyResult>
  neurologicalTests?: Record<string, NeurologicalTestResult>
}

interface LaboratoryTestsSectionProps {
  data: LaboratoryTestsData
  onUpdate: (data: LaboratoryTestsData) => void
}

export default function LaboratoryTestsSection({ data, onUpdate }: LaboratoryTestsSectionProps) {
  const [formData, setFormData] = useState<LaboratoryTestsData>(() => data || {
    bloodWork: {},
    toxicologyScreen: {},
    psychologicalTests: { psychologicalAssessments: {} },
    imagingAndNeurological: { imagingStudies: {}, neurologicalTests: {} },
    selectedTests: {},
    bloodTestComponents: {},
    bloodTestNotes: {},
    psychologicalAssessments: {},
    imagingStudies: {},
    neurologicalTests: {}
  })

  // Update formData when data prop changes (for loading existing assessments)
  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      setFormData(data)
    }
  }, [data])

  // Notify parent of changes with debouncing to prevent infinite loops
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onUpdate(formData)
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [formData, onUpdate])

  const handleBloodWorkUpdate = (bloodWorkData: any) => {
    setFormData(prev => ({
      ...prev,
      bloodWork: bloodWorkData
    }))
  }

  const handleToxicologyUpdate = (toxicologyData: any) => {
    setFormData(prev => ({
      ...prev,
      toxicologyScreen: toxicologyData
    }))
  }

  const handlePsychologicalTestsUpdate = (psychologicalData: PsychologicalTestsData) => {
    setFormData(prev => ({
      ...prev,
      psychologicalTests: psychologicalData
    }))
  }

  const handleImagingAndNeurologicalUpdate = (imagingData: ImagingStudiesData) => {
    setFormData(prev => ({
      ...prev,
      imagingAndNeurological: imagingData
    }))
  }

  const handleTestSelection = (testName: string, category: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedTests: {
        ...prev.selectedTests,
        [testName]: checked
      }
    }))
  }



  // Blood test component handlers
  const handleBloodTestComponentUpdate = (testName: string, componentName: string, field: keyof BloodTestComponent, value: string) => {
    setFormData(prev => ({
      ...prev,
      bloodTestComponents: {
        ...prev.bloodTestComponents,
        [testName]: {
          ...prev.bloodTestComponents?.[testName],
          [componentName]: {
            ...prev.bloodTestComponents?.[testName]?.[componentName],
            [field]: value
          } as BloodTestComponent
        }
      }
    }))
  }

  // Psychological assessment handlers
  const handlePsychologicalResponse = (testName: string, questionIndex: number, score: number) => {
    setFormData(prev => {
      const currentAssessment = prev.psychologicalAssessments?.[testName] || {
        testName,
        responses: {},
        totalScore: 0,
        interpretation: '',
        isCompleted: false
      }

      const updatedResponses = {
        ...currentAssessment.responses,
        [questionIndex.toString()]: score
      }

      const totalScore = Object.values(updatedResponses).reduce((sum, score) => sum + (score as number), 0)

      return {
        ...prev,
        psychologicalAssessments: {
          ...prev.psychologicalAssessments,
          [testName]: {
            ...currentAssessment,
            responses: updatedResponses,
            totalScore
          }
        }
      }
    })
  }

  const completePsychologicalAssessment = (testName: string) => {
    setFormData(prev => {
      const assessment = prev.psychologicalAssessments?.[testName]
      if (!assessment) return prev

      const questionnaire = PSYCHOLOGICAL_QUESTIONNAIRES[testName as keyof typeof PSYCHOLOGICAL_QUESTIONNAIRES]
      let interpretation = ''

      if (testName === 'PHQ-9 (Patient Health Questionnaire)') {
        if (assessment.totalScore <= 4) interpretation = 'Minimal depression'
        else if (assessment.totalScore <= 9) interpretation = 'Mild depression'
        else if (assessment.totalScore <= 14) interpretation = 'Moderate depression'
        else if (assessment.totalScore <= 19) interpretation = 'Moderately severe depression'
        else interpretation = 'Severe depression'
      } else if (testName === 'GAD-7 (Generalized Anxiety Disorder Scale)') {
        if (assessment.totalScore <= 4) interpretation = 'Minimal anxiety'
        else if (assessment.totalScore <= 9) interpretation = 'Mild anxiety'
        else if (assessment.totalScore <= 14) interpretation = 'Moderate anxiety'
        else interpretation = 'Severe anxiety'
      } else if (testName === 'MMSE (Mini-Mental State Exam)') {
        if (assessment.totalScore >= 24) interpretation = 'Normal cognition'
        else if (assessment.totalScore >= 18) interpretation = 'Mild cognitive impairment'
        else interpretation = 'Severe cognitive impairment'
      } else if (testName === 'DASS-21 (Depression, Anxiety and Stress Scale)') {
        // DASS-21 has subscales - calculate each subscale score
        const depressionItems = [2, 4, 9, 12, 15, 16, 20] // 0-indexed
        const anxietyItems = [1, 3, 6, 8, 14, 18, 19]
        const stressItems = [0, 5, 7, 10, 11, 13, 17]
        
        const depressionScore = depressionItems.reduce((sum, item) => sum + (assessment.responses?.[item.toString()] || 0), 0) * 2
        const anxietyScore = anxietyItems.reduce((sum, item) => sum + (assessment.responses?.[item.toString()] || 0), 0) * 2
        const stressScore = stressItems.reduce((sum, item) => sum + (assessment.responses?.[item.toString()] || 0), 0) * 2
        
        const getDepressionLevel = (score: number) => {
          if (score <= 9) return 'Normal'
          if (score <= 13) return 'Mild'
          if (score <= 20) return 'Moderate'
          if (score <= 27) return 'Severe'
          return 'Extremely Severe'
        }
        
        const getAnxietyLevel = (score: number) => {
          if (score <= 7) return 'Normal'
          if (score <= 9) return 'Mild'
          if (score <= 14) return 'Moderate'
          if (score <= 19) return 'Severe'
          return 'Extremely Severe'
        }
        
        const getStressLevel = (score: number) => {
          if (score <= 14) return 'Normal'
          if (score <= 18) return 'Mild'
          if (score <= 25) return 'Moderate'
          if (score <= 33) return 'Severe'
          return 'Extremely Severe'
        }
        
        interpretation = `Depression: ${depressionScore} (${getDepressionLevel(depressionScore)}), Anxiety: ${anxietyScore} (${getAnxietyLevel(anxietyScore)}), Stress: ${stressScore} (${getStressLevel(stressScore)})`
      }

      return {
        ...prev,
        psychologicalAssessments: {
          ...prev.psychologicalAssessments,
          [testName]: {
            ...assessment,
            interpretation,
            isCompleted: true
          }
        }
      }
    })
  }

  // Imaging study handlers
  const handleImagingStudyUpdate = (testName: string, field: keyof ImagingStudyResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      imagingStudies: {
        ...prev.imagingStudies,
        [testName]: {
          ...prev.imagingStudies?.[testName],
          testName,
          [field]: value
        } as ImagingStudyResult
      }
    }))
  }

  // Neurological test handlers
  const handleNeurologicalTestUpdate = (testName: string, field: keyof NeurologicalTestResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      neurologicalTests: {
        ...prev.neurologicalTests,
        [testName]: {
          ...prev.neurologicalTests?.[testName],
          testName,
          [field]: value
        } as NeurologicalTestResult
      }
    }))
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-slate-900 mb-3">Laboratory Tests & Medical Screening</h2>
        <p className="text-base text-slate-600 leading-relaxed">Comprehensive laboratory testing with clinical-grade documentation and interpretation.</p>
      </div>

      <Tabs defaultValue="bloodwork" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="bloodwork" className="flex items-center gap-2">
            <TestTube className="h-4 w-4" />
            Blood Work
          </TabsTrigger>
          <TabsTrigger value="toxicology" className="flex items-center gap-2">
            <Microscope className="h-4 w-4" />
            Toxicology
          </TabsTrigger>
          <TabsTrigger value="psychological" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Psychological
          </TabsTrigger>
          <TabsTrigger value="imaging" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Imaging & Other
          </TabsTrigger>
        </TabsList>

        <TabsContent value="bloodwork" className="mt-6">
          <BloodWorkPanel
            data={formData.bloodWork || {}}
            onUpdate={handleBloodWorkUpdate}
          />
        </TabsContent>

        <TabsContent value="toxicology" className="mt-6">
          <ToxicologyPanel
            data={formData.toxicologyScreen || {}}
            onUpdate={handleToxicologyUpdate}
          />
        </TabsContent>

        <TabsContent value="psychological" className="mt-6">
          <PsychologicalTestsPanel
            data={formData.psychologicalTests || { psychologicalAssessments: {} }}
            onUpdate={handlePsychologicalTestsUpdate}
          />
        </TabsContent>

        <TabsContent value="imaging" className="mt-6">
          <ImagingStudiesPanel
            data={formData.imagingAndNeurological || { imagingStudies: {}, neurologicalTests: {} }}
            onUpdate={handleImagingAndNeurologicalUpdate}
          />
        </TabsContent>
      </Tabs>
    </div>
  )

}

export type { LaboratoryTestsData }
