(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[333],{932:function(e,s,t){Promise.resolve().then(t.bind(t,7037))},2252:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("<PERSON>Left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1047:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},5302:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9397:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3247:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},2369:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},7037:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return j}});var r=t(7437),n=t(2265),a=t(9820),i=t(2381),l=t(279),c=t(9174),d=t(5302),o=t(1723),u=t(8736),m=t(2252),x=t(2660),f=t(9397),h=t(3247),p=t(2369),v=t(1047);let g=(0,t(9763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);var y=t(7648);function j(){let[e,s]=(0,n.useState)([]),[t,j]=(0,n.useState)([]),[b,N]=(0,n.useState)(!0),[w,k]=(0,n.useState)("");(0,n.useEffect)(()=>{Z()},[]),(0,n.useEffect)(()=>{""===w.trim()?j(e):j(e.filter(e=>{var s,t,r;return(null===(t=e.demographics)||void 0===t?void 0:null===(s=t.patientCode)||void 0===s?void 0:s.toLowerCase().includes(w.toLowerCase()))||(null===(r=e.assessorName)||void 0===r?void 0:r.toLowerCase().includes(w.toLowerCase()))||e.id.toLowerCase().includes(w.toLowerCase())}))},[w,e]);let Z=async()=>{try{let e=await fetch("/api/assessments");if(e.ok){let t=await e.json();s(t),j(t)}}catch(e){console.error("Error fetching assessments:",e)}finally{N(!1)}},C=e=>{switch(e){case"completed":return(0,r.jsxs)(c.C,{className:"bg-green-100 text-green-800",children:[(0,r.jsx)(d.Z,{className:"h-3 w-3 mr-1"}),"Completed"]});case"in_progress":return(0,r.jsxs)(c.C,{className:"bg-blue-100 text-blue-800",children:[(0,r.jsx)(o.Z,{className:"h-3 w-3 mr-1"}),"In Progress"]});case"draft":return(0,r.jsxs)(c.C,{className:"bg-gray-100 text-gray-800",children:[(0,r.jsx)(u.Z,{className:"h-3 w-3 mr-1"}),"Draft"]});default:return(0,r.jsxs)(c.C,{className:"bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(m.Z,{className:"h-3 w-3 mr-1"}),"Unknown"]})}},S=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,r.jsxs)("div",{className:"assessment-container py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(y.default,{href:"/",children:(0,r.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(x.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"Patient Management"}),(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Manage and continue patient assessments"})]})]}),(0,r.jsx)(y.default,{href:"/assessment",children:(0,r.jsxs)(i.z,{children:[(0,r.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"New Assessment"]})})]}),(0,r.jsxs)(a.Zb,{className:"mb-6",children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{className:"text-lg",children:"Search Patients"}),(0,r.jsx)(a.SZ,{children:"Find patients by anonymous code, assessor name, or assessment ID"})]}),(0,r.jsx)(a.aY,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,r.jsx)(l.I,{placeholder:"Search by patient code, assessor name, or ID...",value:w,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"text-sm text-slate-600",children:[t.length," of ",e.length," assessments"]})]})})]}),(0,r.jsxs)(a.Zb,{children:[(0,r.jsxs)(a.Ol,{children:[(0,r.jsx)(a.ll,{children:"Assessment Records"}),(0,r.jsx)(a.SZ,{children:"All patient assessments with status and completion information"})]}),(0,r.jsx)(a.aY,{children:b?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-slate-600",children:"Loading assessments..."})]}):0===t.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(p.Z,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-slate-600",children:w?"No assessments match your search":"No assessments found"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 mt-1",children:(0,r.jsx)(y.default,{href:"/assessment",className:"text-blue-600 hover:underline",children:"Create your first assessment"})})]}):(0,r.jsx)("div",{className:"space-y-4",children:t.map(e=>{var s,t,n,a,l;return(0,r.jsx)("div",{className:"border rounded-lg p-4 hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)(p.Z,{className:"h-4 w-4 text-slate-500"}),(0,r.jsx)("span",{className:"font-medium font-mono",children:(null===(s=e.demographics)||void 0===s?void 0:s.patientCode)||"No Patient Code"}),C(e.status)]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-slate-600",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(v.Z,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:["Created: ",S(e.createdAt)]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(v.Z,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:["Updated: ",S(e.updatedAt)]})]}),(0,r.jsx)("div",{children:(0,r.jsxs)("span",{children:["Age: ",(null===(t=e.demographics)||void 0===t?void 0:t.age)||"N/A"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("span",{children:["Gender: ",(null===(n=e.demographics)||void 0===n?void 0:n.gender)||"N/A"]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-slate-500",children:[(0,r.jsxs)("span",{children:["Symptoms: ",(null===(a=e._count)||void 0===a?void 0:a.symptoms)||0]}),(0,r.jsxs)("span",{children:["Diagnoses: ",(null===(l=e._count)||void 0===l?void 0:l.diagnoses)||0]}),(0,r.jsxs)("span",{children:["Assessor: ",e.assessorName]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)(y.default,{href:"/assessment?id=".concat(e.id),children:(0,r.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(g,{className:"h-4 w-4 mr-1"}),"completed"===e.status?"View":"Continue"]})})})]})},e.id)})})})]})]})}},9174:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var r=t(7437);t(2265);var n=t(535),a=t(3448);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...n}=e;return(0,r.jsx)("div",{className:(0,a.cn)(i({variant:t}),s),...n})}},2381:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var r=t(7437),n=t(2265),a=t(7053),i=t(535),l=t(3448);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,s)=>{let{className:t,variant:n,size:i,asChild:d=!1,...o}=e,u=d?a.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(c({variant:n,size:i,className:t})),ref:s,...o})});d.displayName="Button"},9820:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},ll:function(){return c}});var r=t(7437),n=t(2265),a=t(3448);let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});i.displayName="Card";let l=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...n})});l.displayName="CardHeader";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});c.displayName="CardTitle";let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",t),...n})});o.displayName="CardContent",n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},279:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var r=t(7437),n=t(2265),a=t(3448);let i=n.forwardRef((e,s)=>{let{className:t,type:n,...i}=e;return(0,r.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},3448:function(e,s,t){"use strict";t.d(s,{cn:function(){return a}});var r=t(1994),n=t(3335);function a(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,r.W)(s))}}},function(e){e.O(0,[773,971,117,744],function(){return e(e.s=932)}),_N_E=e.O()}]);