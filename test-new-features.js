// Test script for new psychiatric assessment features
const testData = {
  // Basic assessment data
  patientId: "test-patient-123",
  symptoms: {
    selected: ["anxiety", "depression", "insomnia"],
    symptomDetails: {
      "anxiety": {
        severity: "moderate",
        duration: "2 weeks",
        frequency: "daily",
        notes: "Generalized anxiety with panic episodes"
      },
      "depression": {
        severity: "mild",
        duration: "1 month", 
        frequency: "daily",
        notes: "Low mood and decreased interest"
      },
      "insomnia": {
        severity: "severe",
        duration: "3 weeks",
        frequency: "nightly",
        notes: "Difficulty falling asleep"
      }
    }
  },
  
  // New psychological tests data structure
  laboratoryTests: {
    psychologicalTests: {
      psychologicalAssessments: {
        "PHQ-9": {
          testName: "PHQ-9",
          datePerformed: "2024-01-15",
          totalScore: 12,
          interpretation: "Moderate depression",
          notes: "Patient shows significant depressive symptoms",
          isCompleted: true
        },
        "GAD-7": {
          testName: "GAD-7", 
          datePerformed: "2024-01-15",
          totalScore: 8,
          interpretation: "Mild anxiety",
          notes: "Anxiety symptoms present but manageable",
          isCompleted: true
        },
        "MMSE": {
          testName: "MMSE",
          datePerformed: "2024-01-15", 
          totalScore: 28,
          interpretation: "Normal cognition",
          notes: "No cognitive impairment detected",
          isCompleted: true
        }
      }
    },
    
    // New imaging and neurological data structure
    imagingAndNeurological: {
      imagingStudies: {
        "Brain MRI": {
          testName: "Brain MRI",
          category: "Neuroimaging",
          datePerformed: "2024-01-10",
          facility: "City Medical Center",
          findings: "No acute abnormalities. Mild age-related changes.",
          impression: "Normal brain MRI for age",
          notes: "High quality study, good patient cooperation",
          isNormal: true,
          isUrgent: false,
          followUpRequired: false
        },
        "EEG": {
          testName: "EEG",
          category: "Neurophysiology", 
          datePerformed: "2024-01-12",
          facility: "Neurology Clinic",
          findings: "Normal background activity. No epileptiform discharges.",
          impression: "Normal EEG",
          notes: "20-minute recording, patient awake and cooperative",
          isNormal: true,
          isUrgent: false,
          followUpRequired: false
        }
      },
      neurologicalTests: {
        "Sleep Study": {
          testName: "Sleep Study",
          category: "Sleep Medicine",
          datePerformed: "2024-01-08",
          facility: "Sleep Center",
          results: "AHI: 5.2 events/hour. Sleep efficiency: 85%",
          interpretation: "Mild sleep fragmentation, no significant sleep apnea",
          notes: "Patient had difficulty falling asleep initially",
          isNormal: false,
          followUpRequired: true
        }
      }
    },
    
    // Simplified toxicology data structure
    toxicologyScreen: {
      datePerformed: "2024-01-15",
      substances: {
        cannabis: false,
        cocaine: false,
        amphetamines: false,
        opiates: false,
        oxycodone: false,
        benzodiazepines: true,
        barbiturates: false,
        pcp: false,
        alcohol: false,
        syntheticDrugs: false,
        prescriptionDrugs: true,
        other: false
      },
      comments: "Positive for prescribed benzodiazepines (lorazepam) and antidepressants. Patient compliant with prescribed medications."
    },
    
    // Blood work with reorganized hierarchy
    bloodWork: {
      completeBloodCount: {
        datePerformed: "2024-01-15",
        hemoglobin: "13.5",
        hematocrit: "40.2",
        wbc: "6.8",
        platelets: "285",
        neutrophils: "62",
        lymphocytes: "28",
        monocytes: "6",
        eosinophils: "3",
        basophils: "1",
        mcv: "88",
        mch: "30",
        mchc: "34",
        interpretation: "Normal complete blood count",
        notes: "All values within normal limits"
      },
      metabolicPanel: {
        datePerformed: "2024-01-15",
        glucose: "95",
        hba1c: "5.4",
        creatinine: "0.9",
        bun: "15",
        egfr: "85",
        sodium: "140",
        potassium: "4.2",
        chloride: "102",
        co2: "24",
        anionGap: "12",
        interpretation: "Normal metabolic panel",
        notes: "Good glucose control, normal kidney function"
      },
      lipidPanel: {
        datePerformed: "2024-01-15",
        totalCholesterol: "185",
        ldl: "110",
        hdl: "55",
        triglycerides: "120",
        interpretation: "Borderline cholesterol levels",
        notes: "Consider dietary modifications"
      },
      liverFunction: {
        datePerformed: "2024-01-15",
        alt: "25",
        ast: "22",
        totalBilirubin: "0.8",
        directBilirubin: "0.2",
        alkalinePhosphatase: "75",
        ggt: "18",
        albumin: "4.2",
        ptInr: "1.0",
        interpretation: "Normal liver function",
        notes: "No evidence of hepatic dysfunction"
      },
      thyroidFunction: {
        datePerformed: "2024-01-15",
        tsh: "2.1",
        t4: "7.8",
        t3: "3.2",
        interpretation: "Normal thyroid function",
        notes: "Euthyroid state"
      }
    }
  },
  
  // Diagnosis data
  diagnosis: {
    primaryDiagnosis: "Major Depressive Disorder, Moderate",
    primaryDiagnosisCode: "F32.1",
    secondaryDiagnoses: [
      {
        diagnosis: "Generalized Anxiety Disorder",
        code: "F41.1",
        type: "secondary"
      },
      {
        diagnosis: "Insomnia Disorder",
        code: "G47.00", 
        type: "secondary"
      }
    ]
  }
};

// Function to test the API
async function testNewFeatures() {
  try {
    console.log('Testing new psychiatric assessment features...');
    
    // Test POST endpoint
    const response = await fetch('http://localhost:3000/api/assessments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    console.log('✅ POST Assessment successful:', result);
    
    if (result.assessmentId) {
      // Test GET endpoint
      const getResponse = await fetch(`http://localhost:3000/api/assessments?id=${result.assessmentId}`);
      
      if (!getResponse.ok) {
        throw new Error(`HTTP error! status: ${getResponse.status}`);
      }
      
      const assessment = await getResponse.json();
      console.log('✅ GET Assessment successful');
      
      // Verify new features are present
      console.log('🔍 Verifying new features...');
      
      // Check psychological tests
      if (assessment.testsData?.psychologicalTests?.psychologicalAssessments) {
        console.log('✅ Psychological tests data structure present');
        console.log('   - Found tests:', Object.keys(assessment.testsData.psychologicalTests.psychologicalAssessments));
      } else {
        console.log('❌ Psychological tests data structure missing');
      }
      
      // Check imaging and neurological
      if (assessment.testsData?.imagingAndNeurological) {
        console.log('✅ Imaging and neurological data structure present');
        console.log('   - Imaging studies:', Object.keys(assessment.testsData.imagingAndNeurological.imagingStudies || {}));
        console.log('   - Neurological tests:', Object.keys(assessment.testsData.imagingAndNeurological.neurologicalTests || {}));
      } else {
        console.log('❌ Imaging and neurological data structure missing');
      }
      
      // Check toxicology
      if (assessment.testsData?.toxicologyScreen) {
        console.log('✅ Simplified toxicology data structure present');
        console.log('   - Substances checked:', Object.keys(assessment.testsData.toxicologyScreen.substances || {}));
      } else {
        console.log('❌ Simplified toxicology data structure missing');
      }
      
      // Check blood work reorganization
      if (assessment.testsData?.laboratoryTests?.bloodWork) {
        console.log('✅ Reorganized blood work structure present');
        const bloodWork = assessment.testsData.laboratoryTests.bloodWork;
        console.log('   - CBC present:', !!bloodWork.completeBloodCount);
        console.log('   - Metabolic panel present:', !!bloodWork.metabolicPanel);
        console.log('   - Lipid panel present:', !!bloodWork.lipidPanel);
        console.log('   - Liver function present:', !!bloodWork.liverFunction);
        console.log('   - Thyroid function present:', !!bloodWork.thyroidFunction);
      } else {
        console.log('❌ Reorganized blood work structure missing');
      }
      
      console.log('\n🎉 All new features tested successfully!');
      
    } else {
      console.log('❌ No assessment ID returned');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testNewFeatures();
