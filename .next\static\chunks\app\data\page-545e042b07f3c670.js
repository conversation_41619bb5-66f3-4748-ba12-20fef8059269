(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[315],{2614:function(e,s,t){Promise.resolve().then(t.bind(t,1904))},2660:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6221:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},1047:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},91:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},2735:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2369:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1904:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return p}});var r=t(7437),a=t(2265),n=t(9820),i=t(2381),l=t(9174),c=t(2660),d=t(2735),o=t(91),x=t(8736),m=t(6221),u=t(1047),h=t(2369),f=t(7648);function p(){let[e,s]=(0,a.useState)([]),[t,p]=(0,a.useState)(!0),[j,g]=(0,a.useState)(!1);(0,a.useEffect)(()=>{v()},[]);let v=async()=>{try{let e=await fetch("/api/assessments");if(e.ok){let t=await e.json();s(t)}}catch(e){console.error("Error fetching assessments:",e)}finally{p(!1)}},y=async e=>{g(!0);try{let s=await fetch("/api/export?format=".concat(e));if(!s.ok)throw Error("Failed to export data");let t=await s.blob(),r=URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download="psychiatric-assessments-".concat(new Date().toISOString().split("T")[0],".").concat(e),a.click(),URL.revokeObjectURL(r)}catch(e){console.error("Error exporting data:",e),alert("Failed to export data. Please try again.")}finally{g(!1)}},N=e=>{switch(e){case"completed":return(0,r.jsx)(l.C,{variant:"default",className:"bg-green-500",children:"Completed"});case"in_progress":return(0,r.jsx)(l.C,{variant:"secondary",children:"In Progress"});case"draft":return(0,r.jsx)(l.C,{variant:"outline",children:"Draft"});default:return(0,r.jsx)(l.C,{variant:"outline",children:e})}},b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,r.jsxs)("div",{className:"assessment-container py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(f.default,{href:"/",children:(0,r.jsxs)(i.z,{variant:"outline",size:"sm",children:[(0,r.jsx)(c.Z,{className:"h-4 w-4 mr-2"}),"Back to Home"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"Assessment Data"}),(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"View and export collected assessment data for ML training"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(i.z,{onClick:()=>y("csv"),disabled:j||0===e.length,variant:"outline",children:[(0,r.jsx)(d.Z,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,r.jsxs)(i.z,{onClick:()=>y("json"),disabled:j||0===e.length,variant:"outline",children:[(0,r.jsx)(d.Z,{className:"h-4 w-4 mr-2"}),"Export JSON"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.Z,{className:"h-5 w-5 text-blue-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Total Assessments"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:e.length})]})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.Z,{className:"h-5 w-5 text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"Completed"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:e.filter(e=>"completed"===e.status).length})]})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 text-orange-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"In Progress"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:e.filter(e=>"in_progress"===e.status).length})]})]})})}),(0,r.jsx)(n.Zb,{children:(0,r.jsx)(n.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.Z,{className:"h-5 w-5 text-purple-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-slate-600",children:"This Week"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:e.filter(e=>{let s=new Date;return s.setDate(s.getDate()-7),new Date(e.createdAt)>s}).length})]})]})})})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsx)(n.ll,{children:"Assessment Records"}),(0,r.jsx)(n.SZ,{children:"All collected assessment data ready for ML training"})]}),(0,r.jsx)(n.aY,{children:t?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-slate-600",children:"Loading assessments..."})]}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(o.Z,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-slate-600",children:"No assessments found"}),(0,r.jsx)("p",{className:"text-sm text-slate-500 mt-1",children:(0,r.jsx)(f.default,{href:"/assessment",className:"text-blue-600 hover:underline",children:"Create your first assessment"})})]}):(0,r.jsx)("div",{className:"space-y-4",children:e.map(e=>{var s,t,a,n;return(0,r.jsx)("div",{className:"border rounded-lg p-4 hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)(h.Z,{className:"h-4 w-4 text-slate-500"}),(0,r.jsx)("span",{className:"font-medium",children:(null===(s=e.demographics)||void 0===s?void 0:s.firstName)&&(null===(t=e.demographics)||void 0===t?void 0:t.lastName)?"".concat(e.demographics.firstName," ").concat(e.demographics.lastName):e.assessorName||"Anonymous"}),N(e.status)]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-slate-600",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Age:"})," ",(null===(a=e.demographics)||void 0===a?void 0:a.age)||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Gender:"})," ",(null===(n=e.demographics)||void 0===n?void 0:n.gender)||"N/A"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Symptoms:"})," ",e._count.symptoms]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Diagnoses:"})," ",e._count.diagnoses]})]})]}),(0,r.jsx)("div",{className:"text-right text-sm text-slate-500",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(u.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:b(e.createdAt)})]})})]})},e.id)})})})]}),(0,r.jsxs)(n.Zb,{className:"mt-6",children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsx)(n.ll,{children:"ML Training Data Format"}),(0,r.jsx)(n.SZ,{children:"Information about the exported data structure for machine learning"})]}),(0,r.jsx)(n.aY,{children:(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"CSV Export Features"}),(0,r.jsxs)("ul",{className:"text-sm text-slate-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Flattened structure for easy ML processing"}),(0,r.jsx)("li",{children:"• Categorical variables properly encoded"}),(0,r.jsx)("li",{children:"• Boolean risk factors as binary features"}),(0,r.jsx)("li",{children:"• Timestamp data for temporal analysis"}),(0,r.jsx)("li",{children:"• Symptom and diagnosis counts as features"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"JSON Export Features"}),(0,r.jsxs)("ul",{className:"text-sm text-slate-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Complete hierarchical data structure"}),(0,r.jsx)("li",{children:"• Preserves all relationships and metadata"}),(0,r.jsx)("li",{children:"• Detailed symptom and diagnosis information"}),(0,r.jsx)("li",{children:"• Full text responses for NLP analysis"}),(0,r.jsx)("li",{children:"• Export metadata and timestamps"})]})]})]})})]})]})}},9174:function(e,s,t){"use strict";t.d(s,{C:function(){return l}});var r=t(7437);t(2265);var a=t(535),n=t(3448);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),s),...a})}},2381:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var r=t(7437),a=t(2265),n=t(7053),i=t(535),l=t(3448);let c=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:i,asChild:d=!1,...o}=e,x=d?n.g7:"button";return(0,r.jsx)(x,{className:(0,l.cn)(c({variant:a,size:i,className:t})),ref:s,...o})});d.displayName="Button"},9820:function(e,s,t){"use strict";t.d(s,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return i},aY:function(){return o},ll:function(){return c}});var r=t(7437),a=t(2265),n=t(3448);let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},3448:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var r=t(1994),a=t(3335);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.m6)((0,r.W)(s))}}},function(e){e.O(0,[773,971,117,744],function(){return e(e.s=2614)}),_N_E=e.O()}]);