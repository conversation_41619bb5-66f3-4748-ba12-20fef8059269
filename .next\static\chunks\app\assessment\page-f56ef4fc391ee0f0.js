(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[326],{3832:function(e,s,a){Promise.resolve().then(a.bind(a,3639))},3639:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return e5}});var i=a(7437),l=a(2265),n=a(9376),t=a(9820),r=a(7168),c=a(610),o=a(3448);let d=l.forwardRef((e,s)=>{let{className:a,value:l,...n}=e;return(0,i.jsx)(c.fC,{ref:s,className:(0,o.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...n,children:(0,i.jsx)(c.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});d.displayName=c.fC.displayName;var m=a(2381),h=a(5805),x=a(4972),u=a(8906),p=a(8736),j=a(6221),g=a(91),v=a(7829),y=a(2660),f=a(3229),N=a(2252),b=a(5302),C=a(2735),S=a(7648),w=a(279),k=a(6394);let D=(0,a(535).j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),A=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(k.f,{ref:s,className:(0,o.cn)(D(),a),...l})});A.displayName=k.f.displayName;var P=a(7864),O=a(875),T=a(2135),F=a(401);let E=P.fC;P.ZA;let M=P.B4,I=l.forwardRef((e,s)=>{let{className:a,children:l,...n}=e;return(0,i.jsxs)(P.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[l,(0,i.jsx)(P.JO,{asChild:!0,children:(0,i.jsx)(O.Z,{className:"h-4 w-4 opacity-50"})})]})});I.displayName=P.xz.displayName;let L=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(P.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,i.jsx)(T.Z,{className:"h-4 w-4"})})});L.displayName=P.u_.displayName;let Z=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(P.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,i.jsx)(O.Z,{className:"h-4 w-4"})})});Z.displayName=P.$G.displayName;let R=l.forwardRef((e,s)=>{let{className:a,children:l,position:n="popper",...t}=e;return(0,i.jsx)(P.h_,{children:(0,i.jsxs)(P.VY,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...t,children:[(0,i.jsx)(L,{}),(0,i.jsx)(P.l_,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,i.jsx)(Z,{})]})})});R.displayName=P.VY.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(P.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=P.__.displayName;let B=l.forwardRef((e,s)=>{let{className:a,children:l,...n}=e;return(0,i.jsxs)(P.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,i.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,i.jsx)(P.wU,{children:(0,i.jsx)(F.Z,{className:"h-4 w-4"})})}),(0,i.jsx)(P.eT,{children:l})]})});B.displayName=P.ck.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(P.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=P.Z0.displayName;let H=["Less than high school","High school diploma/GED","Some college","Associate degree","Bachelor's degree","Master's degree","Doctoral degree","Professional degree","Other"],V=["Student","Unemployed","Retired","Disabled","Homemaker","Healthcare worker","Teacher/Educator","Business/Finance","Technology","Service industry","Manufacturing","Government","Non-profit","Self-employed","Other"],U=["Lives alone","Lives with spouse/partner","Lives with family","Lives with roommates","Group home","Assisted living","Nursing home","Homeless","Other"],z=["Single","Married","Divorced","Separated","Widowed","Domestic partnership","Other"],Y=["Male","Female","Non-binary","Transgender male","Transgender female","Other","Prefer not to say"],G=["Hispanic or Latino","Not Hispanic or Latino","Prefer not to say"],q=["American Indian or Alaska Native","Asian","Black or African American","Native Hawaiian or Other Pacific Islander","White","Other","Prefer not to say"],W=["Private insurance","Medicare","Medicaid","Military/VA","Self-pay","Other","Uninsured"],_=["Full-time","Part-time","Unemployed","Student","Retired","Disabled","Other"],J=[{value:"mild",label:"Mild"},{value:"moderate",label:"Moderate"},{value:"severe",label:"Severe"}],K=["Less than 1 week","1-2 weeks","2-4 weeks","1-3 months","3-6 months","6-12 months","1-2 years","More than 2 years"],Q=["Daily","Several times a week","Weekly","Several times a month","Monthly","Rarely"],$=[{value:"low",label:"Low"},{value:"moderate",label:"Moderate"},{value:"high",label:"High"}],X=["Antidepressants","Antipsychotics","Anxiolytics","Mood Stabilizers","Stimulants","Anticonvulsants","Antihypertensives","Diabetes Medications","Other"],ee={Antidepressants:["Sertraline (Zoloft)","Fluoxetine (Prozac)","Escitalopram (Lexapro)","Paroxetine (Paxil)","Citalopram (Celexa)","Venlafaxine (Effexor)","Duloxetine (Cymbalta)","Bupropion (Wellbutrin)","Mirtazapine (Remeron)","Trazodone","Amitriptyline","Nortriptyline"],Antipsychotics:["Risperidone (Risperdal)","Olanzapine (Zyprexa)","Quetiapine (Seroquel)","Aripiprazole (Abilify)","Haloperidol (Haldol)","Clozapine (Clozaril)","Ziprasidone (Geodon)","Paliperidone (Invega)","Asenapine (Saphris)","Lurasidone (Latuda)"],Anxiolytics:["Lorazepam (Ativan)","Alprazolam (Xanax)","Clonazepam (Klonopin)","Diazepam (Valium)","Buspirone (Buspar)","Hydroxyzine (Vistaril)","Propranolol"],"Mood Stabilizers":["Lithium","Valproic Acid (Depakote)","Lamotrigine (Lamictal)","Carbamazepine (Tegretol)","Oxcarbazepine (Trileptal)","Topiramate (Topamax)"],Stimulants:["Methylphenidate (Ritalin)","Amphetamine (Adderall)","Lisdexamfetamine (Vyvanse)","Atomoxetine (Strattera)","Modafinil (Provigil)","Armodafinil (Nuvigil)"],Anticonvulsants:["Phenytoin (Dilantin)","Levetiracetam (Keppra)","Gabapentin (Neurontin)","Pregabalin (Lyrica)","Clonazepam (Klonopin)"],Antihypertensives:["Lisinopril","Amlodipine","Metoprolol","Losartan","Hydrochlorothiazide","Atenolol"],"Diabetes Medications":["Metformin","Insulin","Glipizide","Glyburide","Pioglitazone","Sitagliptin"],Other:["Custom medication (specify in notes)"]},es=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...l})});es.displayName="Textarea";var ea=a(9270);let ei=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(ea.fC,{ref:s,className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...l,children:(0,i.jsx)(ea.z$,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,i.jsx)(F.Z,{className:"h-4 w-4"})})})});ei.displayName=ea.fC.displayName;let el={Mood:["Depressed mood","Anhedonia (loss of interest)","Irritability","Mood swings","Hopelessness","Worthlessness","Guilt","Appetite changes","Fatigue","Pain complaints","Elevated mood (mania/hypomania)","Grandiosity","Decreased need for sleep","Increased goal-directed activity","Distractibility","Impulsivity","Social withdrawal"],Anxiety:["Anxiety","Panic attacks","Excessive worry","Restlessness","Hypervigilance","Avoidance"],Psychotic:["Hallucinations","Delusions","Paranoia","Ideas of reference","Disorganized speech","Isolation"],Cognitive:["Concentration difficulties","Memory problems","Disorientation","Slowed thinking","Racing thoughts"],Sleep:["Sleep disturbances","Insomnia","Hypersomnia","Nightmares"],Substance:["Substance use","Cravings","Withdrawal symptoms"],ObsessiveCompulsive:["Obsessive thoughts","Compulsive behaviors","Checking behaviors","Cleaning rituals"],Risk:["Suicidal thoughts","Self-harm behaviors","Aggression/violence"]};var en=a(2325),et=a(519);let er=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(en.fC,{className:(0,o.cn)("grid gap-2",a),...l,ref:s})});er.displayName=en.fC.displayName;let ec=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(en.ck,{ref:s,className:(0,o.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...l,children:(0,i.jsx)(en.z$,{className:"flex items-center justify-center",children:(0,i.jsx)(et.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});ec.displayName=en.ck.displayName;var eo=a(7476),ed=a(9174),em=a(9397),eh=a(8930);let ex={Alcohol:["Beer","Wine","Spirits/Liquor","Mixed Drinks","Other Alcohol"],Cannabis:["Marijuana/THC","Hashish","CBD Products","Synthetic Cannabis (K2/Spice)","Other Cannabis"],Hallucinogens:["LSD","PCP","Psilocybin (Mushrooms)","MDMA/Ecstasy","Mescaline","DMT","Other Hallucinogens"],Inhalants:["Nitrous Oxide","Glue/Solvents","Gasoline","Paint Thinner","Aerosols","Other Inhalants"],Opioids:["Heroin","Fentanyl","Oxycodone","Hydrocodone","Morphine","Codeine","Methadone","Buprenorphine","Other Opioids"],"Sedatives/Hypnotics/Anxiolytics":["Benzodiazepines (Xanax, Valium, etc.)","Barbiturates","Sleep Medications (Ambien, etc.)","Other Sedatives"],Stimulants:["Cocaine","Crack Cocaine","Amphetamines","Methamphetamine","ADHD Medications (Adderall, etc.)","Other Stimulants"],Tobacco:["Cigarettes","Cigars","Pipe Tobacco","Chewing Tobacco","E-cigarettes/Vaping","Other Tobacco"],Caffeine:["Coffee","Tea","Energy Drinks","Caffeine Pills","Other Caffeine"],"Other/Unknown":["Prescription Drugs (Misused)","Over-the-Counter Drugs (Misused)","Unknown Substance","Other"]},eu=["Oral","Intravenous (IV)","Intranasal (Snorting)","Smoking/Inhalation","Sublingual","Transdermal","Intramuscular","Subcutaneous","Other"],ep=["Daily","Multiple times per day","Weekly","Multiple times per week","Monthly","Occasionally","Binges/Episodes","As needed","Other"];function ej(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||[]),[c,o]=(0,l.useState)(""),[d,h]=(0,l.useState)("");(0,l.useEffect)(()=>{s&&Array.isArray(s)&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let x=e=>{r(s=>s.filter(s=>s.id!==e))},u=(e,s,a)=>{r(i=>i.map(i=>i.id===e?{...i,[s]:a}:i))},p=c&&ex[c]||[];return(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Enhanced Substance Use History"}),(0,i.jsx)(t.SZ,{children:"Detailed substance use assessment using DSM-5 categories for comprehensive evaluation"})]}),(0,i.jsxs)(t.aY,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-slate-50",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Add Substance Use History"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"DSM-5 Category"}),(0,i.jsxs)(E,{value:c,onValueChange:o,children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select category"})}),(0,i.jsx)(R,{children:Object.keys(ex).map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Specific Substance"}),(0,i.jsxs)(E,{value:d,onValueChange:h,disabled:!c,children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select substance"})}),(0,i.jsx)(R,{children:p.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsx)("div",{className:"flex items-end",children:(0,i.jsxs)(m.z,{onClick:()=>{if(!c||!d)return;let e={id:Date.now().toString(),category:c,specificSubstance:d,duration:"",route:"",frequency:"",ageOfFirstUse:"",treatmentHistory:"",notes:""};r(s=>[...s,e]),o(""),h("")},disabled:!c||!d,className:"w-full",children:[(0,i.jsx)(em.Z,{className:"h-4 w-4 mr-2"}),"Add Substance"]})})]})]}),n.length>0&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h4",{className:"font-medium",children:"Current Substance Use History"}),n.map(e=>(0,i.jsxs)(t.Zb,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsx)(t.Ol,{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ed.C,{variant:"outline",children:e.category}),(0,i.jsx)("span",{className:"font-medium",children:e.specificSubstance})]}),(0,i.jsx)(m.z,{variant:"ghost",size:"sm",onClick:()=>x(e.id),className:"text-red-600 hover:text-red-800",children:(0,i.jsx)(eh.Z,{className:"h-4 w-4"})})]})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Duration of Use"}),(0,i.jsx)(w.I,{value:e.duration,onChange:s=>u(e.id,"duration",s.target.value),placeholder:"e.g., 2 years, 6 months"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Route of Administration"}),(0,i.jsxs)(E,{value:e.route,onValueChange:s=>u(e.id,"route",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select route"})}),(0,i.jsx)(R,{children:eu.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Frequency/Pattern"}),(0,i.jsxs)(E,{value:e.frequency,onValueChange:s=>u(e.id,"frequency",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select frequency"})}),(0,i.jsx)(R,{children:ep.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Age of First Use"}),(0,i.jsx)(w.I,{value:e.ageOfFirstUse,onChange:s=>u(e.id,"ageOfFirstUse",s.target.value),placeholder:"e.g., 16",type:"number"})]}),(0,i.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,i.jsx)(A,{children:"Treatment History"}),(0,i.jsx)(w.I,{value:e.treatmentHistory,onChange:s=>u(e.id,"treatmentHistory",s.target.value),placeholder:"Previous treatment attempts, rehab, etc."})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:e.notes,onChange:s=>u(e.id,"notes",s.target.value),placeholder:"Additional details about use patterns, triggers, consequences, etc.",rows:2})]})]})]},e.id))]}),0===n.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-slate-500",children:[(0,i.jsx)("p",{children:"No substance use history recorded."}),(0,i.jsx)("p",{className:"text-sm",children:"Use the form above to add substance use information."})]})]})]})}let eg=["Very Effective","Moderately Effective","Minimally Effective","Ineffective","Unknown"],ev=["Ineffective","Side effects","Patient preference","Cost","Other"];function ey(e){var s;let{data:a,onUpdate:n}=e,[r,c]=(0,l.useState)(()=>a||[]),[o,d]=(0,l.useState)(""),[h,x]=(0,l.useState)("");(0,l.useEffect)(()=>{a&&Array.isArray(a)&&c(a)},[a]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{n(r)},100);return()=>clearTimeout(e)},[r,n]);let u=e=>{c(s=>s.filter(s=>s.id!==e))},p=(e,s,a)=>{c(i=>i.map(i=>i.id===e?{...i,[s]:a}:i))};return(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Medication History"}),(0,i.jsx)(t.SZ,{children:"Track psychiatric medications with effectiveness, side effects, and treatment outcomes"})]}),(0,i.jsxs)(t.aY,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-slate-50",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Add Medication"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Medication Category"}),(0,i.jsxs)(E,{value:h,onValueChange:e=>{x(e),d("")},children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select category first"})}),(0,i.jsx)(R,{children:X.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Specific Medication"}),(0,i.jsxs)(E,{value:o,onValueChange:d,disabled:!h,children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:h?"Select medication":"Select category first"})}),(0,i.jsx)(R,{children:h&&(null===(s=ee[h])||void 0===s?void 0:s.map(e=>(0,i.jsx)(B,{value:e,children:e},e)))})]})]}),(0,i.jsx)("div",{className:"flex items-end",children:(0,i.jsxs)(m.z,{onClick:()=>{if(!o||!h)return;let e={id:Date.now().toString(),medicationName:o,category:h,dosage:"",startDate:"",endDate:"",effectiveness:"",sideEffects:"",discontinuationReason:"",discontinuationOther:"",notes:""};c(s=>[...s,e]),d(""),x("")},disabled:!o||!h,className:"w-full",children:[(0,i.jsx)(em.Z,{className:"h-4 w-4 mr-2"}),"Add Medication"]})})]})]}),r.length>0&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h4",{className:"font-medium",children:"Medication History"}),r.map(e=>(0,i.jsxs)(t.Zb,{className:"border-l-4 border-l-green-500",children:[(0,i.jsx)(t.Ol,{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ed.C,{variant:"outline",children:e.category}),(0,i.jsx)("span",{className:"font-medium",children:e.medicationName}),e.dosage&&(0,i.jsx)(ed.C,{variant:"secondary",children:e.dosage})]}),(0,i.jsx)(m.z,{variant:"ghost",size:"sm",onClick:()=>u(e.id),className:"text-red-600 hover:text-red-800",children:(0,i.jsx)(eh.Z,{className:"h-4 w-4"})})]})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Dosage"}),(0,i.jsx)(w.I,{value:e.dosage,onChange:s=>p(e.id,"dosage",s.target.value),placeholder:"e.g., 10mg daily"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Start Date (Month/Year)"}),(0,i.jsx)(w.I,{value:e.startDate,onChange:s=>p(e.id,"startDate",s.target.value),placeholder:"e.g., 03/2023",type:"month"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"End Date (Month/Year)"}),(0,i.jsx)(w.I,{value:e.endDate,onChange:s=>p(e.id,"endDate",s.target.value),placeholder:"Leave empty if current",type:"month"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Effectiveness"}),(0,i.jsxs)(E,{value:e.effectiveness,onValueChange:s=>p(e.id,"effectiveness",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select effectiveness"})}),(0,i.jsx)(R,{children:eg.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,i.jsx)(A,{children:"Reason for Discontinuation"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsxs)(E,{value:e.discontinuationReason,onValueChange:s=>p(e.id,"discontinuationReason",s),children:[(0,i.jsx)(I,{className:"flex-1",children:(0,i.jsx)(M,{placeholder:"Select reason (if discontinued)"})}),(0,i.jsx)(R,{children:ev.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]}),"Other"===e.discontinuationReason&&(0,i.jsx)(w.I,{value:e.discontinuationOther,onChange:s=>p(e.id,"discontinuationOther",s.target.value),placeholder:"Specify other reason",className:"flex-1"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Side Effects"}),(0,i.jsx)(es,{value:e.sideEffects,onChange:s=>p(e.id,"sideEffects",s.target.value),placeholder:"Describe any side effects experienced",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:e.notes,onChange:s=>p(e.id,"notes",s.target.value),placeholder:"Additional details about medication use, response, etc.",rows:2})]})]})]},e.id))]}),0===r.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-slate-500",children:[(0,i.jsx)("p",{children:"No medication history recorded."}),(0,i.jsx)("p",{className:"text-sm",children:"Use the form above to add medication information."})]})]})]})}let ef=["Diabetes Mellitus","Hypertension","Epilepsy/Seizure Disorder","Head trauma with loss of consciousness","Hypothyroidism","Hyperthyroidism","Cardiovascular disease","Chronic kidney disease","Liver disease","Autoimmune disorders"],eN=["Major Depressive Episode","Manic Episode","Hypomanic Episode","Mixed Episode","Psychotic Episode","Anxiety Episode","Other"],eb=["days","weeks","months","years"],eC=["Mild","Moderate","Severe"],eS=["Medication","Therapy","Hospitalization","None"],ew=["Good","Partial","Poor","Unknown"],ek=["Well-groomed","Disheveled","Appropriate dress","Inappropriate dress","Poor hygiene"],eD=["Calm","Agitated","Restless","Withdrawn","Cooperative","Uncooperative"],eA=["Normal","Rapid","Slow","Pressured"],eP=["Normal","Loud","Soft","Whispered"],eO=["Euthymic","Depressed","Anxious","Irritable","Euphoric","Angry"],eT=["Appropriate","Flat","Blunted","Labile","Inappropriate"],eF=["Linear","Tangential","Circumstantial","Flight of ideas","Loose associations"],eE=["Oriented x3","Oriented x2","Oriented x1","Disoriented"],eM=["Good","Fair","Poor","Absent"],eI=["Good","Fair","Poor","Impaired"];var eL=a(3247),eZ=a(2489);let eR=[{code:"F32.9",name:"Major Depressive Disorder, Single Episode, Unspecified"},{code:"F33.9",name:"Major Depressive Disorder, Recurrent, Unspecified"},{code:"F41.1",name:"Generalized Anxiety Disorder"},{code:"F41.0",name:"Panic Disorder"},{code:"F43.10",name:"Post-Traumatic Stress Disorder, Unspecified"},{code:"F31.9",name:"Bipolar Disorder, Unspecified"},{code:"F20.9",name:"Schizophrenia, Unspecified"},{code:"F42.2",name:"Mixed Obsessional Thoughts and Acts"},{code:"F10.20",name:"Alcohol Use Disorder, Moderate"},{code:"F43.25",name:"Adjustment Disorder with Mixed Anxiety and Depressed Mood"},{code:"F60.3",name:"Borderline Personality Disorder"},{code:"F90.9",name:"Attention-Deficit/Hyperactivity Disorder, Unspecified"}],eB=[{value:"primary",label:"Primary"},{value:"secondary",label:"Secondary"},{value:"rule_out",label:"Rule Out"}];var eH=a(3506),eV=a(4401),eU=a(8985);let ez=eU.fC,eY=eU.wy,eG=eU.Fw;var eq=a(407);function eW(e){var s,a,n,r,c,o,d,m,h,x,u,p,j,g,v,y,f,N,C,S,k,D,P,T,F,E,M,I,L,Z,R,B,H,V,U,z,Y,G,q,W,_,J,K,Q,$,X,ee,ea,ei,el,en,et,er,ec,em,eh,ex,eu,ep,ej,eg;let{data:ev,onUpdate:ey}=e,[ef,eN]=(0,l.useState)(()=>ev||{}),[eb,eC]=(0,l.useState)({thyroid:!1,vitamins:!1,liver:!1,cbc:!1,metabolic:!1,lipid:!1,inflammatory:!1,hormonal:!1});(0,l.useEffect)(()=>{ev&&Object.keys(ev).length>0&&eN(ev)},[ev]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{ey(ef)},100);return()=>clearTimeout(e)},[ef,ey]);let eS=(e,s,a)=>{eN(i=>({...i,[e]:{...i[e],[s]:a}}))},ew=e=>{eC(s=>({...s,[e]:!s[e]}))},ek=(e,s)=>{if(!e||!s)return!1;let a=parseFloat(e);if(isNaN(a))return!1;if(s.includes("-")){let[e,i]=s.split("-").map(e=>parseFloat(e));return a<e||a>i}return s.startsWith("<")?a>=parseFloat(s.substring(1)):!!s.startsWith(">")&&a<=parseFloat(s.substring(1))},eD=(e,s,a,l,n,t)=>{let r=ek(t||"",n);return(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)(A,{className:"text-sm font-medium",children:a}),r&&(0,i.jsx)(eo.Z,{className:"h-4 w-4 text-red-500"}),t&&!r&&(0,i.jsx)(b.Z,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(w.I,{value:t||"",onChange:a=>eS(e,s,a.target.value),placeholder:"Enter value",className:r?"border-red-300 focus:border-red-500":""}),(0,i.jsx)(ed.C,{variant:"outline",className:"text-xs whitespace-nowrap",children:l})]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:["Normal: ",n," ",l]})]})};return(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Comprehensive Blood Work Panel"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Enter laboratory test results with automatic abnormal value detection"})]}),(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:eb.cbc,onOpenChange:()=>ew("cbc"),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Complete Blood Count (CBC)"}),(0,i.jsx)(t.SZ,{children:"Hemoglobin, Hematocrit, WBC with differential, Platelets, RBC indices"})]}),eb.cbc?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:(null===(s=ef.completeBloodCount)||void 0===s?void 0:s.datePerformed)||"",onChange:e=>eS("completeBloodCount","datePerformed",e.target.value)})]})}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Basic Parameters"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[eD("completeBloodCount","hemoglobin","Hemoglobin","g/dL","12.0-16.0",null===(a=ef.completeBloodCount)||void 0===a?void 0:a.hemoglobin),eD("completeBloodCount","hematocrit","Hematocrit","%","36-46",null===(n=ef.completeBloodCount)||void 0===n?void 0:n.hematocrit),eD("completeBloodCount","wbc","WBC Count","K/uL","4.5-11.0",null===(r=ef.completeBloodCount)||void 0===r?void 0:r.wbc),eD("completeBloodCount","platelets","Platelets","K/uL","150-450",null===(c=ef.completeBloodCount)||void 0===c?void 0:c.platelets)]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"WBC Differential"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[eD("completeBloodCount","neutrophils","Neutrophils","%","50-70",null===(o=ef.completeBloodCount)||void 0===o?void 0:o.neutrophils),eD("completeBloodCount","lymphocytes","Lymphocytes","%","20-40",null===(d=ef.completeBloodCount)||void 0===d?void 0:d.lymphocytes),eD("completeBloodCount","monocytes","Monocytes","%","2-8",null===(m=ef.completeBloodCount)||void 0===m?void 0:m.monocytes),eD("completeBloodCount","eosinophils","Eosinophils","%","1-4",null===(h=ef.completeBloodCount)||void 0===h?void 0:h.eosinophils),eD("completeBloodCount","basophils","Basophils","%","0.5-1",null===(x=ef.completeBloodCount)||void 0===x?void 0:x.basophils)]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"RBC Indices"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[eD("completeBloodCount","mcv","MCV","fL","80-100",null===(u=ef.completeBloodCount)||void 0===u?void 0:u.mcv),eD("completeBloodCount","mch","MCH","pg","27-32",null===(p=ef.completeBloodCount)||void 0===p?void 0:p.mch),eD("completeBloodCount","mchc","MCHC","g/dL","32-36",null===(j=ef.completeBloodCount)||void 0===j?void 0:j.mchc)]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:(null===(g=ef.completeBloodCount)||void 0===g?void 0:g.interpretation)||"",onChange:e=>eS("completeBloodCount","interpretation",e.target.value),placeholder:"Enter clinical interpretation of CBC results",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(v=ef.completeBloodCount)||void 0===v?void 0:v.notes)||"",onChange:e=>eS("completeBloodCount","notes",e.target.value),placeholder:"Additional notes about CBC",rows:2})]})]})})]})}),(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:eb.metabolic,onOpenChange:()=>ew("metabolic"),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Metabolic Panel (CMP)"}),(0,i.jsx)(t.SZ,{children:"Glucose, HbA1c, Electrolytes, Kidney function, Lipids"})]}),eb.metabolic?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:(null===(y=ef.metabolicPanel)||void 0===y?void 0:y.datePerformed)||"",onChange:e=>eS("metabolicPanel","datePerformed",e.target.value)})]})}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Glucose & Diabetes Markers"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[eD("metabolicPanel","glucose","Fasting Glucose","mg/dL","70-100",null===(f=ef.metabolicPanel)||void 0===f?void 0:f.glucose),eD("metabolicPanel","hba1c","HbA1c","%","<5.7",null===(N=ef.metabolicPanel)||void 0===N?void 0:N.hba1c)]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Kidney Function"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[eD("metabolicPanel","creatinine","Creatinine","mg/dL","0.6-1.2",null===(C=ef.metabolicPanel)||void 0===C?void 0:C.creatinine),eD("metabolicPanel","bun","BUN","mg/dL","7-20",null===(S=ef.metabolicPanel)||void 0===S?void 0:S.bun),eD("metabolicPanel","egfr","eGFR","mL/min/1.73m\xb2",">60",null===(k=ef.metabolicPanel)||void 0===k?void 0:k.egfr)]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Electrolytes"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[eD("metabolicPanel","sodium","Sodium","mEq/L","136-145",null===(D=ef.metabolicPanel)||void 0===D?void 0:D.sodium),eD("metabolicPanel","potassium","Potassium","mEq/L","3.5-5.0",null===(P=ef.metabolicPanel)||void 0===P?void 0:P.potassium),eD("metabolicPanel","chloride","Chloride","mEq/L","98-107",null===(T=ef.metabolicPanel)||void 0===T?void 0:T.chloride),eD("metabolicPanel","co2","CO2","mEq/L","22-28",null===(F=ef.metabolicPanel)||void 0===F?void 0:F.co2)]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:eD("metabolicPanel","anionGap","Anion Gap","mEq/L","8-16",null===(E=ef.metabolicPanel)||void 0===E?void 0:E.anionGap)})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:(null===(M=ef.metabolicPanel)||void 0===M?void 0:M.interpretation)||"",onChange:e=>eS("metabolicPanel","interpretation",e.target.value),placeholder:"Enter clinical interpretation of metabolic panel results",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(I=ef.metabolicPanel)||void 0===I?void 0:I.notes)||"",onChange:e=>eS("metabolicPanel","notes",e.target.value),placeholder:"Additional notes about metabolic panel",rows:2})]})]})})]})}),(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:eb.lipid,onOpenChange:()=>ew("lipid"),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Lipid Panel"}),(0,i.jsx)(t.SZ,{children:"Total Cholesterol, LDL, HDL, Triglycerides"})]}),eb.lipid?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:(null===(L=ef.lipidPanel)||void 0===L?void 0:L.datePerformed)||"",onChange:e=>eS("lipidPanel","datePerformed",e.target.value)})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[eD("lipidPanel","totalCholesterol","Total Cholesterol","mg/dL","<200",null===(Z=ef.lipidPanel)||void 0===Z?void 0:Z.totalCholesterol),eD("lipidPanel","ldl","LDL Cholesterol","mg/dL","<100",null===(R=ef.lipidPanel)||void 0===R?void 0:R.ldl),eD("lipidPanel","hdl","HDL Cholesterol","mg/dL",">40",null===(B=ef.lipidPanel)||void 0===B?void 0:B.hdl),eD("lipidPanel","triglycerides","Triglycerides","mg/dL","<150",null===(H=ef.lipidPanel)||void 0===H?void 0:H.triglycerides)]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:(null===(V=ef.lipidPanel)||void 0===V?void 0:V.interpretation)||"",onChange:e=>eS("lipidPanel","interpretation",e.target.value),placeholder:"Enter clinical interpretation of lipid panel results",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(U=ef.lipidPanel)||void 0===U?void 0:U.notes)||"",onChange:e=>eS("lipidPanel","notes",e.target.value),placeholder:"Additional notes about lipid panel",rows:2})]})]})})]})}),(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:eb.liver,onOpenChange:()=>ew("liver"),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Liver Function Tests (LFT)"}),(0,i.jsx)(t.SZ,{children:"ALT, AST, Bilirubin, Alkaline Phosphatase, GGT, Albumin, PT/INR"})]}),eb.liver?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:(null===(z=ef.liverFunction)||void 0===z?void 0:z.datePerformed)||"",onChange:e=>eS("liverFunction","datePerformed",e.target.value)})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[eD("liverFunction","alt","ALT","U/L","7-56",null===(Y=ef.liverFunction)||void 0===Y?void 0:Y.alt),eD("liverFunction","ast","AST","U/L","10-40",null===(G=ef.liverFunction)||void 0===G?void 0:G.ast),eD("liverFunction","totalBilirubin","Total Bilirubin","mg/dL","0.2-1.2",null===(q=ef.liverFunction)||void 0===q?void 0:q.totalBilirubin),eD("liverFunction","directBilirubin","Direct Bilirubin","mg/dL","0.0-0.3",null===(W=ef.liverFunction)||void 0===W?void 0:W.directBilirubin),eD("liverFunction","alkalinePhosphatase","Alkaline Phosphatase","U/L","44-147",null===(_=ef.liverFunction)||void 0===_?void 0:_.alkalinePhosphatase),eD("liverFunction","ggt","GGT","U/L","9-48",null===(J=ef.liverFunction)||void 0===J?void 0:J.ggt),eD("liverFunction","albumin","Albumin","g/dL","3.5-5.0",null===(K=ef.liverFunction)||void 0===K?void 0:K.albumin),eD("liverFunction","ptInr","PT/INR","ratio","0.8-1.1",null===(Q=ef.liverFunction)||void 0===Q?void 0:Q.ptInr)]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:(null===($=ef.liverFunction)||void 0===$?void 0:$.interpretation)||"",onChange:e=>eS("liverFunction","interpretation",e.target.value),placeholder:"Enter clinical interpretation of liver function results",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(X=ef.liverFunction)||void 0===X?void 0:X.notes)||"",onChange:e=>eS("liverFunction","notes",e.target.value),placeholder:"Additional notes about liver function tests",rows:2})]})]})})]})}),(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:eb.thyroid,onOpenChange:()=>ew("thyroid"),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Thyroid Function Tests"}),(0,i.jsx)(t.SZ,{children:"TSH, Free T3, Free T4, Reverse T3"})]}),eb.thyroid?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:(null===(ee=ef.thyroidFunction)||void 0===ee?void 0:ee.datePerformed)||"",onChange:e=>eS("thyroidFunction","datePerformed",e.target.value)})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[eD("thyroidFunction","tsh","TSH","mIU/L","0.4-4.0",null===(ea=ef.thyroidFunction)||void 0===ea?void 0:ea.tsh),eD("thyroidFunction","freeT3","Free T3","pg/mL","2.3-4.2",null===(ei=ef.thyroidFunction)||void 0===ei?void 0:ei.freeT3),eD("thyroidFunction","freeT4","Free T4","ng/dL","0.8-1.8",null===(el=ef.thyroidFunction)||void 0===el?void 0:el.freeT4),eD("thyroidFunction","reverseT3","Reverse T3","ng/dL","8-25",null===(en=ef.thyroidFunction)||void 0===en?void 0:en.reverseT3)]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:(null===(et=ef.thyroidFunction)||void 0===et?void 0:et.interpretation)||"",onChange:e=>eS("thyroidFunction","interpretation",e.target.value),placeholder:"Enter clinical interpretation of thyroid function results",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(er=ef.thyroidFunction)||void 0===er?void 0:er.notes)||"",onChange:e=>eS("thyroidFunction","notes",e.target.value),placeholder:"Additional notes about thyroid function tests",rows:2})]})]})})]})}),(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:eb.vitamins,onOpenChange:()=>ew("vitamins"),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Vitamin Levels"}),(0,i.jsx)(t.SZ,{children:"B12, D3, Folate, B1 (Thiamine), B6"})]}),eb.vitamins?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:(null===(ec=ef.vitaminLevels)||void 0===ec?void 0:ec.datePerformed)||"",onChange:e=>eS("vitaminLevels","datePerformed",e.target.value)})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[eD("vitaminLevels","vitaminB12","Vitamin B12","pg/mL","300-900",null===(em=ef.vitaminLevels)||void 0===em?void 0:em.vitaminB12),eD("vitaminLevels","vitaminD3","Vitamin D3","ng/mL","30-100",null===(eh=ef.vitaminLevels)||void 0===eh?void 0:eh.vitaminD3),eD("vitaminLevels","folate","Folate","ng/mL","3-17",null===(ex=ef.vitaminLevels)||void 0===ex?void 0:ex.folate),eD("vitaminLevels","thiamine","Thiamine (B1)","nmol/L","70-180",null===(eu=ef.vitaminLevels)||void 0===eu?void 0:eu.thiamine),eD("vitaminLevels","vitaminB6","Vitamin B6","ng/mL","5-50",null===(ep=ef.vitaminLevels)||void 0===ep?void 0:ep.vitaminB6)]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:(null===(ej=ef.vitaminLevels)||void 0===ej?void 0:ej.interpretation)||"",onChange:e=>eS("vitaminLevels","interpretation",e.target.value),placeholder:"Enter clinical interpretation of vitamin level results",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(eg=ef.vitaminLevels)||void 0===eg?void 0:eg.notes)||"",onChange:e=>eS("vitaminLevels","notes",e.target.value),placeholder:"Additional notes about vitamin levels",rows:2})]})]})})]})})]})}let e_=[{key:"cannabis",label:"Cannabis (THC/CBD)"},{key:"cocaine",label:"Cocaine"},{key:"amphetamines",label:"Amphetamines/Methamphetamines"},{key:"opiates",label:"Opiates (Morphine, Codeine, Heroin)"},{key:"oxycodone",label:"Oxycodone/Oxymorphone"},{key:"benzodiazepines",label:"Benzodiazepines"},{key:"barbiturates",label:"Barbiturates"},{key:"pcp",label:"Phencyclidine (PCP)"},{key:"alcohol",label:"Alcohol/Ethanol"},{key:"syntheticDrugs",label:"Synthetic Drugs (K2/Spice, Bath salts)"},{key:"prescriptionDrugs",label:"Prescription Drug Misuse"},{key:"other",label:"Other Substances"}];function eJ(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||{substances:{}});(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let c=(e,s)=>{r(a=>({...a,substances:{...a.substances,[e]:s}}))},o=(e,s)=>{r(a=>({...a,[e]:s}))},d=()=>n.substances?Object.values(n.substances).filter(Boolean).length:0;return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Toxicology Screening"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Simple substance screening checklist"})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Substance Screening"}),(0,i.jsxs)(t.SZ,{children:["Check substances that were tested or are of clinical concern",d()>0&&(0,i.jsxs)(ed.C,{variant:"outline",className:"ml-2",children:[d()," selected"]})]})]}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:n.datePerformed||"",onChange:e=>o("datePerformed",e.target.value)})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Substances"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:e_.map(e=>{var s;return(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{id:e.key,checked:(null===(s=n.substances)||void 0===s?void 0:s[e.key])||!1,onCheckedChange:s=>c(e.key,s)}),(0,i.jsx)(A,{htmlFor:e.key,className:"text-sm font-normal cursor-pointer",children:e.label})]},e.key)})})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Comments & Notes"}),(0,i.jsx)(es,{value:n.comments||"",onChange:e=>o("comments",e.target.value),placeholder:"Enter any additional comments, test results, levels detected, or clinical notes...",rows:4})]})]})]})]})}var eK=a(3618);let eQ=[{name:"PHQ-9",fullName:"Patient Health Questionnaire-9",category:"Depression",description:"9-item depression screening tool",scoringRange:"0-27",interpretation:{"0-4":"Minimal depression","5-9":"Mild depression","10-14":"Moderate depression","15-19":"Moderately severe depression","20-27":"Severe depression"}},{name:"GAD-7",fullName:"Generalized Anxiety Disorder 7-item",category:"Anxiety",description:"7-item anxiety screening tool",scoringRange:"0-21",interpretation:{"0-4":"Minimal anxiety","5-9":"Mild anxiety","10-14":"Moderate anxiety","15-21":"Severe anxiety"}},{name:"MMSE",fullName:"Mini-Mental State Examination",category:"Cognitive",description:"Cognitive function screening",scoringRange:"0-30",interpretation:{"24-30":"Normal cognition","18-23":"Mild cognitive impairment","0-17":"Severe cognitive impairment"}},{name:"Beck Depression Inventory",fullName:"Beck Depression Inventory (BDI-II)",category:"Depression",description:"21-item depression assessment",scoringRange:"0-63",interpretation:{"0-13":"Minimal depression","14-19":"Mild depression","20-28":"Moderate depression","29-63":"Severe depression"}},{name:"Beck Anxiety Inventory",fullName:"Beck Anxiety Inventory (BAI)",category:"Anxiety",description:"21-item anxiety assessment",scoringRange:"0-63",interpretation:{"0-7":"Minimal anxiety","8-15":"Mild anxiety","16-25":"Moderate anxiety","26-63":"Severe anxiety"}},{name:"DASS-21",fullName:"Depression, Anxiety and Stress Scale",category:"Multi-domain",description:"21-item assessment of depression, anxiety, and stress",scoringRange:"0-63 (each subscale 0-21)",interpretation:{Depression:"Normal: 0-4, Mild: 5-6, Moderate: 7-10, Severe: 11-13, Extremely severe: 14+",Anxiety:"Normal: 0-3, Mild: 4-5, Moderate: 6-7, Severe: 8-9, Extremely severe: 10+",Stress:"Normal: 0-7, Mild: 8-9, Moderate: 10-12, Severe: 13-16, Extremely severe: 17+"}},{name:"MOCA",fullName:"Montreal Cognitive Assessment",category:"Cognitive",description:"Cognitive screening for mild cognitive impairment",scoringRange:"0-30",interpretation:{"26-30":"Normal cognition","18-25":"Mild cognitive impairment","0-17":"Moderate to severe cognitive impairment"}},{name:"Y-BOCS",fullName:"Yale-Brown Obsessive Compulsive Scale",category:"OCD",description:"10-item OCD severity assessment",scoringRange:"0-40",interpretation:{"0-7":"Subclinical","8-15":"Mild","16-23":"Moderate","24-31":"Severe","32-40":"Extreme"}}];function e$(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||{psychologicalAssessments:{}}),[c,o]=(0,l.useState)({});(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let d=(e,s,a)=>{r(i=>({...i,psychologicalAssessments:{...i.psychologicalAssessments,[e]:{...i.psychologicalAssessments[e],testName:e,[s]:a}}}))},h=e=>{r(s=>{let a={...s.psychologicalAssessments};return delete a[e],{...s,psychologicalAssessments:a}}),o(s=>{let a={...s};return delete a[e],a})},u=e=>{o(s=>({...s,[e]:!s[e]}))},p=(e,s)=>{let a=eQ.find(s=>s.name===e);if(!a||!a.interpretation)return"";for(let[e,i]of Object.entries(a.interpretation))if(e.includes("-")){let[a,l]=e.split("-").map(Number);if(s>=a&&s<=l)return i}else if(e.includes("+")&&s>=parseInt(e.replace("+","")))return i;return""},j=e=>{let s=e.toLowerCase();return s.includes("severe")||s.includes("extreme")?"text-red-600":s.includes("moderate")?"text-orange-600":s.includes("mild")?"text-yellow-600":s.includes("minimal")||s.includes("normal")?"text-green-600":"text-gray-600"},g=eQ.filter(e=>!n.psychologicalAssessments[e.name]);return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Psychological Assessment Tools"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Standardized psychological testing and assessment instruments"})]}),g.length>0&&(0,i.jsxs)(E,{onValueChange:e=>{eQ.find(s=>s.name===e),r(s=>({...s,psychologicalAssessments:{...s.psychologicalAssessments,[e]:{testName:e,isCompleted:!1,datePerformed:new Date().toISOString().split("T")[0]}}})),o(s=>({...s,[e]:!0}))},children:[(0,i.jsx)(I,{className:"w-64",children:(0,i.jsx)(M,{placeholder:"Add assessment tool..."})}),(0,i.jsx)(R,{children:g.map(e=>(0,i.jsx)(B,{value:e.name,children:(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"font-medium",children:e.name}),(0,i.jsx)("span",{className:"text-xs text-slate-500",children:e.fullName})]})},e.name))})]})]}),0===Object.keys(n.psychologicalAssessments).length&&(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(t.aY,{className:"text-center py-8 text-slate-500",children:[(0,i.jsx)(x.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,i.jsx)("p",{children:"No psychological assessments added yet."}),(0,i.jsx)("p",{className:"text-sm mt-2",children:"Use the dropdown above to add standardized assessment tools."})]})}),Object.entries(n.psychologicalAssessments).map(e=>{let[s,a]=e,l=eQ.find(e=>e.name===s),n=a.totalScore?p(s,a.totalScore):"";return(0,i.jsx)(t.Zb,{children:(0,i.jsxs)(ez,{open:c[s],onOpenChange:()=>u(s),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{className:"flex items-center space-x-3",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)(t.ll,{className:"text-lg flex items-center gap-2",children:[s,a.isCompleted&&(0,i.jsx)(b.Z,{className:"h-5 w-5 text-green-500"})]}),(0,i.jsxs)(t.SZ,{children:[null==l?void 0:l.fullName," • ",null==l?void 0:l.category,a.totalScore&&(0,i.jsxs)("span",{className:"ml-2",children:["Score: ",a.totalScore,n&&(0,i.jsxs)("span",{className:"ml-1 font-medium ".concat(j(n)),children:["(",n,")"]})]})]})]})}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(m.z,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),h(s)},className:"text-red-500 hover:text-red-700",children:(0,i.jsx)(eh.Z,{className:"h-4 w-4"})}),c[s]?(0,i.jsx)(O.Z,{className:"h-5 w-5"}):(0,i.jsx)(eq.Z,{className:"h-5 w-5"})]})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:a.datePerformed||"",onChange:e=>d(s,"datePerformed",e.target.value)})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Total Score"}),(0,i.jsx)(w.I,{type:"number",value:a.totalScore||"",onChange:e=>d(s,"totalScore",parseInt(e.target.value)||0),placeholder:"Range: ".concat((null==l?void 0:l.scoringRange)||"N/A")})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Completion Status"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 pt-2",children:[(0,i.jsx)(ei,{checked:a.isCompleted,onCheckedChange:e=>d(s,"isCompleted",e)}),(0,i.jsx)("span",{className:"text-sm",children:"Assessment completed"})]})]})]}),n&&(0,i.jsx)("div",{className:"p-3 bg-slate-50 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eK.Z,{className:"h-4 w-4 text-slate-600"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Interpretation:"}),(0,i.jsx)("span",{className:"text-sm font-medium ".concat(j(n)),children:n})]})}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Clinical Interpretation"}),(0,i.jsx)(es,{value:a.interpretation||"",onChange:e=>d(s,"interpretation",e.target.value),placeholder:"Enter clinical interpretation and context",rows:3})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:a.notes||"",onChange:e=>d(s,"notes",e.target.value),placeholder:"Additional notes, observations, or context",rows:2})]}),l&&(0,i.jsx)("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("div",{className:"font-medium text-blue-900 mb-1",children:"Assessment Information"}),(0,i.jsxs)("div",{className:"text-blue-700",children:[(0,i.jsx)("div",{children:l.description}),(0,i.jsxs)("div",{className:"mt-1",children:["Scoring Range: ",l.scoringRange]})]})]})})]})})]})},s)})]})}var eX=a(1239);let e0=[{name:"Brain MRI",category:"Neuroimaging",description:"Magnetic Resonance Imaging of the brain",commonFindings:["Normal","White matter changes","Atrophy","Lesions","Vascular changes"]},{name:"Brain CT",category:"Neuroimaging",description:"Computed Tomography of the brain",commonFindings:["Normal","Atrophy","Hemorrhage","Infarct","Mass effect"]},{name:"EEG",category:"Neurophysiology",description:"Electroencephalogram",commonFindings:["Normal","Generalized slowing","Focal abnormalities","Epileptiform activity","Sleep abnormalities"]},{name:"SPECT",category:"Nuclear Medicine",description:"Single Photon Emission Computed Tomography",commonFindings:["Normal perfusion","Hypoperfusion","Hyperperfusion","Asymmetric perfusion"]},{name:"PET Scan",category:"Nuclear Medicine",description:"Positron Emission Tomography",commonFindings:["Normal metabolism","Hypometabolism","Hypermetabolism","Alzheimer pattern"]},{name:"fMRI",category:"Functional Imaging",description:"Functional Magnetic Resonance Imaging",commonFindings:["Normal activation","Altered activation patterns","Connectivity changes"]},{name:"DTI",category:"Advanced MRI",description:"Diffusion Tensor Imaging",commonFindings:["Normal white matter integrity","Reduced FA","Increased diffusivity","Tract disruption"]},{name:"Neuropsychological Testing",category:"Cognitive Assessment",description:"Comprehensive cognitive evaluation",commonFindings:["Normal cognition","Memory impairment","Executive dysfunction","Language deficits","Attention deficits"]}],e2=[{name:"EMG",category:"Neurophysiology",description:"Electromyography",commonFindings:["Normal","Myopathic changes","Neuropathic changes","Denervation"]},{name:"Nerve Conduction Study",category:"Neurophysiology",description:"Peripheral nerve function assessment",commonFindings:["Normal","Demyelinating","Axonal","Mixed pattern"]},{name:"Evoked Potentials",category:"Neurophysiology",description:"Visual, auditory, or somatosensory evoked potentials",commonFindings:["Normal latencies","Prolonged latencies","Reduced amplitudes","Absent responses"]},{name:"Sleep Study",category:"Sleep Medicine",description:"Polysomnography",commonFindings:["Normal sleep","Sleep apnea","Periodic limb movements","REM abnormalities","Insomnia"]}];function e4(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||{imagingStudies:{},neurologicalTests:{}}),[c,o]=(0,l.useState)({});(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let d=(e,s,a)=>{r(i=>({...i,imagingStudies:{...i.imagingStudies,[e]:{...i.imagingStudies[e],testName:e,[s]:a}}}))},h=(e,s,a)=>{r(i=>({...i,neurologicalTests:{...i.neurologicalTests,[e]:{...i.neurologicalTests[e],testName:e,[s]:a}}}))},u=e=>{r(s=>{let a={...s.imagingStudies};return delete a[e],{...s,imagingStudies:a}}),o(s=>{let a={...s};return delete a["imaging-".concat(e)],a})},p=e=>{r(s=>{let a={...s.neurologicalTests};return delete a[e],{...s,neurologicalTests:a}}),o(s=>{let a={...s};return delete a["neuro-".concat(e)],a})},j=e=>{o(s=>({...s,[e]:!s[e]}))},g=e0.filter(e=>!n.imagingStudies[e.name]),v=e2.filter(e=>!n.neurologicalTests[e.name]);return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Imaging Studies & Neurological Tests"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Neuroimaging, EEG, and other diagnostic procedures"})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)(t.ll,{className:"flex items-center gap-2",children:[(0,i.jsx)(x.Z,{className:"h-5 w-5"}),"Imaging Studies"]}),(0,i.jsx)(t.SZ,{children:"Neuroimaging and brain imaging studies"})]}),g.length>0&&(0,i.jsxs)(E,{onValueChange:e=>{let s=e0.find(s=>s.name===e);r(a=>({...a,imagingStudies:{...a.imagingStudies,[e]:{testName:e,category:(null==s?void 0:s.category)||"",isNormal:!0,isUrgent:!1,followUpRequired:!1,datePerformed:new Date().toISOString().split("T")[0]}}})),o(s=>({...s,["imaging-".concat(e)]:!0}))},children:[(0,i.jsx)(I,{className:"w-48",children:(0,i.jsx)(M,{placeholder:"Add imaging study..."})}),(0,i.jsx)(R,{children:g.map(e=>(0,i.jsx)(B,{value:e.name,children:(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"font-medium",children:e.name}),(0,i.jsx)("span",{className:"text-xs text-slate-500",children:e.category})]})},e.name))})]})]})}),(0,i.jsx)(t.aY,{children:0===Object.keys(n.imagingStudies).length?(0,i.jsxs)("div",{className:"text-center py-6 text-slate-500",children:[(0,i.jsx)(eV.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,i.jsx)("p",{className:"text-sm",children:"No imaging studies added yet."})]}):(0,i.jsx)("div",{className:"space-y-4",children:Object.entries(n.imagingStudies).map(e=>{let[s,a]=e,l="imaging-".concat(s),n=e0.find(e=>e.name===s);return(0,i.jsx)(t.Zb,{className:"border-l-4 border-l-blue-500",children:(0,i.jsxs)(ez,{open:c[l],onOpenChange:()=>j(l),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors py-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{className:"flex items-center space-x-3",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)(t.ll,{className:"text-base flex items-center gap-2",children:[s,a.isUrgent&&(0,i.jsx)(eo.Z,{className:"h-4 w-4 text-red-500"}),!a.isNormal&&(0,i.jsx)(ed.C,{variant:"destructive",className:"text-xs",children:"Abnormal"}),a.followUpRequired&&(0,i.jsx)(ed.C,{variant:"outline",className:"text-xs",children:"Follow-up"})]}),(0,i.jsxs)(t.SZ,{className:"text-sm",children:[a.category," • ",a.datePerformed||"Date not set"]})]})}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(m.z,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),u(s)},className:"text-red-500 hover:text-red-700",children:(0,i.jsx)(eh.Z,{className:"h-4 w-4"})}),c[l]?(0,i.jsx)(O.Z,{className:"h-4 w-4"}):(0,i.jsx)(eq.Z,{className:"h-4 w-4"})]})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:a.datePerformed||"",onChange:e=>d(s,"datePerformed",e.target.value)})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Facility/Location"}),(0,i.jsx)(w.I,{value:a.facility||"",onChange:e=>d(s,"facility",e.target.value),placeholder:"Hospital/clinic name"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{checked:a.isNormal,onCheckedChange:e=>d(s,"isNormal",e)}),(0,i.jsx)(A,{className:"text-sm",children:"Normal findings"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{checked:a.isUrgent,onCheckedChange:e=>d(s,"isUrgent",e)}),(0,i.jsx)(A,{className:"text-sm",children:"Urgent/Critical"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{checked:a.followUpRequired,onCheckedChange:e=>d(s,"followUpRequired",e)}),(0,i.jsx)(A,{className:"text-sm",children:"Follow-up required"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Findings"}),(0,i.jsx)(es,{value:a.findings||"",onChange:e=>d(s,"findings",e.target.value),placeholder:"Detailed findings and observations",rows:3})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Impression/Conclusion"}),(0,i.jsx)(es,{value:a.impression||"",onChange:e=>d(s,"impression",e.target.value),placeholder:"Radiologist's impression and clinical correlation",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:a.notes||"",onChange:e=>d(s,"notes",e.target.value),placeholder:"Additional clinical notes or context",rows:2})]}),n&&(0,i.jsx)("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("div",{className:"font-medium text-blue-900 mb-1",children:"Study Information"}),(0,i.jsx)("div",{className:"text-blue-700",children:n.description}),n.commonFindings&&(0,i.jsxs)("div",{className:"mt-2",children:[(0,i.jsx)("span",{className:"font-medium",children:"Common findings: "}),n.commonFindings.join(", ")]})]})})]})})]})},s)})})})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)(t.ll,{className:"flex items-center gap-2",children:[(0,i.jsx)(eX.Z,{className:"h-5 w-5"}),"Neurological Tests"]}),(0,i.jsx)(t.SZ,{children:"Neurophysiology and specialized neurological assessments"})]}),v.length>0&&(0,i.jsxs)(E,{onValueChange:e=>{let s=e2.find(s=>s.name===e);r(a=>({...a,neurologicalTests:{...a.neurologicalTests,[e]:{testName:e,category:(null==s?void 0:s.category)||"",isNormal:!0,followUpRequired:!1,datePerformed:new Date().toISOString().split("T")[0]}}})),o(s=>({...s,["neuro-".concat(e)]:!0}))},children:[(0,i.jsx)(I,{className:"w-48",children:(0,i.jsx)(M,{placeholder:"Add neurological test..."})}),(0,i.jsx)(R,{children:v.map(e=>(0,i.jsx)(B,{value:e.name,children:(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"font-medium",children:e.name}),(0,i.jsx)("span",{className:"text-xs text-slate-500",children:e.category})]})},e.name))})]})]})}),(0,i.jsx)(t.aY,{children:0===Object.keys(n.neurologicalTests).length?(0,i.jsxs)("div",{className:"text-center py-6 text-slate-500",children:[(0,i.jsx)(eX.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,i.jsx)("p",{className:"text-sm",children:"No neurological tests added yet."})]}):(0,i.jsx)("div",{className:"space-y-4",children:Object.entries(n.neurologicalTests).map(e=>{let[s,a]=e,l="neuro-".concat(s),n=e2.find(e=>e.name===s);return(0,i.jsx)(t.Zb,{className:"border-l-4 border-l-green-500",children:(0,i.jsxs)(ez,{open:c[l],onOpenChange:()=>j(l),children:[(0,i.jsx)(eY,{asChild:!0,children:(0,i.jsx)(t.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors py-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{className:"flex items-center space-x-3",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)(t.ll,{className:"text-base flex items-center gap-2",children:[s,!a.isNormal&&(0,i.jsx)(ed.C,{variant:"destructive",className:"text-xs",children:"Abnormal"}),a.followUpRequired&&(0,i.jsx)(ed.C,{variant:"outline",className:"text-xs",children:"Follow-up"})]}),(0,i.jsxs)(t.SZ,{className:"text-sm",children:[a.category," • ",a.datePerformed||"Date not set"]})]})}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(m.z,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),p(s)},className:"text-red-500 hover:text-red-700",children:(0,i.jsx)(eh.Z,{className:"h-4 w-4"})}),c[l]?(0,i.jsx)(O.Z,{className:"h-4 w-4"}):(0,i.jsx)(eq.Z,{className:"h-4 w-4"})]})]})})}),(0,i.jsx)(eG,{children:(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Date Performed"}),(0,i.jsx)(w.I,{type:"date",value:a.datePerformed||"",onChange:e=>h(s,"datePerformed",e.target.value)})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Facility/Location"}),(0,i.jsx)(w.I,{value:a.facility||"",onChange:e=>h(s,"facility",e.target.value),placeholder:"Hospital/clinic name"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{checked:a.isNormal,onCheckedChange:e=>h(s,"isNormal",e)}),(0,i.jsx)(A,{className:"text-sm",children:"Normal results"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{checked:a.followUpRequired,onCheckedChange:e=>h(s,"followUpRequired",e)}),(0,i.jsx)(A,{className:"text-sm",children:"Follow-up required"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Results"}),(0,i.jsx)(es,{value:a.results||"",onChange:e=>h(s,"results",e.target.value),placeholder:"Detailed test results and measurements",rows:3})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Interpretation"}),(0,i.jsx)(es,{value:a.interpretation||"",onChange:e=>h(s,"interpretation",e.target.value),placeholder:"Clinical interpretation and significance",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:a.notes||"",onChange:e=>h(s,"notes",e.target.value),placeholder:"Additional clinical notes or context",rows:2})]}),n&&(0,i.jsx)("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("div",{className:"font-medium text-green-900 mb-1",children:"Test Information"}),(0,i.jsx)("div",{className:"text-green-700",children:n.description}),n.commonFindings&&(0,i.jsxs)("div",{className:"mt-2",children:[(0,i.jsx)("span",{className:"font-medium",children:"Common findings: "}),n.commonFindings.join(", ")]})]})})]})})]})},s)})})})]})]})}let e1=[{id:"demographics",label:"Demographics",icon:h.Z,component:function(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||{});(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let c=(e,s)=>{r(a=>({...a,[e]:s}))},o=e=>{let s=new Date,a=new Date(e),i=s.getFullYear()-a.getFullYear(),l=s.getMonth()-a.getMonth();return(l<0||0===l&&s.getDate()<a.getDate())&&i--,i},d=e=>{c("dateOfBirth",e),e&&c("age",o(e))};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Demographics Information"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Please provide basic demographic information using anonymous patient codes for privacy."})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Patient Identification"}),(0,i.jsx)(t.SZ,{children:"Use anonymous patient codes to maintain privacy"})]}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"patientCode",children:"Anonymous Patient Code"}),(0,i.jsx)(w.I,{id:"patientCode",value:n.patientCode||"",onChange:e=>c("patientCode",e.target.value),placeholder:"Enter anonymous patient code (e.g., PT-2024-001)",className:"font-mono"}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"Use a pre-generated anonymous code to identify this patient while maintaining privacy"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,i.jsx)(w.I,{id:"dateOfBirth",type:"date",value:n.dateOfBirth||"",onChange:e=>d(e.target.value)})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"age",children:"Age"}),(0,i.jsx)(w.I,{id:"age",type:"number",value:n.age||"",onChange:e=>c("age",parseInt(e.target.value)||0),placeholder:"Age",readOnly:!!n.dateOfBirth})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"gender",children:"Gender"}),(0,i.jsxs)(E,{value:n.gender||"",onValueChange:e=>c("gender",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select gender"})}),(0,i.jsx)(R,{children:Y.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"General Location"}),(0,i.jsx)(t.SZ,{children:"General location information for demographic analysis (no specific addresses)"})]}),(0,i.jsx)(t.aY,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"generalLocation",children:"General Location Type"}),(0,i.jsxs)(E,{value:n.generalLocation||"",onValueChange:e=>c("generalLocation",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select location type"})}),(0,i.jsxs)(R,{children:[(0,i.jsx)(B,{value:"urban",children:"Urban"}),(0,i.jsx)(B,{value:"suburban",children:"Suburban"}),(0,i.jsx)(B,{value:"rural",children:"Rural"}),(0,i.jsx)(B,{value:"other",children:"Other"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"region",children:"General Region"}),(0,i.jsxs)(E,{value:n.region||"",onValueChange:e=>c("region",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select region"})}),(0,i.jsxs)(R,{children:[(0,i.jsx)(B,{value:"northeast",children:"Northeast"}),(0,i.jsx)(B,{value:"southeast",children:"Southeast"}),(0,i.jsx)(B,{value:"midwest",children:"Midwest"}),(0,i.jsx)(B,{value:"southwest",children:"Southwest"}),(0,i.jsx)(B,{value:"west",children:"West"}),(0,i.jsx)(B,{value:"other",children:"Other"})]})]})]})]})})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Demographics"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"ethnicity",children:"Ethnicity"}),(0,i.jsxs)(E,{value:n.ethnicity||"",onValueChange:e=>c("ethnicity",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select ethnicity"})}),(0,i.jsx)(R,{children:G.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"race",children:"Race"}),(0,i.jsxs)(E,{value:n.race||"",onValueChange:e=>c("race",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select race"})}),(0,i.jsx)(R,{children:q.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"primaryLanguage",children:"Primary Language"}),(0,i.jsx)(w.I,{id:"primaryLanguage",value:n.primaryLanguage||"",onChange:e=>c("primaryLanguage",e.target.value),placeholder:"English"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"maritalStatus",children:"Marital Status"}),(0,i.jsxs)(E,{value:n.maritalStatus||"",onValueChange:e=>c("maritalStatus",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select marital status"})}),(0,i.jsx)(R,{children:z.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Social Information"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"education",children:"Education Level"}),(0,i.jsxs)(E,{value:n.education||"",onValueChange:e=>c("education",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select education level"})}),(0,i.jsx)(R,{children:H.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"occupation",children:"Occupation"}),(0,i.jsxs)(E,{value:n.occupation||"",onValueChange:e=>c("occupation",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select occupation"})}),(0,i.jsx)(R,{children:V.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"employmentStatus",children:"Employment Status"}),(0,i.jsxs)(E,{value:n.employmentStatus||"",onValueChange:e=>c("employmentStatus",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select employment status"})}),(0,i.jsx)(R,{children:_.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"livingArrangement",children:"Living Arrangement"}),(0,i.jsxs)(E,{value:n.livingArrangement||"",onValueChange:e=>c("livingArrangement",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select living arrangement"})}),(0,i.jsx)(R,{children:U.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Insurance Information"})}),(0,i.jsx)(t.aY,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{htmlFor:"insuranceType",children:"Insurance Type"}),(0,i.jsxs)(E,{value:n.insuranceType||"",onValueChange:e=>c("insuranceType",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select insurance type"})}),(0,i.jsx)(R,{children:W.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})})]})]})}},{id:"symptoms",label:"Symptoms",icon:x.Z,component:function(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||{selectedSymptoms:[],symptomDetails:{},additionalSymptoms:""});(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let c=(e,s)=>{let a=s?[...n.selectedSymptoms||[],e]:(n.selectedSymptoms||[]).filter(s=>s!==e);r(e=>({...e,selectedSymptoms:a}))},o=(e,s,a)=>{r(i=>{var l;return{...i,symptomDetails:{...i.symptomDetails,[e]:{...null===(l=i.symptomDetails)||void 0===l?void 0:l[e],[s]:a}}}})};return(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-slate-900 mb-3",children:"Symptom Assessment"}),(0,i.jsx)("p",{className:"text-base text-slate-600 leading-relaxed",children:"Select all symptoms that apply and provide additional details for comprehensive evaluation."})]}),Object.entries(el).map(e=>{let[s,a]=e;return(0,i.jsxs)(t.Zb,{className:"symptom-category-card",children:[(0,i.jsxs)(t.Ol,{className:"pb-4",children:[(0,i.jsxs)(t.ll,{className:"text-xl font-semibold text-slate-800",children:[s," Symptoms"]}),(0,i.jsx)(t.SZ,{className:"text-slate-600",children:"Select all that apply to the patient's current condition"})]}),(0,i.jsx)(t.aY,{children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(e=>{var a;let l=(null===(a=n.selectedSymptoms)||void 0===a?void 0:a.includes(e))||!1;return(0,i.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-md hover:bg-white/50 transition-colors duration-200",children:[(0,i.jsx)(ei,{id:"".concat(s,"-").concat(e),checked:l,onCheckedChange:s=>c(e,s),className:"symptom-checkbox"}),(0,i.jsx)(A,{htmlFor:"".concat(s,"-").concat(e),className:"symptom-label flex-1",children:e})]},e)})})})]},s)}),n.selectedSymptoms&&n.selectedSymptoms.length>0&&(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Symptom Details"}),(0,i.jsx)(t.SZ,{children:"Provide additional details for selected symptoms"})]}),(0,i.jsx)(t.aY,{className:"space-y-6",children:n.selectedSymptoms.map(e=>{var s,a,l,t,r,c,d,m;return(0,i.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[(0,i.jsx)("h4",{className:"font-medium text-slate-900",children:e}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Severity"}),(0,i.jsxs)(E,{value:(null===(a=n.symptomDetails)||void 0===a?void 0:null===(s=a[e])||void 0===s?void 0:s.severity)||"",onValueChange:s=>o(e,"severity",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select severity"})}),(0,i.jsx)(R,{children:J.map(e=>(0,i.jsx)(B,{value:e.value,children:e.label},e.value))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Duration"}),(0,i.jsxs)(E,{value:(null===(t=n.symptomDetails)||void 0===t?void 0:null===(l=t[e])||void 0===l?void 0:l.duration)||"",onValueChange:s=>o(e,"duration",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select duration"})}),(0,i.jsx)(R,{children:K.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Frequency"}),(0,i.jsxs)(E,{value:(null===(c=n.symptomDetails)||void 0===c?void 0:null===(r=c[e])||void 0===r?void 0:r.frequency)||"",onValueChange:s=>o(e,"frequency",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select frequency"})}),(0,i.jsx)(R,{children:Q.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:(null===(m=n.symptomDetails)||void 0===m?void 0:null===(d=m[e])||void 0===d?void 0:d.notes)||"",onChange:s=>o(e,"notes",s.target.value),placeholder:"Any additional details about this symptom...",rows:2})]})]},e)})})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Additional Symptoms"}),(0,i.jsx)(t.SZ,{children:"Describe any other symptoms not listed above"})]}),(0,i.jsx)(t.aY,{children:(0,i.jsx)(es,{value:n.additionalSymptoms||"",onChange:e=>r(s=>({...s,additionalSymptoms:e.target.value})),placeholder:"Describe any additional symptoms, their severity, duration, and impact...",rows:4})})]})]})}},{id:"risk",label:"Risk Assessment",icon:u.Z,component:function(e){var s,a,n,r,c,o,d;let{data:m,onUpdate:h}=e,[x,u]=(0,l.useState)(()=>m||{});(0,l.useEffect)(()=>{m&&Object.keys(m).length>0&&u(m)},[m]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{h(x)},100);return()=>clearTimeout(e)},[x,h]);let p=(e,s)=>{u(a=>({...a,[e]:"true"===s}))},j=(e,s)=>{u(a=>({...a,[e]:s}))},g="high"===x.suicidalRiskLevel||"high"===x.violenceRiskLevel||"high"===x.selfHarmRisk;return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Risk Assessment"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Evaluate potential risks for safety and intervention planning."}),g&&(0,i.jsxs)("div",{className:"mt-2 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[(0,i.jsx)(eo.Z,{className:"h-5 w-5 text-red-600"}),(0,i.jsx)("span",{className:"text-sm text-red-800 font-medium",children:"High risk factors identified - immediate intervention may be required"})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Suicide Risk Assessment"})}),(0,i.jsx)(t.aY,{className:"space-y-6",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Suicidal Ideation"}),(0,i.jsxs)(er,{value:(null===(s=x.suicidalIdeation)||void 0===s?void 0:s.toString())||"",onValueChange:e=>p("suicidalIdeation",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"si-no"}),(0,i.jsx)(A,{htmlFor:"si-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"si-yes"}),(0,i.jsx)(A,{htmlFor:"si-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Suicidal Plan"}),(0,i.jsxs)(er,{value:(null===(a=x.suicidalPlan)||void 0===a?void 0:a.toString())||"",onValueChange:e=>p("suicidalPlan",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"sp-no"}),(0,i.jsx)(A,{htmlFor:"sp-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"sp-yes"}),(0,i.jsx)(A,{htmlFor:"sp-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Access to Means"}),(0,i.jsxs)(er,{value:(null===(n=x.suicidalMeans)||void 0===n?void 0:n.toString())||"",onValueChange:e=>p("suicidalMeans",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"sm-no"}),(0,i.jsx)(A,{htmlFor:"sm-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"sm-yes"}),(0,i.jsx)(A,{htmlFor:"sm-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"History of Suicide Attempts"}),(0,i.jsxs)(er,{value:(null===(r=x.suicidalAttemptHistory)||void 0===r?void 0:r.toString())||"",onValueChange:e=>p("suicidalAttemptHistory",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"sah-no"}),(0,i.jsx)(A,{htmlFor:"sah-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"sah-yes"}),(0,i.jsx)(A,{htmlFor:"sah-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Overall Suicide Risk Level"}),(0,i.jsxs)(E,{value:x.suicidalRiskLevel||"",onValueChange:e=>j("suicidalRiskLevel",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select risk level"})}),(0,i.jsx)(R,{children:$.map(e=>(0,i.jsx)(B,{value:e.value,children:e.label},e.value))})]})]})]})})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Violence Risk Assessment"})}),(0,i.jsxs)(t.aY,{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Homicidal Ideation"}),(0,i.jsxs)(er,{value:(null===(c=x.homicidalIdeation)||void 0===c?void 0:c.toString())||"",onValueChange:e=>p("homicidalIdeation",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"hi-no"}),(0,i.jsx)(A,{htmlFor:"hi-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"hi-yes"}),(0,i.jsx)(A,{htmlFor:"hi-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"History of Violence"}),(0,i.jsxs)(er,{value:(null===(o=x.violenceHistory)||void 0===o?void 0:o.toString())||"",onValueChange:e=>p("violenceHistory",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"vh-no"}),(0,i.jsx)(A,{htmlFor:"vh-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"vh-yes"}),(0,i.jsx)(A,{htmlFor:"vh-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Violence Risk Level"}),(0,i.jsxs)(E,{value:x.violenceRiskLevel||"",onValueChange:e=>j("violenceRiskLevel",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select risk level"})}),(0,i.jsx)(R,{children:$.map(e=>(0,i.jsx)(B,{value:e.value,children:e.label},e.value))})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Self-Harm & Substance Use Risk"})}),(0,i.jsxs)(t.aY,{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"History of Self-Harm"}),(0,i.jsxs)(er,{value:(null===(d=x.selfHarmHistory)||void 0===d?void 0:d.toString())||"",onValueChange:e=>p("selfHarmHistory",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"shh-no"}),(0,i.jsx)(A,{htmlFor:"shh-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"shh-yes"}),(0,i.jsx)(A,{htmlFor:"shh-yes",children:"Yes"})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Self-Harm Risk Level"}),(0,i.jsxs)(E,{value:x.selfHarmRisk||"",onValueChange:e=>j("selfHarmRisk",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select risk level"})}),(0,i.jsx)(R,{children:$.map(e=>(0,i.jsx)(B,{value:e.value,children:e.label},e.value))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Substance Use Risk Level"}),(0,i.jsxs)(E,{value:x.substanceUseRisk||"",onValueChange:e=>j("substanceUseRisk",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select risk level"})}),(0,i.jsx)(R,{children:$.map(e=>(0,i.jsx)(B,{value:e.value,children:e.label},e.value))})]})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Risk & Protective Factors"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Risk Factors"}),(0,i.jsx)(es,{value:x.riskFactors||"",onChange:e=>j("riskFactors",e.target.value),placeholder:"Describe specific risk factors (e.g., social isolation, recent losses, substance use, mental illness, access to means, etc.)",rows:3})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Protective Factors"}),(0,i.jsx)(es,{value:x.protectiveFactors||"",onChange:e=>j("protectiveFactors",e.target.value),placeholder:"Describe protective factors (e.g., social support, coping skills, treatment engagement, religious beliefs, etc.)",rows:3})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Recommended Interventions"}),(0,i.jsx)(es,{value:x.interventions||"",onChange:e=>j("interventions",e.target.value),placeholder:"Describe recommended interventions and safety planning measures",rows:3})]})]})]})]})}},{id:"history",label:"Medical History",icon:p.Z,component:function(e){let{data:s,onUpdate:a}=e,[n,r]=(0,l.useState)(()=>s||{}),[c,o]=(0,l.useState)("");(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&r(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]);let d=(e,s)=>{r(a=>({...a,[e]:s}))},h=(0,l.useCallback)(e=>{r(s=>({...s,substanceUseHistory:e}))},[]),x=(0,l.useCallback)(e=>{r(s=>({...s,medicationHistory:e}))},[]),u=(e,s)=>{r(a=>({...a,structuredMedicalConditions:{...a.structuredMedicalConditions,[e]:s}}))},p=e=>{r(s=>{var a;return{...s,psychiatricEpisodes:(null===(a=s.psychiatricEpisodes)||void 0===a?void 0:a.filter(s=>s.id!==e))||[]}})},j=(e,s,a)=>{r(i=>{var l;return{...i,psychiatricEpisodes:(null===(l=i.psychiatricEpisodes)||void 0===l?void 0:l.map(i=>i.id===e?{...i,[s]:a}:i))||[]}})};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Medical History"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Provide comprehensive medical and psychiatric history information."})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Current Medical Information"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Allergies"}),(0,i.jsx)(es,{value:n.allergies||"",onChange:e=>d("allergies",e.target.value),placeholder:"List any known allergies to medications, foods, or other substances",rows:2})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Surgical History"}),(0,i.jsx)(es,{value:n.surgicalHistory||"",onChange:e=>d("surgicalHistory",e.target.value),placeholder:"List any past surgeries and dates",rows:2})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Medical Conditions"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:ef.map(e=>{var s;return(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{id:e,checked:(null===(s=n.structuredMedicalConditions)||void 0===s?void 0:s[e])||!1,onCheckedChange:s=>u(e,s)}),(0,i.jsx)(A,{htmlFor:e,className:"text-sm font-normal cursor-pointer",children:e})]},e)})}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Other Medical Conditions"}),(0,i.jsx)(es,{value:n.otherMedicalConditions||"",onChange:e=>d("otherMedicalConditions",e.target.value),placeholder:"List any additional medical conditions not covered above",rows:2})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Current Medications"})}),(0,i.jsx)(t.aY,{children:(0,i.jsx)("div",{className:"space-y-2",children:(0,i.jsx)(es,{value:n.currentMedications||"",onChange:e=>d("currentMedications",e.target.value),placeholder:"List all current medications, dosages, and frequency",rows:3})})})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsxs)(t.Ol,{children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Psychiatric History Episodes"}),(0,i.jsx)(t.SZ,{children:"Track specific psychiatric episodes with detailed information for comprehensive assessment"})]}),(0,i.jsxs)(t.aY,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-slate-50",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Add Psychiatric Episode"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Episode Type"}),(0,i.jsxs)(E,{value:c,onValueChange:o,children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select episode type"})}),(0,i.jsx)(R,{children:eN.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsx)("div",{className:"flex items-end",children:(0,i.jsxs)(m.z,{onClick:()=>{if(!c)return;let e={id:Date.now().toString(),episodeType:c,duration:"",durationUnit:"",startDate:"",endDate:"",severity:"",treatmentReceived:[],treatmentResponse:"",notes:""};r(s=>({...s,psychiatricEpisodes:[...s.psychiatricEpisodes||[],e]})),o("")},disabled:!c,className:"w-full",children:[(0,i.jsx)(em.Z,{className:"h-4 w-4 mr-2"}),"Add Episode"]})})]})]}),n.psychiatricEpisodes&&n.psychiatricEpisodes.length>0&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h4",{className:"font-medium",children:"Psychiatric Episodes"}),n.psychiatricEpisodes.map(e=>(0,i.jsxs)(t.Zb,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsx)(t.Ol,{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ed.C,{variant:"outline",children:e.episodeType}),e.severity&&(0,i.jsx)(ed.C,{variant:"secondary",children:e.severity})]}),(0,i.jsx)(m.z,{variant:"ghost",size:"sm",onClick:()=>p(e.id),className:"text-red-600 hover:text-red-800",children:(0,i.jsx)(eh.Z,{className:"h-4 w-4"})})]})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Duration"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(w.I,{value:e.duration,onChange:s=>j(e.id,"duration",s.target.value),placeholder:"e.g., 2",type:"number",className:"flex-1"}),(0,i.jsxs)(E,{value:e.durationUnit,onValueChange:s=>j(e.id,"durationUnit",s),children:[(0,i.jsx)(I,{className:"w-24",children:(0,i.jsx)(M,{placeholder:"Unit"})}),(0,i.jsx)(R,{children:eb.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Start Date (Month/Year)"}),(0,i.jsx)(w.I,{value:e.startDate,onChange:s=>j(e.id,"startDate",s.target.value),placeholder:"e.g., 03/2023",type:"month"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"End Date (Month/Year)"}),(0,i.jsx)(w.I,{value:e.endDate,onChange:s=>j(e.id,"endDate",s.target.value),placeholder:"Leave empty if ongoing",type:"month"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Severity"}),(0,i.jsxs)(E,{value:e.severity,onValueChange:s=>j(e.id,"severity",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select severity"})}),(0,i.jsx)(R,{children:eC.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Treatment Response"}),(0,i.jsxs)(E,{value:e.treatmentResponse,onValueChange:s=>j(e.id,"treatmentResponse",s),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select response"})}),(0,i.jsx)(R,{children:ew.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Treatment Received"}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:eS.map(s=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ei,{id:"".concat(e.id,"-").concat(s),checked:e.treatmentReceived.includes(s),onCheckedChange:a=>{let i=e.treatmentReceived,l=a?[...i,s]:i.filter(e=>e!==s);j(e.id,"treatmentReceived",l)}}),(0,i.jsx)(A,{htmlFor:"".concat(e.id,"-").concat(s),className:"text-sm",children:s})]},s))})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Additional Notes"}),(0,i.jsx)(es,{value:e.notes,onChange:s=>j(e.id,"notes",s.target.value),placeholder:"Additional details about the episode, triggers, symptoms, etc.",rows:2})]})]})]},e.id))]}),(!n.psychiatricEpisodes||0===n.psychiatricEpisodes.length)&&(0,i.jsxs)("div",{className:"text-center py-8 text-slate-500",children:[(0,i.jsx)("p",{children:"No psychiatric episodes recorded."}),(0,i.jsx)("p",{className:"text-sm",children:"Use the form above to add episode information."})]}),(0,i.jsxs)("div",{className:"space-y-2 pt-4 border-t",children:[(0,i.jsx)(A,{children:"Family Psychiatric History"}),(0,i.jsx)(es,{value:n.familyPsychiatricHistory||"",onChange:e=>d("familyPsychiatricHistory",e.target.value),placeholder:"Describe any family history of mental illness, suicide, or substance abuse",rows:3})]})]})]}),(0,i.jsx)(ej,{data:n.substanceUseHistory||[],onUpdate:h}),(0,i.jsx)(ey,{data:n.medicationHistory||[],onUpdate:x})]})}},{id:"mental-status",label:"Mental Status",icon:j.Z,component:function(e){var s,a;let{data:n,onUpdate:r}=e,[c,o]=(0,l.useState)(()=>n||{});(0,l.useEffect)(()=>{n&&Object.keys(n).length>0&&o(n)},[n]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{r(c)},100);return()=>clearTimeout(e)},[c,r]);let d=(e,s)=>{o(a=>({...a,[e]:"true"===s}))},m=(e,s)=>{o(a=>({...a,[e]:s}))};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Mental Status Examination"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Systematic evaluation of the patient's current mental state."})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Appearance & Behavior"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Appearance"}),(0,i.jsxs)(E,{value:c.appearance||"",onValueChange:e=>m("appearance",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select appearance"})}),(0,i.jsx)(R,{children:ek.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Behavior"}),(0,i.jsxs)(E,{value:c.behavior||"",onValueChange:e=>m("behavior",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select behavior"})}),(0,i.jsx)(R,{children:eD.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Attitude"}),(0,i.jsx)(es,{value:c.attitude||"",onChange:e=>m("attitude",e.target.value),placeholder:"Describe patient's attitude toward the interview",rows:2})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Speech"})}),(0,i.jsx)(t.aY,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Rate"}),(0,i.jsxs)(E,{value:c.speechRate||"",onValueChange:e=>m("speechRate",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select rate"})}),(0,i.jsx)(R,{children:eA.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Volume"}),(0,i.jsxs)(E,{value:c.speechVolume||"",onValueChange:e=>m("speechVolume",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select volume"})}),(0,i.jsx)(R,{children:eP.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Tone"}),(0,i.jsx)(es,{value:c.speechTone||"",onChange:e=>m("speechTone",e.target.value),placeholder:"Describe tone",rows:1})]})]})})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Mood & Affect"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Mood"}),(0,i.jsxs)(E,{value:c.mood||"",onValueChange:e=>m("mood",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select mood"})}),(0,i.jsx)(R,{children:eO.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Affect"}),(0,i.jsxs)(E,{value:c.affect||"",onValueChange:e=>m("affect",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select affect"})}),(0,i.jsx)(R,{children:eT.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Affect Range"}),(0,i.jsx)(es,{value:c.affectRange||"",onChange:e=>m("affectRange",e.target.value),placeholder:"Describe range (e.g., full, restricted)",rows:1})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Affect Intensity"}),(0,i.jsx)(es,{value:c.affectIntensity||"",onChange:e=>m("affectIntensity",e.target.value),placeholder:"Describe intensity",rows:1})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Thought Process & Content"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Thought Process"}),(0,i.jsxs)(E,{value:c.thoughtProcess||"",onValueChange:e=>m("thoughtProcess",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select thought process"})}),(0,i.jsx)(R,{children:eF.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Thought Content"}),(0,i.jsx)(es,{value:c.thoughtContent||"",onChange:e=>m("thoughtContent",e.target.value),placeholder:"Describe thought content, preoccupations, obsessions",rows:3})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Perceptual Disturbances"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Hallucinations"}),(0,i.jsxs)(er,{value:(null===(s=c.hallucinations)||void 0===s?void 0:s.toString())||"",onValueChange:e=>d("hallucinations",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"hall-no"}),(0,i.jsx)(A,{htmlFor:"hall-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"hall-yes"}),(0,i.jsx)(A,{htmlFor:"hall-yes",children:"Yes"})]})]})]}),c.hallucinations&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Type of Hallucinations"}),(0,i.jsx)(es,{value:c.hallucinationType||"",onChange:e=>m("hallucinationType",e.target.value),placeholder:"Describe type (auditory, visual, tactile, etc.)",rows:2})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(A,{className:"text-base font-medium",children:"Delusions"}),(0,i.jsxs)(er,{value:(null===(a=c.delusions)||void 0===a?void 0:a.toString())||"",onValueChange:e=>d("delusions",e),className:"mt-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"false",id:"del-no"}),(0,i.jsx)(A,{htmlFor:"del-no",children:"No"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ec,{value:"true",id:"del-yes"}),(0,i.jsx)(A,{htmlFor:"del-yes",children:"Yes"})]})]})]}),c.delusions&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Type of Delusions"}),(0,i.jsx)(es,{value:c.delusionType||"",onChange:e=>m("delusionType",e.target.value),placeholder:"Describe type (paranoid, grandiose, somatic, etc.)",rows:2})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Cognitive Function"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Orientation"}),(0,i.jsxs)(E,{value:c.orientation||"",onValueChange:e=>m("orientation",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select orientation"})}),(0,i.jsx)(R,{children:eE.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Attention"}),(0,i.jsx)(es,{value:c.attention||"",onChange:e=>m("attention",e.target.value),placeholder:"Describe attention span",rows:1})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Concentration"}),(0,i.jsx)(es,{value:c.concentration||"",onChange:e=>m("concentration",e.target.value),placeholder:"Describe concentration ability",rows:1})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Memory"}),(0,i.jsx)(es,{value:c.memory||"",onChange:e=>m("memory",e.target.value),placeholder:"Describe memory function",rows:1})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Insight & Judgment"})}),(0,i.jsx)(t.aY,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Insight"}),(0,i.jsxs)(E,{value:c.insight||"",onValueChange:e=>m("insight",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select insight level"})}),(0,i.jsx)(R,{children:eM.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Judgment"}),(0,i.jsxs)(E,{value:c.judgment||"",onValueChange:e=>m("judgment",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select judgment level"})}),(0,i.jsx)(R,{children:eI.map(e=>(0,i.jsx)(B,{value:e,children:e},e))})]})]})]})})]})]})}},{id:"diagnosis",label:"Diagnosis",icon:g.Z,component:function(e){var s;let{data:a,onUpdate:n}=e,[r,c]=(0,l.useState)(()=>a||{secondaryDiagnoses:[]}),[o,d]=(0,l.useState)(""),[h,x]=(0,l.useState)(eR);(0,l.useEffect)(()=>{a&&Object.keys(a).length>0&&c(a)},[a]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{n(r)},100);return()=>clearTimeout(e)},[r,n]),(0,l.useEffect)(()=>{o?x(eR.filter(e=>e.name.toLowerCase().includes(o.toLowerCase())||e.code.toLowerCase().includes(o.toLowerCase()))):x(eR)},[o]);let u=(e,s)=>{c(a=>({...a,[e]:s}))},p=e=>{c(s=>({...s,primaryDiagnosis:e.name,primaryDiagnosisCode:e.code})),d("")},j=(e,s,a)=>{c(i=>{var l;return{...i,secondaryDiagnoses:null===(l=i.secondaryDiagnoses)||void 0===l?void 0:l.map((i,l)=>l===e?{...i,[s]:a}:i)}})},g=e=>{c(s=>{var a;return{...s,secondaryDiagnoses:null===(a=s.secondaryDiagnoses)||void 0===a?void 0:a.filter((s,a)=>a!==e)}})};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Diagnosis"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Provide diagnostic formulation and treatment recommendations."})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Primary Diagnosis"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Search Diagnoses"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eL.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,i.jsx)(w.I,{placeholder:"Search by diagnosis name or ICD-10 code...",value:o,onChange:e=>d(e.target.value),className:"pl-10"})]})]}),o&&(0,i.jsx)("div",{className:"border rounded-lg max-h-48 overflow-y-auto",children:h.map((e,s)=>(0,i.jsxs)("div",{className:"p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0",onClick:()=>p(e),children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:e.code}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:e.name})]},s))}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Primary Diagnosis"}),(0,i.jsx)(w.I,{value:r.primaryDiagnosis||"",onChange:e=>u("primaryDiagnosis",e.target.value),placeholder:"Enter primary diagnosis"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"ICD-10 Code"}),(0,i.jsx)(w.I,{value:r.primaryDiagnosisCode||"",onChange:e=>u("primaryDiagnosisCode",e.target.value),placeholder:"Enter ICD-10 code"})]})]})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)(t.ll,{className:"text-lg",children:"Secondary Diagnoses"}),(0,i.jsxs)(m.z,{onClick:()=>{c(e=>({...e,secondaryDiagnoses:[...e.secondaryDiagnoses||[],{diagnosis:"",code:"",type:"secondary"}]}))},size:"sm",children:[(0,i.jsx)(em.Z,{className:"h-4 w-4 mr-2"}),"Add Diagnosis"]})]})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[null===(s=r.secondaryDiagnoses)||void 0===s?void 0:s.map((e,s)=>(0,i.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("h4",{className:"font-medium",children:["Secondary Diagnosis ",s+1]}),(0,i.jsx)(m.z,{onClick:()=>g(s),variant:"outline",size:"sm",children:(0,i.jsx)(eZ.Z,{className:"h-4 w-4"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Diagnosis"}),(0,i.jsx)(w.I,{value:e.diagnosis,onChange:e=>j(s,"diagnosis",e.target.value),placeholder:"Enter diagnosis"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"ICD-10 Code"}),(0,i.jsx)(w.I,{value:e.code,onChange:e=>j(s,"code",e.target.value),placeholder:"Enter code"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Type"}),(0,i.jsxs)(E,{value:e.type,onValueChange:e=>j(s,"type",e),children:[(0,i.jsx)(I,{children:(0,i.jsx)(M,{placeholder:"Select type"})}),(0,i.jsx)(R,{children:eB.map(e=>(0,i.jsx)(B,{value:e.value,children:e.label},e.value))})]})]})]})]},s)),(!r.secondaryDiagnoses||0===r.secondaryDiagnoses.length)&&(0,i.jsx)("div",{className:"text-center py-8 text-gray-500",children:'No secondary diagnoses added. Click "Add Diagnosis" to add one.'})]})]}),(0,i.jsxs)(t.Zb,{children:[(0,i.jsx)(t.Ol,{children:(0,i.jsx)(t.ll,{className:"text-lg",children:"Clinical Impression & Recommendations"})}),(0,i.jsxs)(t.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Differential Diagnoses"}),(0,i.jsx)(es,{value:r.differentialDiagnoses||"",onChange:e=>u("differentialDiagnoses",e.target.value),placeholder:"List other diagnoses considered and ruled out",rows:3})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Diagnostic Impression"}),(0,i.jsx)(es,{value:r.diagnosticImpression||"",onChange:e=>u("diagnosticImpression",e.target.value),placeholder:"Provide overall diagnostic impression and rationale",rows:4})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(A,{children:"Treatment Recommendations"}),(0,i.jsx)(es,{value:r.treatmentRecommendations||"",onChange:e=>u("treatmentRecommendations",e.target.value),placeholder:"Provide treatment recommendations including medications, therapy, follow-up care",rows:4})]})]})]})]})}},{id:"laboratory-tests",label:"Laboratory and Assessment Tests",icon:v.Z,component:function(e){let{data:s,onUpdate:a}=e,[n,t]=(0,l.useState)(()=>s||{bloodWork:{},toxicologyScreen:{},psychologicalTests:{psychologicalAssessments:{}},imagingAndNeurological:{imagingStudies:{},neurologicalTests:{}},selectedTests:{},bloodTestComponents:{},bloodTestNotes:{},psychologicalAssessments:{},imagingStudies:{},neurologicalTests:{}});return(0,l.useEffect)(()=>{s&&Object.keys(s).length>0&&t(s)},[s]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{a(n)},100);return()=>clearTimeout(e)},[n,a]),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-slate-900 mb-3",children:"Laboratory Tests & Medical Screening"}),(0,i.jsx)("p",{className:"text-base text-slate-600 leading-relaxed",children:"Comprehensive laboratory testing with clinical-grade documentation and interpretation."})]}),(0,i.jsxs)(r.mQ,{defaultValue:"bloodwork",className:"w-full",children:[(0,i.jsxs)(r.dr,{className:"grid w-full grid-cols-4",children:[(0,i.jsxs)(r.SP,{value:"bloodwork",className:"flex items-center gap-2",children:[(0,i.jsx)(v.Z,{className:"h-4 w-4"}),"Blood Work"]}),(0,i.jsxs)(r.SP,{value:"toxicology",className:"flex items-center gap-2",children:[(0,i.jsx)(eH.Z,{className:"h-4 w-4"}),"Toxicology"]}),(0,i.jsxs)(r.SP,{value:"psychological",className:"flex items-center gap-2",children:[(0,i.jsx)(x.Z,{className:"h-4 w-4"}),"Psychological"]}),(0,i.jsxs)(r.SP,{value:"imaging",className:"flex items-center gap-2",children:[(0,i.jsx)(eV.Z,{className:"h-4 w-4"}),"Imaging & Other"]})]}),(0,i.jsx)(r.nU,{value:"bloodwork",className:"mt-6",children:(0,i.jsx)(eW,{data:n.bloodWork||{},onUpdate:e=>{t(s=>({...s,bloodWork:e}))}})}),(0,i.jsx)(r.nU,{value:"toxicology",className:"mt-6",children:(0,i.jsx)(eJ,{data:n.toxicologyScreen||{},onUpdate:e=>{t(s=>({...s,toxicologyScreen:e}))}})}),(0,i.jsx)(r.nU,{value:"psychological",className:"mt-6",children:(0,i.jsx)(e$,{data:n.psychologicalTests||{psychologicalAssessments:{}},onUpdate:e=>{t(s=>({...s,psychologicalTests:e}))}})}),(0,i.jsx)(r.nU,{value:"imaging",className:"mt-6",children:(0,i.jsx)(e4,{data:n.imagingAndNeurological||{imagingStudies:{},neurologicalTests:{}},onUpdate:e=>{t(s=>({...s,imagingAndNeurological:e}))}})})]})]})}}];function e3(){let e=(0,n.useSearchParams)(),s=(0,n.useRouter)(),a=e.get("id"),[c,o]=(0,l.useState)("demographics"),[h,x]=(0,l.useState)({demographics:{},symptoms:{},riskAssessment:{},medicalHistory:{},mentalStatusExam:{},diagnosis:{},laboratoryTests:{}}),[u,p]=(0,l.useState)(new Set),[j,g]=(0,l.useState)(null),[v,w]=(0,l.useState)(!1),[k,D]=(0,l.useState)(!1),[A,P]=(0,l.useState)(!1),[O,T]=(0,l.useState)(null),[F,E]=(0,l.useState)(a),M=u.size/e1.length*100,I=(0,l.useRef)(null),L=(0,l.useCallback)(async(e,s,a)=>{w(!0);try{let i={data:e,completedSections:Array.from(s),lastSaved:new Date().toISOString(),assessmentId:a};localStorage.setItem("psychiatric-assessment-data",JSON.stringify(i));let l={...e,assessmentId:a},n=await fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!n.ok)throw Error("Failed to save to database");let t=await n.json();!a&&t.assessmentId&&E(t.assessmentId),g(new Date),T(null)}catch(e){console.error("Error saving data:",e),T(e instanceof Error?e.message:"Failed to save assessment"),g(new Date)}finally{w(!1)}},[]);(0,l.useEffect)(()=>(I.current&&clearTimeout(I.current),(Object.keys(h.demographics).length>0||Object.keys(h.symptoms).length>0||Object.keys(h.riskAssessment).length>0||Object.keys(h.medicalHistory).length>0||Object.keys(h.mentalStatusExam).length>0||Object.keys(h.diagnosis).length>0||Object.keys(h.laboratoryTests).length>0)&&(I.current=setTimeout(()=>{L(h,u,F)},2e3)),()=>{I.current&&clearTimeout(I.current)}),[h,u,F,L]);let Z=(0,l.useCallback)(()=>{x({demographics:{},symptoms:{},riskAssessment:{},medicalHistory:{},mentalStatusExam:{},diagnosis:{},laboratoryTests:{}}),p(new Set),g(null),E(null),T(null),localStorage.removeItem("psychiatric-assessment-data")},[]);(0,l.useEffect)(()=>{(async()=>{if(a)try{let c=await fetch("/api/assessments?id=".concat(a));if(c.ok){var e,s,i,l,n,t,r;let o=await c.json();console.log("Loaded assessment data:",o);let d={demographics:o.demographics||{},symptoms:{selectedSymptoms:(null===(e=o.symptoms)||void 0===e?void 0:e.map(e=>e.symptom.name))||[],symptomDetails:(null===(s=o.symptoms)||void 0===s?void 0:s.reduce((e,s)=>(e[s.symptom.name]={severity:s.severity,duration:s.duration,frequency:s.frequency,notes:s.notes},e),{}))||{}},riskAssessment:o.riskAssessment||{},medicalHistory:o.medicalHistory||{},mentalStatusExam:o.mentalStatusExam||{},diagnosis:{primaryDiagnosis:(null===(l=o.diagnoses)||void 0===l?void 0:null===(i=l.find(e=>"primary"===e.type))||void 0===i?void 0:i.diagnosis.name)||"",primaryDiagnosisCode:(null===(t=o.diagnoses)||void 0===t?void 0:null===(n=t.find(e=>"primary"===e.type))||void 0===n?void 0:n.diagnosis.code)||"",secondaryDiagnoses:(null===(r=o.diagnoses)||void 0===r?void 0:r.filter(e=>"primary"!==e.type).map(e=>({diagnosis:e.diagnosis.name,code:e.diagnosis.code,type:e.type})))||[]},laboratoryTests:{selectedTests:{},testResults:o.laboratoryTests||[]}};x(d),E(a);let m=new Set;Object.keys(d.demographics).length>0&&m.add("demographics"),d.symptoms.selectedSymptoms.length>0&&m.add("symptoms"),Object.keys(d.riskAssessment).length>0&&m.add("risk"),Object.keys(d.medicalHistory).length>0&&m.add("history"),Object.keys(d.mentalStatusExam).length>0&&m.add("mental-status"),d.diagnosis.primaryDiagnosis&&m.add("diagnosis"),p(m)}else console.error("Assessment not found"),Z()}catch(e){console.error("Error loading assessment:",e),Z()}else if("true"===new URLSearchParams(window.location.search).get("new"))Z();else{let e=localStorage.getItem("psychiatric-assessment-data");if(e)try{let s=JSON.parse(e);x(s.data||{demographics:{},symptoms:{},riskAssessment:{},medicalHistory:{},mentalStatusExam:{},diagnosis:{},laboratoryTests:{}}),p(new Set(s.completedSections||[])),g(s.lastSaved?new Date(s.lastSaved):null),E(s.assessmentId||null)}catch(e){console.error("Error loading saved data:",e),Z()}else Z()}})()},[a,Z]);let R=(0,l.useCallback)((e,s)=>{x(a=>({...a,[e]:s})),s&&Object.keys(s).length>0&&p(s=>new Set(Array.from(s).concat(e)))},[]);(0,l.useCallback)(async()=>{await L(h,u,F)},[L,h,u,F]),(0,l.useCallback)(()=>{I.current&&clearTimeout(I.current),window.location.href="/assessment?new=true"},[]);let B=(0,l.useMemo)(()=>{let e={};return e1.forEach(s=>{e[s.id]=e=>R(s.id,e)}),e},[R]),H=async e=>{try{let s=await fetch("/api/export?format=".concat(e));if(!s.ok)throw Error("Failed to export data");let a=await s.blob(),i=URL.createObjectURL(a),l=document.createElement("a");l.href=i,l.download="psychiatric-assessments-".concat(new Date().toISOString().split("T")[0],".").concat(e),l.click(),URL.revokeObjectURL(i)}catch(a){console.error("Error exporting data:",a);let s={...h,metadata:{exportDate:new Date().toISOString(),completedSections:Array.from(u),progress:M}};if("json"===e){let e=new Blob([JSON.stringify(s,null,2)],{type:"application/json"}),a=URL.createObjectURL(e),i=document.createElement("a");i.href=a,i.download="psychiatric-assessment-".concat(new Date().toISOString().split("T")[0],".json"),i.click(),URL.revokeObjectURL(a)}}},V=async()=>{D(!0),T(null);try{I.current&&clearTimeout(I.current);let e={...h,assessmentId:F,status:"completed"},a=await fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.error||"Failed to complete assessment")}localStorage.removeItem("psychiatric-assessment-data"),Z(),s.push("/patients")}catch(s){console.error("Error completing assessment:",s);let e=s instanceof Error?s.message:"Failed to complete assessment";T(e),alert("Error: ".concat(e,". Please try again."))}finally{D(!1)}};return(0,l.useEffect)(()=>()=>{I.current&&clearTimeout(I.current)},[]),(0,i.jsx)("div",{className:"assessment-page-container",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsx)("div",{className:"assessment-header-enhanced fade-in",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,i.jsx)(S.default,{href:"/patients",children:(0,i.jsxs)(m.z,{variant:"outline",size:"sm",className:"button-modern-secondary",children:[(0,i.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Back to Patients"]})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"assessment-title-enhanced mb-2",children:a?"Edit Assessment":"New Assessment"}),(0,i.jsx)("p",{className:"assessment-subtitle-enhanced",children:a?"Continue or modify existing assessment":"Complete all sections for comprehensive evaluation"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"autosave-indicator",children:v?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(f.Z,{className:"h-4 w-4 animate-spin"}),(0,i.jsx)("span",{children:"Saving..."})]}):O?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(N.Z,{className:"h-4 w-4 text-red-500"}),(0,i.jsx)("span",{className:"text-red-600",children:O})]}):j?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b.Z,{className:"h-4 w-4 text-green-500"}),(0,i.jsxs)("span",{children:["Saved ",j.toLocaleTimeString()]})]}):null}),(0,i.jsx)(m.z,{onClick:V,disabled:k||M<100,className:"button-modern-primary",children:k?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(f.Z,{className:"h-5 w-5 animate-spin mr-3"}),"Completing..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b.Z,{className:"h-5 w-5 mr-3"}),"Save & Complete"]})}),(0,i.jsxs)(m.z,{variant:"outline",size:"sm",onClick:()=>H("json"),className:"button-modern-secondary",children:[(0,i.jsx)(C.Z,{className:"h-4 w-4 mr-2"}),"Export JSON"]}),(0,i.jsxs)(m.z,{variant:"outline",size:"sm",onClick:()=>H("csv"),className:"hover:bg-slate-50 border-slate-300 hover:border-slate-400 transition-all duration-200",children:[(0,i.jsx)(C.Z,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]})]})}),(0,i.jsxs)(t.Zb,{className:"mb-8 progress-card-enhanced slide-in-right",children:[(0,i.jsx)(t.Ol,{className:"pb-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)(t.ll,{className:"text-2xl font-bold text-slate-800",children:"Assessment Progress"}),(0,i.jsxs)("span",{className:"badge-modern badge-info",children:[u.size," of ",e1.length," sections completed"]})]})}),(0,i.jsx)(t.aY,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(d,{value:M,className:"w-full h-4 mb-4"}),(0,i.jsxs)("div",{className:"flex justify-between text-sm font-semibold text-slate-600",children:[(0,i.jsx)("span",{children:"0%"}),(0,i.jsxs)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:[Math.round(M),"% Complete"]}),(0,i.jsx)("span",{children:"100%"})]})]})})]}),(0,i.jsx)(t.Zb,{className:"card-modern fade-in",children:(0,i.jsx)(t.aY,{className:"p-0",children:(0,i.jsxs)(r.mQ,{value:c,onValueChange:o,className:"w-full",children:[(0,i.jsx)("div",{className:"border-b border-slate-200/50 bg-gradient-to-r from-slate-50 to-blue-50/30",children:(0,i.jsx)(r.dr,{className:"grid w-full grid-cols-7 h-auto p-3 bg-transparent",children:e1.map(e=>{let s=e.icon,a=u.has(e.id);return(0,i.jsxs)(r.SP,{value:e.id,className:"section-tab-enhanced flex flex-col items-center space-y-3 p-5 rounded-xl mx-1 transition-all duration-300 hover:bg-white/50 data-[state=active]:section-tab-active",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(s,{className:"h-6 w-6"}),a&&(0,i.jsx)(b.Z,{className:"h-5 w-5 text-emerald-500 pulse-glow"})]}),(0,i.jsx)("span",{className:"text-sm font-bold leading-tight text-center",children:e.label})]},e.id)})})}),e1.map(e=>{let s=e.component;return(0,i.jsx)(r.nU,{value:e.id,className:"form-section-modern fade-in",children:(0,i.jsx)(s,{data:h[e.id],onUpdate:B[e.id]})},e.id)})]})})})]})})}function e5(){return(0,i.jsx)(l.Suspense,{fallback:(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading assessment..."}),children:(0,i.jsx)(e3,{})})}},9174:function(e,s,a){"use strict";a.d(s,{C:function(){return r}});var i=a(7437);a(2265);var l=a(535),n=a(3448);let t=(0,l.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function r(e){let{className:s,variant:a,...l}=e;return(0,i.jsx)("div",{className:(0,n.cn)(t({variant:a}),s),...l})}},2381:function(e,s,a){"use strict";a.d(s,{z:function(){return o}});var i=a(7437),l=a(2265),n=a(7053),t=a(535),r=a(3448);let c=(0,t.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=l.forwardRef((e,s)=>{let{className:a,variant:l,size:t,asChild:o=!1,...d}=e,m=o?n.g7:"button";return(0,i.jsx)(m,{className:(0,r.cn)(c({variant:l,size:t,className:a})),ref:s,...d})});o.displayName="Button"},9820:function(e,s,a){"use strict";a.d(s,{Ol:function(){return r},SZ:function(){return o},Zb:function(){return t},aY:function(){return d},ll:function(){return c}});var i=a(7437),l=a(2265),n=a(3448);let t=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...l})});t.displayName="Card";let r=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...l})});r.displayName="CardHeader";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});c.displayName="CardTitle";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...l})});o.displayName="CardDescription";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...l})});d.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter"},279:function(e,s,a){"use strict";a.d(s,{I:function(){return t}});var i=a(7437),l=a(2265),n=a(3448);let t=l.forwardRef((e,s)=>{let{className:a,type:l,...t}=e;return(0,i.jsx)("input",{type:l,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...t})});t.displayName="Input"},7168:function(e,s,a){"use strict";a.d(s,{SP:function(){return o},dr:function(){return c},mQ:function(){return r},nU:function(){return d}});var i=a(7437),l=a(2265),n=a(271),t=a(3448);let r=n.fC,c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(n.aV,{ref:s,className:(0,t.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});c.displayName=n.aV.displayName;let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(n.xz,{ref:s,className:(0,t.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});o.displayName=n.xz.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(n.VY,{ref:s,className:(0,t.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});d.displayName=n.VY.displayName},3448:function(e,s,a){"use strict";a.d(s,{cn:function(){return n}});var i=a(1994),l=a(3335);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.m6)((0,i.W)(s))}}},function(e){e.O(0,[773,784,274,971,117,744],function(){return e(e.s=3832)}),_N_E=e.O()}]);