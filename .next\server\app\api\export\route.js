"use strict";(()=>{var e={};e.id=711,e.ids=[711],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1040:(e,s,t)=>{t.r(s),t.d(s,{originalPathname:()=>h,patchFetch:()=>y,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var a={};t.r(a),t.d(a,{GET:()=>u,dynamic:()=>m});var n=t(9303),i=t(8716),r=t(670),o=t(7070),l=t(8194);let m="force-dynamic";async function u(e){try{let{searchParams:s}=new URL(e.url),t=s.get("format")||"json",a=await l.db.assessment.findMany({include:{demographics:!0,riskAssessment:!0,medicalHistory:!0,mentalStatusExam:!0,symptoms:{include:{symptom:!0}},diagnoses:{include:{diagnosis:!0}}}});if("csv"===t){let e=a.map(e=>{let s=e.symptoms.map(e=>e.symptom.name).join(";"),t=e.diagnoses.map(e=>e.diagnosis.name).join(";");return{assessment_id:e.id,assessment_date:e.assessmentDate.toISOString(),assessor_name:e.assessorName,status:e.status,patient_code:e.demographics?.patientCode||null,age:e.demographics?.age||null,gender:e.demographics?.gender||null,general_location:e.demographics?.generalLocation||null,region:e.demographics?.region||null,ethnicity:e.demographics?.ethnicity||null,race:e.demographics?.race||null,education:e.demographics?.education||null,occupation:e.demographics?.occupation||null,employment_status:e.demographics?.employmentStatus||null,living_arrangement:e.demographics?.livingArrangement||null,marital_status:e.demographics?.maritalStatus||null,insurance_type:e.demographics?.insuranceType||null,suicidal_ideation:e.riskAssessment?.suicidalIdeation||!1,suicidal_plan:e.riskAssessment?.suicidalPlan||!1,suicidal_means:e.riskAssessment?.suicidalMeans||!1,suicidal_attempt_history:e.riskAssessment?.suicidalAttemptHistory||!1,suicidal_risk_level:e.riskAssessment?.suicidalRiskLevel||null,homicidal_ideation:e.riskAssessment?.homicidalIdeation||!1,violence_history:e.riskAssessment?.violenceHistory||!1,violence_risk_level:e.riskAssessment?.violenceRiskLevel||null,self_harm_history:e.riskAssessment?.selfHarmHistory||!1,self_harm_risk:e.riskAssessment?.selfHarmRisk||null,substance_use_risk:e.riskAssessment?.substanceUseRisk||null,previous_psychiatric_treatment:e.medicalHistory?.previousPsychiatricTreatment||!1,trauma_history:e.medicalHistory?.traumaHistory||!1,appearance:e.mentalStatusExam?.appearance||null,behavior:e.mentalStatusExam?.behavior||null,mood:e.mentalStatusExam?.mood||null,affect:e.mentalStatusExam?.affect||null,thought_process:e.mentalStatusExam?.thoughtProcess||null,hallucinations:e.mentalStatusExam?.hallucinations||!1,delusions:e.mentalStatusExam?.delusions||!1,orientation:e.mentalStatusExam?.orientation||null,insight:e.mentalStatusExam?.insight||null,judgment:e.mentalStatusExam?.judgment||null,symptoms:s,diagnoses:t,symptom_count:e.symptoms.length,diagnosis_count:e.diagnoses.length,created_at:e.createdAt.toISOString(),updated_at:e.updatedAt.toISOString()}});if(0===e.length)return new o.NextResponse("No data available",{status:404});let s=Object.keys(e[0]),t=[s.join(","),...e.map(e=>s.map(s=>{let t=e[s];return"string"==typeof t&&(t.includes(",")||t.includes('"'))?`"${t.replace(/"/g,'""')}"`:t||""}).join(","))].join("\n");return new o.NextResponse(t,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="psychiatric-assessments-${new Date().toISOString().split("T")[0]}.csv"`}})}{let e={metadata:{export_date:new Date().toISOString(),total_assessments:a.length,format:"json"},assessments:a};return o.NextResponse.json(e,{headers:{"Content-Disposition":`attachment; filename="psychiatric-assessments-${new Date().toISOString().split("T")[0]}.json"`}})}}catch(e){return console.error("Error exporting data:",e),o.NextResponse.json({error:"Failed to export data"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/export/route",pathname:"/api/export",filename:"route",bundlePath:"app/api/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\export\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:g}=c,h="/api/export/route";function y(){return(0,r.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}},8194:(e,s,t)=>{t.d(s,{db:()=>n});let a=require("@prisma/client"),n=globalThis.prisma??new a.PrismaClient({log:["query"]})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,972],()=>t(1040));module.exports=a})();