(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2419:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(5480),t(2029),t(5866);var a=t(3191),r=t(8716),i=t(7922),l=t.n(i),n=t(5231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5480)),"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\page.tsx"],m="/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8633:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},7002:()=>{},2163:(e,s,t)=>{Promise.resolve().then(t.bind(t,9606))},8998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(6557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9606:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(326),r=t(7577),i=t(1664),l=t(9752),n=t(384),c=t(8360),d=t(3634),o=t(4659),m=t(165),x=t(8319),h=t(4061),f=t(6464),u=t(8998);let p=(0,t(6557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var g=t(6283),j=t(2978),N=t(434);function b(){let[e,s]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"assessment-container py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[a.jsx("div",{className:"p-3 bg-gradient-to-br from-blue-500 to-teal-600 rounded-xl shadow-lg mr-4",children:a.jsx(c.Z,{className:"h-12 w-12 text-white"})}),(0,a.jsxs)("div",{className:"text-left",children:[a.jsx("h1",{className:"text-4xl font-bold text-slate-900 mb-2",children:"Psychiatric Assessment System"}),a.jsx("p",{className:"text-lg text-slate-600",children:"Professional clinical assessment platform"})]})]}),a.jsx("p",{className:"text-xl text-slate-600 max-w-3xl mx-auto",children:"Fast, reliable, and optimized for ML training data collection with anonymous patient codes"})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8 mb-12",children:[a.jsx("div",{className:"lg:col-span-2",children:(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(d.Z,{className:"h-5 w-5 mr-2 text-yellow-500"}),"Key Features"]})}),a.jsx(l.aY,{children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(o.Z,{className:"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Lightning Fast"}),a.jsx("p",{className:"text-sm text-slate-600",children:"Debounced 2-second autosave with optimized performance"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(m.Z,{className:"h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Data Protection"}),a.jsx("p",{className:"text-sm text-slate-600",children:"Local storage backup prevents data loss"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(x.Z,{className:"h-5 w-5 text-purple-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"ML-Ready Export"}),a.jsx("p",{className:"text-sm text-slate-600",children:"CSV and JSON export for machine learning"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(h.Z,{className:"h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Predefined Options"}),a.jsx("p",{className:"text-sm text-slate-600",children:"Ready-to-click education, occupation, and living options"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(f.Z,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Smart Diagnosis"}),a.jsx("p",{className:"text-sm text-slate-600",children:"Searchable diagnosis database with filtering"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(u.Z,{className:"h-5 w-5 text-indigo-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold",children:"Real-time Progress"}),a.jsx("p",{className:"text-sm text-slate-600",children:"Track completion status across all sections"})]})]})]})]})})]})}),a.jsx("div",{children:(0,a.jsxs)(l.Zb,{className:"h-full",children:[(0,a.jsxs)(l.Ol,{children:[a.jsx(l.ll,{children:"Get Started"}),a.jsx(l.SZ,{children:"Begin a new psychiatric assessment session"})]}),(0,a.jsxs)(l.aY,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-teal-50 rounded-lg border border-blue-100",children:[a.jsx("span",{className:"text-sm font-medium text-slate-700",children:"Assessment Sections"}),a.jsx("span",{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium",children:"7"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[a.jsx("span",{className:"text-sm font-medium",children:"Estimated Time"}),a.jsx("span",{className:"text-sm bg-slate-200 px-2 py-1 rounded",children:"15-30 min"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[a.jsx("span",{className:"text-sm font-medium",children:"Auto-save"}),a.jsx("span",{className:"text-sm bg-green-100 text-green-800 px-2 py-1 rounded",children:"Enabled"})]})]}),a.jsx(i.z,{onClick:()=>{s(!0),window.location.href="/assessment"},disabled:e,className:"w-full mb-3 bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white shadow-lg",size:"lg",children:e?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Starting..."]}):(0,a.jsxs)(a.Fragment,{children:["Start New Assessment",a.jsx(p,{className:"ml-2 h-4 w-4"})]})}),a.jsx(N.default,{href:"/patients",children:(0,a.jsxs)(i.z,{variant:"outline",className:"w-full mb-3 border-blue-200 hover:bg-blue-50 hover:border-blue-300",size:"lg",children:[a.jsx(h.Z,{className:"mr-2 h-4 w-4 text-blue-600"}),"Manage Patients"]})}),a.jsx(N.default,{href:"/data",children:(0,a.jsxs)(i.z,{variant:"outline",className:"w-full border-teal-200 hover:bg-teal-50 hover:border-teal-300",size:"lg",children:[a.jsx(x.Z,{className:"mr-2 h-4 w-4 text-teal-600"}),"View Data & Export"]})}),(0,a.jsxs)("div",{className:"text-xs text-slate-500 text-center mt-4 p-2 bg-white/60 rounded",children:[a.jsx(m.Z,{className:"h-3 w-3 inline mr-1"}),"Anonymous patient codes • Auto-save enabled"]})]})]})})]}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{children:(0,a.jsxs)(l.ll,{className:"flex items-center",children:[a.jsx(g.Z,{className:"h-5 w-5 mr-2"}),"Assessment Overview"]})}),a.jsx(l.aY,{children:(0,a.jsxs)(n.mQ,{defaultValue:"sections",className:"w-full",children:[(0,a.jsxs)(n.dr,{className:"grid w-full grid-cols-3",children:[a.jsx(n.SP,{value:"sections",children:"Sections"}),a.jsx(n.SP,{value:"features",children:"Features"}),a.jsx(n.SP,{value:"export",children:"Data Export"})]}),a.jsx(n.nU,{value:"sections",className:"mt-6",children:a.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{title:"Demographics",desc:"Patient information and background",icon:h.Z,color:"text-orange-600"},{title:"Symptoms",desc:"DSM-5-TR aligned symptom categories",icon:c.Z,color:"text-purple-600"},{title:"Risk Assessment",desc:"Safety and risk evaluation",icon:m.Z,color:"text-red-600"},{title:"Medical History",desc:"Medical conditions and medications",icon:g.Z,color:"text-blue-600"},{title:"Mental Status Exam",desc:"Current mental state evaluation",icon:f.Z,color:"text-green-600"},{title:"Diagnosis",desc:"Diagnostic formulation and coding",icon:x.Z,color:"text-indigo-600"},{title:"Laboratory Tests",desc:"Lab results and psychological assessments",icon:j.Z,color:"text-teal-600"}].map((e,s)=>(0,a.jsxs)("div",{className:"p-4 border border-slate-200 rounded-lg hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[a.jsx(e.icon,{className:`h-5 w-5 ${e.color} mr-2`}),a.jsx("h3",{className:"font-semibold text-slate-800",children:e.title})]}),a.jsx("p",{className:"text-sm text-slate-600",children:e.desc})]},s))})}),a.jsx(n.nU,{value:"features",className:"mt-6",children:a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-3",children:"Performance Optimizations"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Debounced autosave (2 seconds)"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Virtualized symptom selection"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Optimized state management"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Efficient data persistence"]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-3",children:"User Experience"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Intuitive form navigation"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Real-time progress tracking"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Predefined dropdown options"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Responsive design"]})]})]})]})})}),a.jsx(n.nU,{value:"export",className:"mt-6",children:a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-3",children:"Export Formats"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"CSV format for ML training"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"JSON format for applications"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Structured data format"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Metadata included"]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-3",children:"Data Quality"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Validated and clean data"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Consistent formatting"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Complete field coverage"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx(o.Z,{className:"h-4 w-4 text-green-500 mr-2"}),"Timestamp tracking"]})]})]})]})})})]})})]})]})}},1664:(e,s,t)=>{"use strict";t.d(s,{z:()=>d});var a=t(326),r=t(7577),i=t(4214),l=t(9360),n=t(1223);let c=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:s,size:t,asChild:r=!1,...l},d)=>{let o=r?i.g7:"button";return a.jsx(o,{className:(0,n.cn)(c({variant:s,size:t,className:e})),ref:d,...l})});d.displayName="Button"},9752:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>n,SZ:()=>d,Zb:()=>l,aY:()=>o,ll:()=>c});var a=t(326),r=t(7577),i=t(1223);let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));l.displayName="Card";let n=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},384:(e,s,t)=>{"use strict";t.d(s,{SP:()=>d,dr:()=>c,mQ:()=>n,nU:()=>o});var a=t(326),r=t(7577),i=t(8407),l=t(1223);let n=i.fC,c=r.forwardRef(({className:e,...s},t)=>a.jsx(i.aV,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=i.aV.displayName;let d=r.forwardRef(({className:e,...s},t)=>a.jsx(i.xz,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.xz.displayName;let o=r.forwardRef(({className:e,...s},t)=>a.jsx(i.VY,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=i.VY.displayName},1223:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(1135),r=t(1009);function i(...e){return(0,r.m6)((0,a.W)(e))}},2029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>l});var a=t(9510),r=t(5384),i=t.n(r);t(5023);let l={title:"Psychiatric Assessment System",description:"Fast and reliable psychiatric assessment system optimized for ML training data collection"};function n({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:i().className,children:a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:e})})})}},5480:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\Users\<USER>\projects\psychiatric-assessment\src\app\page.tsx#default`)},5023:()=>{}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[276,349,566,503],()=>t(2419));module.exports=a})();