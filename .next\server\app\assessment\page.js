(()=>{var e={};e.id=326,e.ids=[326],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4528:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c}),s(8200),s(2029),s(5866);var n=s(3191),a=s(8716),i=s(7922),r=s.n(i),l=s(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["assessment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8200)),"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\assessment\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\assessment\\page.tsx"],u="/assessment/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/assessment/page",pathname:"/assessment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8633:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7002:()=>{},5258:(e,t,s)=>{Promise.resolve().then(s.bind(s,1551))},7888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});let n=(0,s(6557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});let n=(0,s(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},1540:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});let n=(0,s(6557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},3855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});let n=(0,s(6557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},8307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});let n=(0,s(6557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1551:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>iW});var n,a,i,r=s(326),l=s(7577),o=s(7389),c=s(9752),d=s(384),u=s(3095),m=s(5226),h="Progress",[p,x]=(0,u.b)(h),[f,g]=p(h),v=l.forwardRef((e,t)=>{var s,n;let{__scopeProgress:a,value:i=null,max:l,getValueLabel:o=b,...c}=e;(l||0===l)&&!C(l)&&console.error((s=`${l}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let d=C(l)?l:100;null===i||S(i,d)||console.error((n=`${i}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=S(i,d)?i:null,h=w(u)?o(u,d):void 0;return(0,r.jsx)(f,{scope:a,value:u,max:d,children:(0,r.jsx)(m.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":w(u)?u:void 0,"aria-valuetext":h,role:"progressbar","data-state":N(u,d),"data-value":u??void 0,"data-max":d,...c,ref:t})})});v.displayName=h;var y="ProgressIndicator",j=l.forwardRef((e,t)=>{let{__scopeProgress:s,...n}=e,a=g(y,s);return(0,r.jsx)(m.WV.div,{"data-state":N(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...n,ref:t})});function b(e,t){return`${Math.round(e/t*100)}%`}function N(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function w(e){return"number"==typeof e}function C(e){return w(e)&&!isNaN(e)&&e>0}function S(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}j.displayName=y;var k=s(1223);let E=l.forwardRef(({className:e,value:t,...s},n)=>r.jsx(v,{ref:n,className:(0,k.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:r.jsx(j,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));E.displayName=v.displayName;var P=s(1664),A=s(4061),R=s(8360),D=s(165),M=s(6283),T=s(6464),L=s(8319),O=s(2978),I=s(6333),F=s(6557);let H=(0,F.Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);var V=s(7888),B=s(4659),Z=s(1540),W=s(434),z=s(1190),U=l.forwardRef((e,t)=>(0,r.jsx)(m.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));U.displayName="Label";let Y=(0,s(9360).j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),q=l.forwardRef(({className:e,...t},s)=>r.jsx(U,{ref:s,className:(0,k.cn)(Y(),e),...t}));q.displayName=U.displayName;var _=s(962);function G(e,[t,s]){return Math.min(s,Math.max(t,e))}var $=s(2561),K=s(545),X=s(8051),J=s(7124),Q=s(5049),ee="dismissableLayer.update",et=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),es=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:n,onPointerDownOutside:i,onFocusOutside:o,onInteractOutside:c,onDismiss:d,...u}=e,h=l.useContext(et),[p,x]=l.useState(null),f=p?.ownerDocument??globalThis?.document,[,g]=l.useState({}),v=(0,X.e)(t,e=>x(e)),y=Array.from(h.layers),[j]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),b=y.indexOf(j),N=p?y.indexOf(p):-1,w=h.layersWithOutsidePointerEventsDisabled.size>0,C=N>=b,S=function(e,t=globalThis?.document){let s=(0,Q.W)(e),n=l.useRef(!1),a=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){ea("dismissableLayer.pointerDownOutside",s,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=n,t.addEventListener("click",a.current,{once:!0})):n()}else t.removeEventListener("click",a.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,s]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,s=[...h.branches].some(e=>e.contains(t));!C||s||(i?.(e),c?.(e),e.defaultPrevented||d?.())},f),k=function(e,t=globalThis?.document){let s=(0,Q.W)(e),n=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!n.current&&ea("dismissableLayer.focusOutside",s,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,s]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...h.branches].some(e=>e.contains(t))||(o?.(e),c?.(e),e.defaultPrevented||d?.())},f);return function(e,t=globalThis?.document){let s=(0,Q.W)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&s(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[s,t])}(e=>{N!==h.layers.size-1||(n?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},f),l.useEffect(()=>{if(p)return s&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(a=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(p)),h.layers.add(p),en(),()=>{s&&1===h.layersWithOutsidePointerEventsDisabled.size&&(f.body.style.pointerEvents=a)}},[p,f,s,h]),l.useEffect(()=>()=>{p&&(h.layers.delete(p),h.layersWithOutsidePointerEventsDisabled.delete(p),en())},[p,h]),l.useEffect(()=>{let e=()=>g({});return document.addEventListener(ee,e),()=>document.removeEventListener(ee,e)},[]),(0,r.jsx)(m.WV.div,{...u,ref:v,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:(0,$.M)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,$.M)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,$.M)(e.onPointerDownCapture,S.onPointerDownCapture)})});function en(){let e=new CustomEvent(ee);document.dispatchEvent(e)}function ea(e,t,s,{discrete:n}){let a=s.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:s});t&&a.addEventListener(e,t,{once:!0}),n?(0,m.jH)(a,i):a.dispatchEvent(i)}es.displayName="DismissableLayer",l.forwardRef((e,t)=>{let s=l.useContext(et),n=l.useRef(null),a=(0,X.e)(t,n);return l.useEffect(()=>{let e=n.current;if(e)return s.branches.add(e),()=>{s.branches.delete(e)}},[s.branches]),(0,r.jsx)(m.WV.div,{...e,ref:a})}).displayName="DismissableLayerBranch";var ei=0;function er(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var el="focusScope.autoFocusOnMount",eo="focusScope.autoFocusOnUnmount",ec={bubbles:!1,cancelable:!0},ed=l.forwardRef((e,t)=>{let{loop:s=!1,trapped:n=!1,onMountAutoFocus:a,onUnmountAutoFocus:i,...o}=e,[c,d]=l.useState(null),u=(0,Q.W)(a),h=(0,Q.W)(i),p=l.useRef(null),x=(0,X.e)(t,e=>d(e)),f=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let e=function(e){if(f.paused||!c)return;let t=e.target;c.contains(t)?p.current=t:eh(p.current,{select:!0})},t=function(e){if(f.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||eh(p.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let s=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&eh(c)});return c&&s.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),s.disconnect()}}},[n,c,f.paused]),l.useEffect(()=>{if(c){ep.add(f);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(el,ec);c.addEventListener(el,u),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let s=document.activeElement;for(let n of e)if(eh(n,{select:t}),document.activeElement!==s)return}(eu(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&eh(c))}return()=>{c.removeEventListener(el,u),setTimeout(()=>{let t=new CustomEvent(eo,ec);c.addEventListener(eo,h),c.dispatchEvent(t),t.defaultPrevented||eh(e??document.body,{select:!0}),c.removeEventListener(eo,h),ep.remove(f)},0)}}},[c,u,h,f]);let g=l.useCallback(e=>{if(!s&&!n||f.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[n,i]=function(e){let t=eu(e);return[em(t,e),em(t.reverse(),e)]}(t);n&&i?e.shiftKey||a!==i?e.shiftKey&&a===n&&(e.preventDefault(),s&&eh(i,{select:!0})):(e.preventDefault(),s&&eh(n,{select:!0})):a===t&&e.preventDefault()}},[s,n,f.paused]);return(0,r.jsx)(m.WV.div,{tabIndex:-1,...o,ref:x,onKeyDown:g})});function eu(e){let t=[],s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)t.push(s.currentNode);return t}function em(e,t){for(let s of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(s,{upTo:t}))return s}function eh(e,{select:t=!1}={}){if(e&&e.focus){var s;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(s=e)instanceof HTMLInputElement&&"select"in s&&t&&e.select()}}ed.displayName="FocusScope";var ep=function(){let e=[];return{add(t){let s=e[0];t!==s&&s?.pause(),(e=ex(e,t)).unshift(t)},remove(t){e=ex(e,t),e[0]?.resume()}}}();function ex(e,t){let s=[...e],n=s.indexOf(t);return -1!==n&&s.splice(n,1),s}var ef=s(8957);let eg=["top","right","bottom","left"],ev=Math.min,ey=Math.max,ej=Math.round,eb=Math.floor,eN=e=>({x:e,y:e}),ew={left:"right",right:"left",bottom:"top",top:"bottom"},eC={start:"end",end:"start"};function eS(e,t){return"function"==typeof e?e(t):e}function ek(e){return e.split("-")[0]}function eE(e){return e.split("-")[1]}function eP(e){return"x"===e?"y":"x"}function eA(e){return"y"===e?"height":"width"}let eR=new Set(["top","bottom"]);function eD(e){return eR.has(ek(e))?"y":"x"}function eM(e){return e.replace(/start|end/g,e=>eC[e])}let eT=["left","right"],eL=["right","left"],eO=["top","bottom"],eI=["bottom","top"];function eF(e){return e.replace(/left|right|bottom|top/g,e=>ew[e])}function eH(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eV(e){let{x:t,y:s,width:n,height:a}=e;return{width:n,height:a,top:s,left:t,right:t+n,bottom:s+a,x:t,y:s}}function eB(e,t,s){let n,{reference:a,floating:i}=e,r=eD(t),l=eP(eD(t)),o=eA(l),c=ek(t),d="y"===r,u=a.x+a.width/2-i.width/2,m=a.y+a.height/2-i.height/2,h=a[o]/2-i[o]/2;switch(c){case"top":n={x:u,y:a.y-i.height};break;case"bottom":n={x:u,y:a.y+a.height};break;case"right":n={x:a.x+a.width,y:m};break;case"left":n={x:a.x-i.width,y:m};break;default:n={x:a.x,y:a.y}}switch(eE(t)){case"start":n[l]-=h*(s&&d?-1:1);break;case"end":n[l]+=h*(s&&d?-1:1)}return n}let eZ=async(e,t,s)=>{let{placement:n="bottom",strategy:a="absolute",middleware:i=[],platform:r}=s,l=i.filter(Boolean),o=await (null==r.isRTL?void 0:r.isRTL(t)),c=await r.getElementRects({reference:e,floating:t,strategy:a}),{x:d,y:u}=eB(c,n,o),m=n,h={},p=0;for(let s=0;s<l.length;s++){let{name:i,fn:x}=l[s],{x:f,y:g,data:v,reset:y}=await x({x:d,y:u,initialPlacement:n,placement:m,strategy:a,middlewareData:h,rects:c,platform:r,elements:{reference:e,floating:t}});d=null!=f?f:d,u=null!=g?g:u,h={...h,[i]:{...h[i],...v}},y&&p<=50&&(p++,"object"==typeof y&&(y.placement&&(m=y.placement),y.rects&&(c=!0===y.rects?await r.getElementRects({reference:e,floating:t,strategy:a}):y.rects),{x:d,y:u}=eB(c,m,o)),s=-1)}return{x:d,y:u,placement:m,strategy:a,middlewareData:h}};async function eW(e,t){var s;void 0===t&&(t={});let{x:n,y:a,platform:i,rects:r,elements:l,strategy:o}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:m=!1,padding:h=0}=eS(t,e),p=eH(h),x=l[m?"floating"===u?"reference":"floating":u],f=eV(await i.getClippingRect({element:null==(s=await (null==i.isElement?void 0:i.isElement(x)))||s?x:x.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:d,strategy:o})),g="floating"===u?{x:n,y:a,width:r.floating.width,height:r.floating.height}:r.reference,v=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),y=await (null==i.isElement?void 0:i.isElement(v))&&await (null==i.getScale?void 0:i.getScale(v))||{x:1,y:1},j=eV(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:v,strategy:o}):g);return{top:(f.top-j.top+p.top)/y.y,bottom:(j.bottom-f.bottom+p.bottom)/y.y,left:(f.left-j.left+p.left)/y.x,right:(j.right-f.right+p.right)/y.x}}function ez(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eU(e){return eg.some(t=>e[t]>=0)}let eY=new Set(["left","top"]);async function eq(e,t){let{placement:s,platform:n,elements:a}=e,i=await (null==n.isRTL?void 0:n.isRTL(a.floating)),r=ek(s),l=eE(s),o="y"===eD(s),c=eY.has(r)?-1:1,d=i&&o?-1:1,u=eS(t,e),{mainAxis:m,crossAxis:h,alignmentAxis:p}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return l&&"number"==typeof p&&(h="end"===l?-1*p:p),o?{x:h*d,y:m*c}:{x:m*c,y:h*d}}function e_(){return"undefined"!=typeof window}function eG(e){return eX(e)?(e.nodeName||"").toLowerCase():"#document"}function e$(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eK(e){var t;return null==(t=(eX(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eX(e){return!!e_()&&(e instanceof Node||e instanceof e$(e).Node)}function eJ(e){return!!e_()&&(e instanceof Element||e instanceof e$(e).Element)}function eQ(e){return!!e_()&&(e instanceof HTMLElement||e instanceof e$(e).HTMLElement)}function e0(e){return!!e_()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof e$(e).ShadowRoot)}let e1=new Set(["inline","contents"]);function e2(e){let{overflow:t,overflowX:s,overflowY:n,display:a}=tn(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+s)&&!e1.has(a)}let e4=new Set(["table","td","th"]),e5=[":popover-open",":modal"];function e3(e){return e5.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let e6=["transform","translate","scale","rotate","perspective"],e8=["transform","translate","scale","rotate","perspective","filter"],e7=["paint","layout","strict","content"];function e9(e){let t=te(),s=eJ(e)?tn(e):e;return e6.some(e=>!!s[e]&&"none"!==s[e])||!!s.containerType&&"normal"!==s.containerType||!t&&!!s.backdropFilter&&"none"!==s.backdropFilter||!t&&!!s.filter&&"none"!==s.filter||e8.some(e=>(s.willChange||"").includes(e))||e7.some(e=>(s.contain||"").includes(e))}function te(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let tt=new Set(["html","body","#document"]);function ts(e){return tt.has(eG(e))}function tn(e){return e$(e).getComputedStyle(e)}function ta(e){return eJ(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ti(e){if("html"===eG(e))return e;let t=e.assignedSlot||e.parentNode||e0(e)&&e.host||eK(e);return e0(t)?t.host:t}function tr(e,t,s){var n;void 0===t&&(t=[]),void 0===s&&(s=!0);let a=function e(t){let s=ti(t);return ts(s)?t.ownerDocument?t.ownerDocument.body:t.body:eQ(s)&&e2(s)?s:e(s)}(e),i=a===(null==(n=e.ownerDocument)?void 0:n.body),r=e$(a);if(i){let e=tl(r);return t.concat(r,r.visualViewport||[],e2(a)?a:[],e&&s?tr(e):[])}return t.concat(a,tr(a,[],s))}function tl(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function to(e){let t=tn(e),s=parseFloat(t.width)||0,n=parseFloat(t.height)||0,a=eQ(e),i=a?e.offsetWidth:s,r=a?e.offsetHeight:n,l=ej(s)!==i||ej(n)!==r;return l&&(s=i,n=r),{width:s,height:n,$:l}}function tc(e){return eJ(e)?e:e.contextElement}function td(e){let t=tc(e);if(!eQ(t))return eN(1);let s=t.getBoundingClientRect(),{width:n,height:a,$:i}=to(t),r=(i?ej(s.width):s.width)/n,l=(i?ej(s.height):s.height)/a;return r&&Number.isFinite(r)||(r=1),l&&Number.isFinite(l)||(l=1),{x:r,y:l}}let tu=eN(0);function tm(e){let t=e$(e);return te()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:tu}function th(e,t,s,n){var a;void 0===t&&(t=!1),void 0===s&&(s=!1);let i=e.getBoundingClientRect(),r=tc(e),l=eN(1);t&&(n?eJ(n)&&(l=td(n)):l=td(e));let o=(void 0===(a=s)&&(a=!1),n&&(!a||n===e$(r))&&a)?tm(r):eN(0),c=(i.left+o.x)/l.x,d=(i.top+o.y)/l.y,u=i.width/l.x,m=i.height/l.y;if(r){let e=e$(r),t=n&&eJ(n)?e$(n):n,s=e,a=tl(s);for(;a&&n&&t!==s;){let e=td(a),t=a.getBoundingClientRect(),n=tn(a),i=t.left+(a.clientLeft+parseFloat(n.paddingLeft))*e.x,r=t.top+(a.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,u*=e.x,m*=e.y,c+=i,d+=r,a=tl(s=e$(a))}}return eV({width:u,height:m,x:c,y:d})}function tp(e,t){let s=ta(e).scrollLeft;return t?t.left+s:th(eK(e)).left+s}function tx(e,t,s){void 0===s&&(s=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(s?0:tp(e,n)),y:n.top+t.scrollTop}}let tf=new Set(["absolute","fixed"]);function tg(e,t,s){let n;if("viewport"===t)n=function(e,t){let s=e$(e),n=eK(e),a=s.visualViewport,i=n.clientWidth,r=n.clientHeight,l=0,o=0;if(a){i=a.width,r=a.height;let e=te();(!e||e&&"fixed"===t)&&(l=a.offsetLeft,o=a.offsetTop)}return{width:i,height:r,x:l,y:o}}(e,s);else if("document"===t)n=function(e){let t=eK(e),s=ta(e),n=e.ownerDocument.body,a=ey(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=ey(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),r=-s.scrollLeft+tp(e),l=-s.scrollTop;return"rtl"===tn(n).direction&&(r+=ey(t.clientWidth,n.clientWidth)-a),{width:a,height:i,x:r,y:l}}(eK(e));else if(eJ(t))n=function(e,t){let s=th(e,!0,"fixed"===t),n=s.top+e.clientTop,a=s.left+e.clientLeft,i=eQ(e)?td(e):eN(1),r=e.clientWidth*i.x;return{width:r,height:e.clientHeight*i.y,x:a*i.x,y:n*i.y}}(t,s);else{let s=tm(e);n={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return eV(n)}function tv(e){return"static"===tn(e).position}function ty(e,t){if(!eQ(e)||"fixed"===tn(e).position)return null;if(t)return t(e);let s=e.offsetParent;return eK(e)===s&&(s=s.ownerDocument.body),s}function tj(e,t){var s;let n=e$(e);if(e3(e))return n;if(!eQ(e)){let t=ti(e);for(;t&&!ts(t);){if(eJ(t)&&!tv(t))return t;t=ti(t)}return n}let a=ty(e,t);for(;a&&(s=a,e4.has(eG(s)))&&tv(a);)a=ty(a,t);return a&&ts(a)&&tv(a)&&!e9(a)?n:a||function(e){let t=ti(e);for(;eQ(t)&&!ts(t);){if(e9(t))return t;if(e3(t))break;t=ti(t)}return null}(e)||n}let tb=async function(e){let t=this.getOffsetParent||tj,s=this.getDimensions,n=await s(e.floating);return{reference:function(e,t,s){let n=eQ(t),a=eK(t),i="fixed"===s,r=th(e,!0,i,t),l={scrollLeft:0,scrollTop:0},o=eN(0);if(n||!n&&!i){if(("body"!==eG(t)||e2(a))&&(l=ta(t)),n){let e=th(t,!0,i,t);o.x=e.x+t.clientLeft,o.y=e.y+t.clientTop}else a&&(o.x=tp(a))}i&&!n&&a&&(o.x=tp(a));let c=!a||n||i?eN(0):tx(a,l);return{x:r.left+l.scrollLeft-o.x-c.x,y:r.top+l.scrollTop-o.y-c.y,width:r.width,height:r.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},tN={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:s,offsetParent:n,strategy:a}=e,i="fixed"===a,r=eK(n),l=!!t&&e3(t.floating);if(n===r||l&&i)return s;let o={scrollLeft:0,scrollTop:0},c=eN(1),d=eN(0),u=eQ(n);if((u||!u&&!i)&&(("body"!==eG(n)||e2(r))&&(o=ta(n)),eQ(n))){let e=th(n);c=td(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let m=!r||u||i?eN(0):tx(r,o,!0);return{width:s.width*c.x,height:s.height*c.y,x:s.x*c.x-o.scrollLeft*c.x+d.x+m.x,y:s.y*c.y-o.scrollTop*c.y+d.y+m.y}},getDocumentElement:eK,getClippingRect:function(e){let{element:t,boundary:s,rootBoundary:n,strategy:a}=e,i=[..."clippingAncestors"===s?e3(t)?[]:function(e,t){let s=t.get(e);if(s)return s;let n=tr(e,[],!1).filter(e=>eJ(e)&&"body"!==eG(e)),a=null,i="fixed"===tn(e).position,r=i?ti(e):e;for(;eJ(r)&&!ts(r);){let t=tn(r),s=e9(r);s||"fixed"!==t.position||(a=null),(i?!s&&!a:!s&&"static"===t.position&&!!a&&tf.has(a.position)||e2(r)&&!s&&function e(t,s){let n=ti(t);return!(n===s||!eJ(n)||ts(n))&&("fixed"===tn(n).position||e(n,s))}(e,r))?n=n.filter(e=>e!==r):a=t,r=ti(r)}return t.set(e,n),n}(t,this._c):[].concat(s),n],r=i[0],l=i.reduce((e,s)=>{let n=tg(t,s,a);return e.top=ey(n.top,e.top),e.right=ev(n.right,e.right),e.bottom=ev(n.bottom,e.bottom),e.left=ey(n.left,e.left),e},tg(t,r,a));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:tj,getElementRects:tb,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:s}=to(e);return{width:t,height:s}},getScale:td,isElement:eJ,isRTL:function(e){return"rtl"===tn(e).direction}};function tw(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let tC=e=>({name:"arrow",options:e,async fn(t){let{x:s,y:n,placement:a,rects:i,platform:r,elements:l,middlewareData:o}=t,{element:c,padding:d=0}=eS(e,t)||{};if(null==c)return{};let u=eH(d),m={x:s,y:n},h=eP(eD(a)),p=eA(h),x=await r.getDimensions(c),f="y"===h,g=f?"clientHeight":"clientWidth",v=i.reference[p]+i.reference[h]-m[h]-i.floating[p],y=m[h]-i.reference[h],j=await (null==r.getOffsetParent?void 0:r.getOffsetParent(c)),b=j?j[g]:0;b&&await (null==r.isElement?void 0:r.isElement(j))||(b=l.floating[g]||i.floating[p]);let N=b/2-x[p]/2-1,w=ev(u[f?"top":"left"],N),C=ev(u[f?"bottom":"right"],N),S=b-x[p]-C,k=b/2-x[p]/2+(v/2-y/2),E=ey(w,ev(k,S)),P=!o.arrow&&null!=eE(a)&&k!==E&&i.reference[p]/2-(k<w?w:C)-x[p]/2<0,A=P?k<w?k-w:k-S:0;return{[h]:m[h]+A,data:{[h]:E,centerOffset:k-E-A,...P&&{alignmentOffset:A}},reset:P}}}),tS=(e,t,s)=>{let n=new Map,a={platform:tN,...s},i={...a.platform,_c:n};return eZ(e,t,{...a,platform:i})};var tk="undefined"!=typeof document?l.useLayoutEffect:function(){};function tE(e,t){let s,n,a;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((s=e.length)!==t.length)return!1;for(n=s;0!=n--;)if(!tE(e[n],t[n]))return!1;return!0}if((s=(a=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=s;0!=n--;)if(!({}).hasOwnProperty.call(t,a[n]))return!1;for(n=s;0!=n--;){let s=a[n];if(("_owner"!==s||!e.$$typeof)&&!tE(e[s],t[s]))return!1}return!0}return e!=e&&t!=t}function tP(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tA(e,t){let s=tP(e);return Math.round(t*s)/s}function tR(e){let t=l.useRef(e);return tk(()=>{t.current=e}),t}let tD=e=>({name:"arrow",options:e,fn(t){let{element:s,padding:n}="function"==typeof e?e(t):e;return s&&({}).hasOwnProperty.call(s,"current")?null!=s.current?tC({element:s.current,padding:n}).fn(t):{}:s?tC({element:s,padding:n}).fn(t):{}}}),tM=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var s,n;let{x:a,y:i,placement:r,middlewareData:l}=t,o=await eq(t,e);return r===(null==(s=l.offset)?void 0:s.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:a+o.x,y:i+o.y,data:{...o,placement:r}}}}}(e),options:[e,t]}),tT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:s,y:n,placement:a}=t,{mainAxis:i=!0,crossAxis:r=!1,limiter:l={fn:e=>{let{x:t,y:s}=e;return{x:t,y:s}}},...o}=eS(e,t),c={x:s,y:n},d=await eW(t,o),u=eD(ek(a)),m=eP(u),h=c[m],p=c[u];if(i){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",s=h+d[e],n=h-d[t];h=ey(s,ev(h,n))}if(r){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",s=p+d[e],n=p-d[t];p=ey(s,ev(p,n))}let x=l.fn({...t,[m]:h,[u]:p});return{...x,data:{x:x.x-s,y:x.y-n,enabled:{[m]:i,[u]:r}}}}}}(e),options:[e,t]}),tL=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:s,y:n,placement:a,rects:i,middlewareData:r}=t,{offset:l=0,mainAxis:o=!0,crossAxis:c=!0}=eS(e,t),d={x:s,y:n},u=eD(a),m=eP(u),h=d[m],p=d[u],x=eS(l,t),f="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(o){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+f.mainAxis,s=i.reference[m]+i.reference[e]-f.mainAxis;h<t?h=t:h>s&&(h=s)}if(c){var g,v;let e="y"===m?"width":"height",t=eY.has(ek(a)),s=i.reference[u]-i.floating[e]+(t&&(null==(g=r.offset)?void 0:g[u])||0)+(t?0:f.crossAxis),n=i.reference[u]+i.reference[e]+(t?0:(null==(v=r.offset)?void 0:v[u])||0)-(t?f.crossAxis:0);p<s?p=s:p>n&&(p=n)}return{[m]:h,[u]:p}}}}(e),options:[e,t]}),tO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var s,n,a,i,r;let{placement:l,middlewareData:o,rects:c,initialPlacement:d,platform:u,elements:m}=t,{mainAxis:h=!0,crossAxis:p=!0,fallbackPlacements:x,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0,...y}=eS(e,t);if(null!=(s=o.arrow)&&s.alignmentOffset)return{};let j=ek(l),b=eD(d),N=ek(d)===d,w=await (null==u.isRTL?void 0:u.isRTL(m.floating)),C=x||(N||!v?[eF(d)]:function(e){let t=eF(e);return[eM(e),t,eM(t)]}(d)),S="none"!==g;!x&&S&&C.push(...function(e,t,s,n){let a=eE(e),i=function(e,t,s){switch(e){case"top":case"bottom":if(s)return t?eL:eT;return t?eT:eL;case"left":case"right":return t?eO:eI;default:return[]}}(ek(e),"start"===s,n);return a&&(i=i.map(e=>e+"-"+a),t&&(i=i.concat(i.map(eM)))),i}(d,v,g,w));let k=[d,...C],E=await eW(t,y),P=[],A=(null==(n=o.flip)?void 0:n.overflows)||[];if(h&&P.push(E[j]),p){let e=function(e,t,s){void 0===s&&(s=!1);let n=eE(e),a=eP(eD(e)),i=eA(a),r="x"===a?n===(s?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(r=eF(r)),[r,eF(r)]}(l,c,w);P.push(E[e[0]],E[e[1]])}if(A=[...A,{placement:l,overflows:P}],!P.every(e=>e<=0)){let e=((null==(a=o.flip)?void 0:a.index)||0)+1,t=k[e];if(t&&(!("alignment"===p&&b!==eD(t))||A.every(e=>eD(e.placement)!==b||e.overflows[0]>0)))return{data:{index:e,overflows:A},reset:{placement:t}};let s=null==(i=A.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!s)switch(f){case"bestFit":{let e=null==(r=A.filter(e=>{if(S){let t=eD(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:r[0];e&&(s=e);break}case"initialPlacement":s=d}if(l!==s)return{reset:{placement:s}}}return{}}}}(e),options:[e,t]}),tI=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var s,n;let a,i;let{placement:r,rects:l,platform:o,elements:c}=t,{apply:d=()=>{},...u}=eS(e,t),m=await eW(t,u),h=ek(r),p=eE(r),x="y"===eD(r),{width:f,height:g}=l.floating;"top"===h||"bottom"===h?(a=h,i=p===(await (null==o.isRTL?void 0:o.isRTL(c.floating))?"start":"end")?"left":"right"):(i=h,a="end"===p?"top":"bottom");let v=g-m.top-m.bottom,y=f-m.left-m.right,j=ev(g-m[a],v),b=ev(f-m[i],y),N=!t.middlewareData.shift,w=j,C=b;if(null!=(s=t.middlewareData.shift)&&s.enabled.x&&(C=y),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(w=v),N&&!p){let e=ey(m.left,0),t=ey(m.right,0),s=ey(m.top,0),n=ey(m.bottom,0);x?C=f-2*(0!==e||0!==t?e+t:ey(m.left,m.right)):w=g-2*(0!==s||0!==n?s+n:ey(m.top,m.bottom))}await d({...t,availableWidth:C,availableHeight:w});let S=await o.getDimensions(c.floating);return f!==S.width||g!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),tF=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:s}=t,{strategy:n="referenceHidden",...a}=eS(e,t);switch(n){case"referenceHidden":{let e=ez(await eW(t,{...a,elementContext:"reference"}),s.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:eU(e)}}}case"escaped":{let e=ez(await eW(t,{...a,altBoundary:!0}),s.floating);return{data:{escapedOffsets:e,escaped:eU(e)}}}default:return{}}}}}(e),options:[e,t]}),tH=(e,t)=>({...tD(e),options:[e,t]});var tV=l.forwardRef((e,t)=>{let{children:s,width:n=10,height:a=5,...i}=e;return(0,r.jsx)(m.WV.svg,{...i,ref:t,width:n,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?s:(0,r.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tV.displayName="Arrow";var tB=s(5819);function tZ(e){let[t,s]=l.useState(void 0);return(0,tB.b)(()=>{if(e){s({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,a;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,a=t.blockSize}else n=e.offsetWidth,a=e.offsetHeight;s({width:n,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}s(void 0)},[e]),t}var tW="Popper",[tz,tU]=(0,u.b)(tW),[tY,tq]=tz(tW),t_=e=>{let{__scopePopper:t,children:s}=e,[n,a]=l.useState(null);return(0,r.jsx)(tY,{scope:t,anchor:n,onAnchorChange:a,children:s})};t_.displayName=tW;var tG="PopperAnchor",t$=l.forwardRef((e,t)=>{let{__scopePopper:s,virtualRef:n,...a}=e,i=tq(tG,s),o=l.useRef(null),c=(0,X.e)(t,o);return l.useEffect(()=>{i.onAnchorChange(n?.current||o.current)}),n?null:(0,r.jsx)(m.WV.div,{...a,ref:c})});t$.displayName=tG;var tK="PopperContent",[tX,tJ]=tz(tK),tQ=l.forwardRef((e,t)=>{let{__scopePopper:s,side:n="bottom",sideOffset:a=0,align:i="center",alignOffset:o=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:u=[],collisionPadding:h=0,sticky:p="partial",hideWhenDetached:x=!1,updatePositionStrategy:f="optimized",onPlaced:g,...v}=e,y=tq(tK,s),[j,b]=l.useState(null),N=(0,X.e)(t,e=>b(e)),[w,C]=l.useState(null),S=tZ(w),k=S?.width??0,E=S?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},A=Array.isArray(u)?u:[u],R=A.length>0,D={padding:P,boundary:A.filter(t4),altBoundary:R},{refs:M,floatingStyles:T,placement:L,isPositioned:O,middlewareData:I}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:s="absolute",middleware:n=[],platform:a,elements:{reference:i,floating:r}={},transform:o=!0,whileElementsMounted:c,open:d}=e,[u,m]=l.useState({x:0,y:0,strategy:s,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=l.useState(n);tE(h,n)||p(n);let[x,f]=l.useState(null),[g,v]=l.useState(null),y=l.useCallback(e=>{e!==w.current&&(w.current=e,f(e))},[]),j=l.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=i||x,N=r||g,w=l.useRef(null),C=l.useRef(null),S=l.useRef(u),k=null!=c,E=tR(c),P=tR(a),A=tR(d),R=l.useCallback(()=>{if(!w.current||!C.current)return;let e={placement:t,strategy:s,middleware:h};P.current&&(e.platform=P.current),tS(w.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};D.current&&!tE(S.current,t)&&(S.current=t,_.flushSync(()=>{m(t)}))})},[h,t,s,P,A]);tk(()=>{!1===d&&S.current.isPositioned&&(S.current.isPositioned=!1,m(e=>({...e,isPositioned:!1})))},[d]);let D=l.useRef(!1);tk(()=>(D.current=!0,()=>{D.current=!1}),[]),tk(()=>{if(b&&(w.current=b),N&&(C.current=N),b&&N){if(E.current)return E.current(b,N,R);R()}},[b,N,R,E,k]);let M=l.useMemo(()=>({reference:w,floating:C,setReference:y,setFloating:j}),[y,j]),T=l.useMemo(()=>({reference:b,floating:N}),[b,N]),L=l.useMemo(()=>{let e={position:s,left:0,top:0};if(!T.floating)return e;let t=tA(T.floating,u.x),n=tA(T.floating,u.y);return o?{...e,transform:"translate("+t+"px, "+n+"px)",...tP(T.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:t,top:n}},[s,o,T.floating,u.x,u.y]);return l.useMemo(()=>({...u,update:R,refs:M,elements:T,floatingStyles:L}),[u,R,M,T,L])}({strategy:"fixed",placement:n+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,s,n){let a;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:o="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=tc(e),u=i||r?[...d?tr(d):[],...tr(t)]:[];u.forEach(e=>{i&&e.addEventListener("scroll",s,{passive:!0}),r&&e.addEventListener("resize",s)});let m=d&&o?function(e,t){let s,n=null,a=eK(e);function i(){var e;clearTimeout(s),null==(e=n)||e.disconnect(),n=null}return function r(l,o){void 0===l&&(l=!1),void 0===o&&(o=1),i();let c=e.getBoundingClientRect(),{left:d,top:u,width:m,height:h}=c;if(l||t(),!m||!h)return;let p=eb(u),x=eb(a.clientWidth-(d+m)),f={rootMargin:-p+"px "+-x+"px "+-eb(a.clientHeight-(u+h))+"px "+-eb(d)+"px",threshold:ey(0,ev(1,o))||1},g=!0;function v(t){let n=t[0].intersectionRatio;if(n!==o){if(!g)return r();n?r(!1,n):s=setTimeout(()=>{r(!1,1e-7)},1e3)}1!==n||tw(c,e.getBoundingClientRect())||r(),g=!1}try{n=new IntersectionObserver(v,{...f,root:a.ownerDocument})}catch(e){n=new IntersectionObserver(v,f)}n.observe(e)}(!0),i}(d,s):null,h=-1,p=null;l&&(p=new ResizeObserver(e=>{let[n]=e;n&&n.target===d&&p&&(p.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),s()}),d&&!c&&p.observe(d),p.observe(t));let x=c?th(e):null;return c&&function t(){let n=th(e);x&&!tw(x,n)&&s(),x=n,a=requestAnimationFrame(t)}(),s(),()=>{var e;u.forEach(e=>{i&&e.removeEventListener("scroll",s),r&&e.removeEventListener("resize",s)}),null==m||m(),null==(e=p)||e.disconnect(),p=null,c&&cancelAnimationFrame(a)}})(...e,{animationFrame:"always"===f}),elements:{reference:y.anchor},middleware:[tM({mainAxis:a+E,alignmentAxis:o}),d&&tT({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?tL():void 0,...D}),d&&tO({...D}),tI({...D,apply:({elements:e,rects:t,availableWidth:s,availableHeight:n})=>{let{width:a,height:i}=t.reference,r=e.floating.style;r.setProperty("--radix-popper-available-width",`${s}px`),r.setProperty("--radix-popper-available-height",`${n}px`),r.setProperty("--radix-popper-anchor-width",`${a}px`),r.setProperty("--radix-popper-anchor-height",`${i}px`)}}),w&&tH({element:w,padding:c}),t5({arrowWidth:k,arrowHeight:E}),x&&tF({strategy:"referenceHidden",...D})]}),[F,H]=t3(L),V=(0,Q.W)(g);(0,tB.b)(()=>{O&&V?.()},[O,V]);let B=I.arrow?.x,Z=I.arrow?.y,W=I.arrow?.centerOffset!==0,[z,U]=l.useState();return(0,tB.b)(()=>{j&&U(window.getComputedStyle(j).zIndex)},[j]),(0,r.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...T,transform:O?T.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:z,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,r.jsx)(tX,{scope:s,placedSide:F,onArrowChange:C,arrowX:B,arrowY:Z,shouldHideArrow:W,children:(0,r.jsx)(m.WV.div,{"data-side":F,"data-align":H,...v,ref:N,style:{...v.style,animation:O?void 0:"none"}})})})});tQ.displayName=tK;var t0="PopperArrow",t1={top:"bottom",right:"left",bottom:"top",left:"right"},t2=l.forwardRef(function(e,t){let{__scopePopper:s,...n}=e,a=tJ(t0,s),i=t1[a.placedSide];return(0,r.jsx)("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:(0,r.jsx)(tV,{...n,ref:t,style:{...n.style,display:"block"}})})});function t4(e){return null!==e}t2.displayName=t0;var t5=e=>({name:"transformOrigin",options:e,fn(t){let{placement:s,rects:n,middlewareData:a}=t,i=a.arrow?.centerOffset!==0,r=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[o,c]=t3(s),d={start:"0%",center:"50%",end:"100%"}[c],u=(a.arrow?.x??0)+r/2,m=(a.arrow?.y??0)+l/2,h="",p="";return"bottom"===o?(h=i?d:`${u}px`,p=`${-l}px`):"top"===o?(h=i?d:`${u}px`,p=`${n.floating.height+l}px`):"right"===o?(h=`${-l}px`,p=i?d:`${m}px`):"left"===o&&(h=`${n.floating.width+l}px`,p=i?d:`${m}px`),{data:{x:h,y:p}}}});function t3(e){let[t,s="center"]=e.split("-");return[t,s]}var t6=l.forwardRef((e,t)=>{let{container:s,...n}=e,[a,i]=l.useState(!1);(0,tB.b)(()=>i(!0),[]);let o=s||a&&globalThis?.document?.body;return o?_.createPortal((0,r.jsx)(m.WV.div,{...n,ref:t}),o):null});t6.displayName="Portal";var t8=s(4214),t7=s(2067);function t9(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var se=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,r.jsx)(m.WV.span,{...e,ref:t,style:{...se,...e.style}})).displayName="VisuallyHidden";var st=new WeakMap,ss=new WeakMap,sn={},sa=0,si=function(e){return e&&(e.host||si(e.parentNode))},sr=function(e,t,s,n){var a=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var s=si(e);return s&&t.contains(s)?s:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});sn[s]||(sn[s]=new WeakMap);var i=sn[s],r=[],l=new Set,o=new Set(a),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};a.forEach(c);var d=function(e){!e||o.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))d(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,o=(st.get(e)||0)+1,c=(i.get(e)||0)+1;st.set(e,o),i.set(e,c),r.push(e),1===o&&a&&ss.set(e,!0),1===c&&e.setAttribute(s,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),l.clear(),sa++,function(){r.forEach(function(e){var t=st.get(e)-1,a=i.get(e)-1;st.set(e,t),i.set(e,a),t||(ss.has(e)||e.removeAttribute(n),ss.delete(e)),a||e.removeAttribute(s)}),--sa||(st=new WeakMap,st=new WeakMap,ss=new WeakMap,sn={})}},sl=function(e,t,s){void 0===s&&(s="data-aria-hidden");var n,a=Array.from(Array.isArray(e)?e:[e]),i=t||(n=e,"undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return i?(a.push.apply(a,Array.from(i.querySelectorAll("[aria-live], script"))),sr(a,i,s,"aria-hidden")):function(){return null}},so=function(){return(so=Object.assign||function(e){for(var t,s=1,n=arguments.length;s<n;s++)for(var a in t=arguments[s])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function sc(e,t){var s={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(s[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(s[n[a]]=e[n[a]]);return s}Object.create,Object.create;var sd=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),su="width-before-scroll-bar";function sm(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var sh="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,sp=new WeakMap;function sx(e){return e}var sf=function(e){void 0===e&&(e={});var t,s,n,a=(void 0===t&&(t=sx),s=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:null},useMedium:function(e){var a=t(e,n);return s.push(a),function(){s=s.filter(function(e){return e!==a})}},assignSyncMedium:function(e){for(n=!0;s.length;){var t=s;s=[],t.forEach(e)}s={push:function(t){return e(t)},filter:function(){return s}}},assignMedium:function(e){n=!0;var t=[];if(s.length){var a=s;s=[],a.forEach(e),t=s}var i=function(){var s=t;t=[],s.forEach(e)},r=function(){return Promise.resolve().then(i)};r(),s={push:function(e){t.push(e),r()},filter:function(e){return t=t.filter(e),s}}}});return a.options=so({async:!0,ssr:!1},e),a}(),sg=function(){},sv=l.forwardRef(function(e,t){var s,n,a,i,r=l.useRef(null),o=l.useState({onScrollCapture:sg,onWheelCapture:sg,onTouchMoveCapture:sg}),c=o[0],d=o[1],u=e.forwardProps,m=e.children,h=e.className,p=e.removeScrollBar,x=e.enabled,f=e.shards,g=e.sideCar,v=e.noRelative,y=e.noIsolation,j=e.inert,b=e.allowPinchZoom,N=e.as,w=e.gapMode,C=sc(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(s=[r,t],n=function(e){return s.forEach(function(t){return sm(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,i=a.facade,sh(function(){var e=sp.get(i);if(e){var t=new Set(e),n=new Set(s),a=i.current;t.forEach(function(e){n.has(e)||sm(e,null)}),n.forEach(function(e){t.has(e)||sm(e,a)})}sp.set(i,s)},[s]),i),k=so(so({},C),c);return l.createElement(l.Fragment,null,x&&l.createElement(g,{sideCar:sf,removeScrollBar:p,shards:f,noRelative:v,noIsolation:y,inert:j,setCallbacks:d,allowPinchZoom:!!b,lockRef:r,gapMode:w}),u?l.cloneElement(l.Children.only(m),so(so({},k),{ref:S})):l.createElement(void 0===N?"div":N,so({},k,{className:h,ref:S}),m))});sv.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},sv.classNames={fullWidth:su,zeroRight:sd};var sy=function(e){var t=e.sideCar,s=sc(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,so({},s))};sy.isSideCarExport=!0;var sj=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||s.nc;return t&&e.setAttribute("nonce",t),e}())){var a,r;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),r=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(r)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},sb=function(){var e=sj();return function(t,s){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&s])}},sN=function(){var e=sb();return function(t){return e(t.styles,t.dynamic),null}},sw={left:0,top:0,right:0,gap:0},sC=function(e){return parseInt(e||"",10)||0},sS=function(e){var t=window.getComputedStyle(document.body),s=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[sC(s),sC(n),sC(a)]},sk=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return sw;var t=sS(e),s=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-s+t[2]-t[0])}},sE=sN(),sP="data-scroll-locked",sA=function(e,t,s,n){var a=e.left,i=e.top,r=e.right,l=e.gap;return void 0===s&&(s="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(sP,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===s&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(r,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===s&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(sd," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(su," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(sd," .").concat(sd," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(su," .").concat(su," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(sP,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},sR=function(){var e=parseInt(document.body.getAttribute(sP)||"0",10);return isFinite(e)?e:0},sD=function(){l.useEffect(function(){return document.body.setAttribute(sP,(sR()+1).toString()),function(){var e=sR()-1;e<=0?document.body.removeAttribute(sP):document.body.setAttribute(sP,e.toString())}},[])},sM=function(e){var t=e.noRelative,s=e.noImportant,n=e.gapMode,a=void 0===n?"margin":n;sD();var i=l.useMemo(function(){return sk(a)},[a]);return l.createElement(sE,{styles:sA(i,!t,a,s?"":"!important")})},sT=!1;if("undefined"!=typeof window)try{var sL=Object.defineProperty({},"passive",{get:function(){return sT=!0,!0}});window.addEventListener("test",sL,sL),window.removeEventListener("test",sL,sL)}catch(e){sT=!1}var sO=!!sT&&{passive:!1},sI=function(e,t){if(!(e instanceof Element))return!1;var s=window.getComputedStyle(e);return"hidden"!==s[t]&&!(s.overflowY===s.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===s[t])},sF=function(e,t){var s=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),sH(e,n)){var a=sV(e,n);if(a[1]>a[2])return!0}n=n.parentNode}while(n&&n!==s.body);return!1},sH=function(e,t){return"v"===e?sI(t,"overflowY"):sI(t,"overflowX")},sV=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},sB=function(e,t,s,n,a){var i,r=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=r*n,o=s.target,c=t.contains(o),d=!1,u=l>0,m=0,h=0;do{if(!o)break;var p=sV(e,o),x=p[0],f=p[1]-p[2]-r*x;(x||f)&&sH(e,o)&&(m+=f,h+=x);var g=o.parentNode;o=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&o!==document.body||c&&(t.contains(o)||t===o));return u&&(a&&1>Math.abs(m)||!a&&l>m)?d=!0:!u&&(a&&1>Math.abs(h)||!a&&-l>h)&&(d=!0),d},sZ=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},sW=function(e){return[e.deltaX,e.deltaY]},sz=function(e){return e&&"current"in e?e.current:e},sU=0,sY=[];let sq=(n=function(e){var t=l.useRef([]),s=l.useRef([0,0]),n=l.useRef(),a=l.useState(sU++)[0],i=l.useState(sN)[0],r=l.useRef(e);l.useEffect(function(){r.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,s){if(s||2==arguments.length)for(var n,a=0,i=t.length;a<i;a++)!n&&a in t||(n||(n=Array.prototype.slice.call(t,0,a)),n[a]=t[a]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(sz),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var o=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!r.current.allowPinchZoom;var a,i=sZ(e),l=s.current,o="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],d=e.target,u=Math.abs(o)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var m=sF(u,d);if(!m)return!0;if(m?a=u:(a="v"===u?"h":"v",m=sF(u,d)),!m)return!1;if(!n.current&&"changedTouches"in e&&(o||c)&&(n.current=a),!a)return!0;var h=n.current||a;return sB(h,t,e,"h"===h?o:c,!0)},[]),c=l.useCallback(function(e){if(sY.length&&sY[sY.length-1]===i){var s="deltaY"in e?sW(e):sZ(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===s[0]&&n[1]===s[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var a=(r.current.shards||[]).map(sz).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?o(e,a[0]):!r.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=l.useCallback(function(e,s,n,a){var i={name:e,delta:s,target:n,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),u=l.useCallback(function(e){s.current=sZ(e),n.current=void 0},[]),m=l.useCallback(function(t){d(t.type,sW(t),t.target,o(t,e.lockRef.current))},[]),h=l.useCallback(function(t){d(t.type,sZ(t),t.target,o(t,e.lockRef.current))},[]);l.useEffect(function(){return sY.push(i),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:h}),document.addEventListener("wheel",c,sO),document.addEventListener("touchmove",c,sO),document.addEventListener("touchstart",u,sO),function(){sY=sY.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,sO),document.removeEventListener("touchmove",c,sO),document.removeEventListener("touchstart",u,sO)}},[]);var p=e.removeScrollBar,x=e.inert;return l.createElement(l.Fragment,null,x?l.createElement(i,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,p?l.createElement(sM,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},sf.useMedium(n),sy);var s_=l.forwardRef(function(e,t){return l.createElement(sv,so({},e,{ref:t,sideCar:sq}))});s_.classNames=sv.classNames;var sG=[" ","Enter","ArrowUp","ArrowDown"],s$=[" ","Enter"],sK="Select",[sX,sJ,sQ]=(0,K.B)(sK),[s0,s1]=(0,u.b)(sK,[sQ,tU]),s2=tU(),[s4,s5]=s0(sK),[s3,s6]=s0(sK),s8=e=>{let{__scopeSelect:t,children:s,open:n,defaultOpen:a,onOpenChange:i,value:o,defaultValue:c,onValueChange:d,dir:u,name:m,autoComplete:h,disabled:p,required:x,form:f}=e,g=s2(t),[v,y]=l.useState(null),[j,b]=l.useState(null),[N,w]=l.useState(!1),C=(0,J.gm)(u),[S,k]=(0,t7.T)({prop:n,defaultProp:a??!1,onChange:i,caller:sK}),[E,P]=(0,t7.T)({prop:o,defaultProp:c,onChange:d,caller:sK}),A=l.useRef(null),R=!v||f||!!v.closest("form"),[D,M]=l.useState(new Set),T=Array.from(D).map(e=>e.props.value).join(";");return(0,r.jsx)(t_,{...g,children:(0,r.jsxs)(s4,{required:x,scope:t,trigger:v,onTriggerChange:y,valueNode:j,onValueNodeChange:b,valueNodeHasChildren:N,onValueNodeHasChildrenChange:w,contentId:(0,ef.M)(),value:E,onValueChange:P,open:S,onOpenChange:k,dir:C,triggerPointerDownPosRef:A,disabled:p,children:[(0,r.jsx)(sX.Provider,{scope:t,children:(0,r.jsx)(s3,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{M(t=>{let s=new Set(t);return s.delete(e),s})},[]),children:s})}),R?(0,r.jsxs)(nI,{"aria-hidden":!0,required:x,tabIndex:-1,name:m,autoComplete:h,value:E,onChange:e=>P(e.target.value),disabled:p,form:f,children:[void 0===E?(0,r.jsx)("option",{value:""}):null,Array.from(D)]},T):null]})})};s8.displayName=sK;var s7="SelectTrigger",s9=l.forwardRef((e,t)=>{let{__scopeSelect:s,disabled:n=!1,...a}=e,i=s2(s),o=s5(s7,s),c=o.disabled||n,d=(0,X.e)(t,o.onTriggerChange),u=sJ(s),h=l.useRef("touch"),[p,x,f]=nH(e=>{let t=u().filter(e=>!e.disabled),s=t.find(e=>e.value===o.value),n=nV(t,e,s);void 0!==n&&o.onValueChange(n.value)}),g=e=>{c||(o.onOpenChange(!0),f()),e&&(o.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,r.jsx)(t$,{asChild:!0,...i,children:(0,r.jsx)(m.WV.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":nF(o.value)?"":void 0,...a,ref:d,onClick:(0,$.M)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&g(e)}),onPointerDown:(0,$.M)(a.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,$.M)(a.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||x(e.key),(!t||" "!==e.key)&&sG.includes(e.key)&&(g(),e.preventDefault())})})})});s9.displayName=s7;var ne="SelectValue",nt=l.forwardRef((e,t)=>{let{__scopeSelect:s,className:n,style:a,children:i,placeholder:l="",...o}=e,c=s5(ne,s),{onValueNodeHasChildrenChange:d}=c,u=void 0!==i,h=(0,X.e)(t,c.onValueNodeChange);return(0,tB.b)(()=>{d(u)},[d,u]),(0,r.jsx)(m.WV.span,{...o,ref:h,style:{pointerEvents:"none"},children:nF(c.value)?(0,r.jsx)(r.Fragment,{children:l}):i})});nt.displayName=ne;var ns=l.forwardRef((e,t)=>{let{__scopeSelect:s,children:n,...a}=e;return(0,r.jsx)(m.WV.span,{"aria-hidden":!0,...a,ref:t,children:n||"▼"})});ns.displayName="SelectIcon";var nn=e=>(0,r.jsx)(t6,{asChild:!0,...e});nn.displayName="SelectPortal";var na="SelectContent",ni=l.forwardRef((e,t)=>{let s=s5(na,e.__scopeSelect),[n,a]=l.useState();return((0,tB.b)(()=>{a(new DocumentFragment)},[]),s.open)?(0,r.jsx)(nc,{...e,ref:t}):n?_.createPortal((0,r.jsx)(nr,{scope:e.__scopeSelect,children:(0,r.jsx)(sX.Slot,{scope:e.__scopeSelect,children:(0,r.jsx)("div",{children:e.children})})}),n):null});ni.displayName=na;var[nr,nl]=s0(na),no=(0,t8.Z8)("SelectContent.RemoveScroll"),nc=l.forwardRef((e,t)=>{let{__scopeSelect:s,position:n="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:i,onPointerDownOutside:o,side:c,sideOffset:d,align:u,alignOffset:m,arrowPadding:h,collisionBoundary:p,collisionPadding:x,sticky:f,hideWhenDetached:g,avoidCollisions:v,...y}=e,j=s5(na,s),[b,N]=l.useState(null),[w,C]=l.useState(null),S=(0,X.e)(t,e=>N(e)),[k,E]=l.useState(null),[P,A]=l.useState(null),R=sJ(s),[D,M]=l.useState(!1),T=l.useRef(!1);l.useEffect(()=>{if(b)return sl(b)},[b]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??er()),document.body.insertAdjacentElement("beforeend",e[1]??er()),ei++,()=>{1===ei&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),ei--}},[]);let L=l.useCallback(e=>{let[t,...s]=R().map(e=>e.ref.current),[n]=s.slice(-1),a=document.activeElement;for(let s of e)if(s===a||(s?.scrollIntoView({block:"nearest"}),s===t&&w&&(w.scrollTop=0),s===n&&w&&(w.scrollTop=w.scrollHeight),s?.focus(),document.activeElement!==a))return},[R,w]),O=l.useCallback(()=>L([k,b]),[L,k,b]);l.useEffect(()=>{D&&O()},[D,O]);let{onOpenChange:I,triggerPointerDownPosRef:F}=j;l.useEffect(()=>{if(b){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(F.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(F.current?.y??0))}},s=s=>{e.x<=10&&e.y<=10?s.preventDefault():b.contains(s.target)||I(!1),document.removeEventListener("pointermove",t),F.current=null};return null!==F.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",s,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",s,{capture:!0})}}},[b,I,F]),l.useEffect(()=>{let e=()=>I(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[I]);let[H,V]=nH(e=>{let t=R().filter(e=>!e.disabled),s=t.find(e=>e.ref.current===document.activeElement),n=nV(t,e,s);n&&setTimeout(()=>n.ref.current.focus())}),B=l.useCallback((e,t,s)=>{let n=!T.current&&!s;(void 0!==j.value&&j.value===t||n)&&(E(e),n&&(T.current=!0))},[j.value]),Z=l.useCallback(()=>b?.focus(),[b]),W=l.useCallback((e,t,s)=>{let n=!T.current&&!s;(void 0!==j.value&&j.value===t||n)&&A(e)},[j.value]),z="popper"===n?nu:nd,U=z===nu?{side:c,sideOffset:d,align:u,alignOffset:m,arrowPadding:h,collisionBoundary:p,collisionPadding:x,sticky:f,hideWhenDetached:g,avoidCollisions:v}:{};return(0,r.jsx)(nr,{scope:s,content:b,viewport:w,onViewportChange:C,itemRefCallback:B,selectedItem:k,onItemLeave:Z,itemTextRefCallback:W,focusSelectedItem:O,selectedItemText:P,position:n,isPositioned:D,searchRef:H,children:(0,r.jsx)(s_,{as:no,allowPinchZoom:!0,children:(0,r.jsx)(ed,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,$.M)(a,e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,r.jsx)(es,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,r.jsx)(z,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...y,...U,onPlaced:()=>M(!0),ref:S,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:(0,$.M)(y.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=R().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let s=e.target,n=t.indexOf(s);t=t.slice(n+1)}setTimeout(()=>L(t)),e.preventDefault()}})})})})})})});nc.displayName="SelectContentImpl";var nd=l.forwardRef((e,t)=>{let{__scopeSelect:s,onPlaced:n,...a}=e,i=s5(na,s),o=nl(na,s),[c,d]=l.useState(null),[u,h]=l.useState(null),p=(0,X.e)(t,e=>h(e)),x=sJ(s),f=l.useRef(!1),g=l.useRef(!0),{viewport:v,selectedItem:y,selectedItemText:j,focusSelectedItem:b}=o,N=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&u&&v&&y&&j){let e=i.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),s=i.valueNode.getBoundingClientRect(),a=j.getBoundingClientRect();if("rtl"!==i.dir){let n=a.left-t.left,i=s.left-n,r=e.left-i,l=e.width+r,o=Math.max(l,t.width),d=G(i,[10,Math.max(10,window.innerWidth-10-o)]);c.style.minWidth=l+"px",c.style.left=d+"px"}else{let n=t.right-a.right,i=window.innerWidth-s.right-n,r=window.innerWidth-e.right-i,l=e.width+r,o=Math.max(l,t.width),d=G(i,[10,Math.max(10,window.innerWidth-10-o)]);c.style.minWidth=l+"px",c.style.right=d+"px"}let r=x(),l=window.innerHeight-20,o=v.scrollHeight,d=window.getComputedStyle(u),m=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),p=parseInt(d.borderBottomWidth,10),g=m+h+o+parseInt(d.paddingBottom,10)+p,b=Math.min(5*y.offsetHeight,g),N=window.getComputedStyle(v),w=parseInt(N.paddingTop,10),C=parseInt(N.paddingBottom,10),S=e.top+e.height/2-10,k=y.offsetHeight/2,E=m+h+(y.offsetTop+k);if(E<=S){let e=r.length>0&&y===r[r.length-1].ref.current;c.style.bottom="0px";let t=u.clientHeight-v.offsetTop-v.offsetHeight;c.style.height=E+Math.max(l-S,k+(e?C:0)+t+p)+"px"}else{let e=r.length>0&&y===r[0].ref.current;c.style.top="0px";let t=Math.max(S,m+v.offsetTop+(e?w:0)+k);c.style.height=t+(g-E)+"px",v.scrollTop=E-S+v.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=l+"px",n?.(),requestAnimationFrame(()=>f.current=!0)}},[x,i.trigger,i.valueNode,c,u,v,y,j,i.dir,n]);(0,tB.b)(()=>N(),[N]);let[w,C]=l.useState();(0,tB.b)(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);let S=l.useCallback(e=>{e&&!0===g.current&&(N(),b?.(),g.current=!1)},[N,b]);return(0,r.jsx)(nm,{scope:s,contentWrapper:c,shouldExpandOnScrollRef:f,onScrollButtonChange:S,children:(0,r.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:w},children:(0,r.jsx)(m.WV.div,{...a,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});nd.displayName="SelectItemAlignedPosition";var nu=l.forwardRef((e,t)=>{let{__scopeSelect:s,align:n="start",collisionPadding:a=10,...i}=e,l=s2(s);return(0,r.jsx)(tQ,{...l,...i,ref:t,align:n,collisionPadding:a,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nu.displayName="SelectPopperPosition";var[nm,nh]=s0(na,{}),np="SelectViewport",nx=l.forwardRef((e,t)=>{let{__scopeSelect:s,nonce:n,...a}=e,i=nl(np,s),o=nh(np,s),c=(0,X.e)(t,i.onViewportChange),d=l.useRef(0);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,r.jsx)(sX.Slot,{scope:s,children:(0,r.jsx)(m.WV.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,$.M)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:s,shouldExpandOnScrollRef:n}=o;if(n?.current&&s){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,a=Math.max(parseFloat(s.style.minHeight),parseFloat(s.style.height));if(a<n){let i=a+e,r=Math.min(n,i),l=i-r;s.style.height=r+"px","0px"===s.style.bottom&&(t.scrollTop=l>0?l:0,s.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});nx.displayName=np;var nf="SelectGroup",[ng,nv]=s0(nf);l.forwardRef((e,t)=>{let{__scopeSelect:s,...n}=e,a=(0,ef.M)();return(0,r.jsx)(ng,{scope:s,id:a,children:(0,r.jsx)(m.WV.div,{role:"group","aria-labelledby":a,...n,ref:t})})}).displayName=nf;var ny="SelectLabel",nj=l.forwardRef((e,t)=>{let{__scopeSelect:s,...n}=e,a=nv(ny,s);return(0,r.jsx)(m.WV.div,{id:a.id,...n,ref:t})});nj.displayName=ny;var nb="SelectItem",[nN,nw]=s0(nb),nC=l.forwardRef((e,t)=>{let{__scopeSelect:s,value:n,disabled:a=!1,textValue:i,...o}=e,c=s5(nb,s),d=nl(nb,s),u=c.value===n,[h,p]=l.useState(i??""),[x,f]=l.useState(!1),g=(0,X.e)(t,e=>d.itemRefCallback?.(e,n,a)),v=(0,ef.M)(),y=l.useRef("touch"),j=()=>{a||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,r.jsx)(nN,{scope:s,value:n,disabled:a,textId:v,isSelected:u,onItemTextChange:l.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,r.jsx)(sX.ItemSlot,{scope:s,value:n,disabled:a,textValue:h,children:(0,r.jsx)(m.WV.div,{role:"option","aria-labelledby":v,"data-highlighted":x?"":void 0,"aria-selected":u&&x,"data-state":u?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...o,ref:g,onFocus:(0,$.M)(o.onFocus,()=>f(!0)),onBlur:(0,$.M)(o.onBlur,()=>f(!1)),onClick:(0,$.M)(o.onClick,()=>{"mouse"!==y.current&&j()}),onPointerUp:(0,$.M)(o.onPointerUp,()=>{"mouse"===y.current&&j()}),onPointerDown:(0,$.M)(o.onPointerDown,e=>{y.current=e.pointerType}),onPointerMove:(0,$.M)(o.onPointerMove,e=>{y.current=e.pointerType,a?d.onItemLeave?.():"mouse"===y.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,$.M)(o.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,$.M)(o.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(s$.includes(e.key)&&j()," "===e.key&&e.preventDefault())})})})})});nC.displayName=nb;var nS="SelectItemText",nk=l.forwardRef((e,t)=>{let{__scopeSelect:s,className:n,style:a,...i}=e,o=s5(nS,s),c=nl(nS,s),d=nw(nS,s),u=s6(nS,s),[h,p]=l.useState(null),x=(0,X.e)(t,e=>p(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),f=h?.textContent,g=l.useMemo(()=>(0,r.jsx)("option",{value:d.value,disabled:d.disabled,children:f},d.value),[d.disabled,d.value,f]),{onNativeOptionAdd:v,onNativeOptionRemove:y}=u;return(0,tB.b)(()=>(v(g),()=>y(g)),[v,y,g]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.WV.span,{id:d.textId,...i,ref:x}),d.isSelected&&o.valueNode&&!o.valueNodeHasChildren?_.createPortal(i.children,o.valueNode):null]})});nk.displayName=nS;var nE="SelectItemIndicator",nP=l.forwardRef((e,t)=>{let{__scopeSelect:s,...n}=e;return nw(nE,s).isSelected?(0,r.jsx)(m.WV.span,{"aria-hidden":!0,...n,ref:t}):null});nP.displayName=nE;var nA="SelectScrollUpButton",nR=l.forwardRef((e,t)=>{let s=nl(nA,e.__scopeSelect),n=nh(nA,e.__scopeSelect),[a,i]=l.useState(!1),o=(0,X.e)(t,n.onScrollButtonChange);return(0,tB.b)(()=>{if(s.viewport&&s.isPositioned){let e=function(){i(t.scrollTop>0)},t=s.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[s.viewport,s.isPositioned]),a?(0,r.jsx)(nT,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=s;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nR.displayName=nA;var nD="SelectScrollDownButton",nM=l.forwardRef((e,t)=>{let s=nl(nD,e.__scopeSelect),n=nh(nD,e.__scopeSelect),[a,i]=l.useState(!1),o=(0,X.e)(t,n.onScrollButtonChange);return(0,tB.b)(()=>{if(s.viewport&&s.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=s.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[s.viewport,s.isPositioned]),a?(0,r.jsx)(nT,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=s;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nM.displayName=nD;var nT=l.forwardRef((e,t)=>{let{__scopeSelect:s,onAutoScroll:n,...a}=e,i=nl("SelectScrollButton",s),o=l.useRef(null),c=sJ(s),d=l.useCallback(()=>{null!==o.current&&(window.clearInterval(o.current),o.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),(0,tB.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,r.jsx)(m.WV.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,$.M)(a.onPointerDown,()=>{null===o.current&&(o.current=window.setInterval(n,50))}),onPointerMove:(0,$.M)(a.onPointerMove,()=>{i.onItemLeave?.(),null===o.current&&(o.current=window.setInterval(n,50))}),onPointerLeave:(0,$.M)(a.onPointerLeave,()=>{d()})})}),nL=l.forwardRef((e,t)=>{let{__scopeSelect:s,...n}=e;return(0,r.jsx)(m.WV.div,{"aria-hidden":!0,...n,ref:t})});nL.displayName="SelectSeparator";var nO="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:s,...n}=e,a=s2(s),i=s5(nO,s),l=nl(nO,s);return i.open&&"popper"===l.position?(0,r.jsx)(t2,{...a,...n,ref:t}):null}).displayName=nO;var nI=l.forwardRef(({__scopeSelect:e,value:t,...s},n)=>{let a=l.useRef(null),i=(0,X.e)(n,a),o=t9(t);return l.useEffect(()=>{let e=a.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(o!==t&&s){let n=new Event("change",{bubbles:!0});s.call(e,t),e.dispatchEvent(n)}},[o,t]),(0,r.jsx)(m.WV.select,{...s,style:{...se,...s.style},ref:i,defaultValue:t})});function nF(e){return""===e||void 0===e}function nH(e){let t=(0,Q.W)(e),s=l.useRef(""),n=l.useRef(0),a=l.useCallback(e=>{let a=s.current+e;t(a),function e(t){s.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(a)},[t]),i=l.useCallback(()=>{s.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[s,a,i]}function nV(e,t,s){var n;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(n=Math.max(s?e.indexOf(s):-1,0),e.map((t,s)=>e[(n+s)%e.length]));1===a.length&&(i=i.filter(e=>e!==s));let r=i.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return r!==s?r:void 0}nI.displayName="SelectBubbleInput";let nB=(0,F.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),nZ=(0,F.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),nW=(0,F.Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),nz=l.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsxs)(s9,{ref:n,className:(0,k.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,r.jsx(ns,{asChild:!0,children:r.jsx(nB,{className:"h-4 w-4 opacity-50"})})]}));nz.displayName=s9.displayName;let nU=l.forwardRef(({className:e,...t},s)=>r.jsx(nR,{ref:s,className:(0,k.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(nZ,{className:"h-4 w-4"})}));nU.displayName=nR.displayName;let nY=l.forwardRef(({className:e,...t},s)=>r.jsx(nM,{ref:s,className:(0,k.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(nB,{className:"h-4 w-4"})}));nY.displayName=nM.displayName;let nq=l.forwardRef(({className:e,children:t,position:s="popper",...n},a)=>r.jsx(nn,{children:(0,r.jsxs)(ni,{ref:a,className:(0,k.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[r.jsx(nU,{}),r.jsx(nx,{className:(0,k.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),r.jsx(nY,{})]})}));nq.displayName=ni.displayName,l.forwardRef(({className:e,...t},s)=>r.jsx(nj,{ref:s,className:(0,k.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=nj.displayName;let n_=l.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsxs)(nC,{ref:n,className:(0,k.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(nP,{children:r.jsx(nW,{className:"h-4 w-4"})})}),r.jsx(nk,{children:t})]}));n_.displayName=nC.displayName,l.forwardRef(({className:e,...t},s)=>r.jsx(nL,{ref:s,className:(0,k.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=nL.displayName;let nG=["Less than high school","High school diploma/GED","Some college","Associate degree","Bachelor's degree","Master's degree","Doctoral degree","Professional degree","Other"],n$=["Student","Unemployed","Retired","Disabled","Homemaker","Healthcare worker","Teacher/Educator","Business/Finance","Technology","Service industry","Manufacturing","Government","Non-profit","Self-employed","Other"],nK=["Lives alone","Lives with spouse/partner","Lives with family","Lives with roommates","Group home","Assisted living","Nursing home","Homeless","Other"],nX=["Single","Married","Divorced","Separated","Widowed","Domestic partnership","Other"],nJ=["Male","Female","Non-binary","Transgender male","Transgender female","Other","Prefer not to say"],nQ=["Hispanic or Latino","Not Hispanic or Latino","Prefer not to say"],n0=["American Indian or Alaska Native","Asian","Black or African American","Native Hawaiian or Other Pacific Islander","White","Other","Prefer not to say"],n1=["Private insurance","Medicare","Medicaid","Military/VA","Self-pay","Other","Uninsured"],n2=["Full-time","Part-time","Unemployed","Student","Retired","Disabled","Other"],n4=[{value:"mild",label:"Mild"},{value:"moderate",label:"Moderate"},{value:"severe",label:"Severe"}],n5=["Less than 1 week","1-2 weeks","2-4 weeks","1-3 months","3-6 months","6-12 months","1-2 years","More than 2 years"],n3=["Daily","Several times a week","Weekly","Several times a month","Monthly","Rarely"],n6=[{value:"low",label:"Low"},{value:"moderate",label:"Moderate"},{value:"high",label:"High"}],n8=["Antidepressants","Antipsychotics","Anxiolytics","Mood Stabilizers","Stimulants","Anticonvulsants","Antihypertensives","Diabetes Medications","Other"],n7={Antidepressants:["Sertraline (Zoloft)","Fluoxetine (Prozac)","Escitalopram (Lexapro)","Paroxetine (Paxil)","Citalopram (Celexa)","Venlafaxine (Effexor)","Duloxetine (Cymbalta)","Bupropion (Wellbutrin)","Mirtazapine (Remeron)","Trazodone","Amitriptyline","Nortriptyline"],Antipsychotics:["Risperidone (Risperdal)","Olanzapine (Zyprexa)","Quetiapine (Seroquel)","Aripiprazole (Abilify)","Haloperidol (Haldol)","Clozapine (Clozaril)","Ziprasidone (Geodon)","Paliperidone (Invega)","Asenapine (Saphris)","Lurasidone (Latuda)"],Anxiolytics:["Lorazepam (Ativan)","Alprazolam (Xanax)","Clonazepam (Klonopin)","Diazepam (Valium)","Buspirone (Buspar)","Hydroxyzine (Vistaril)","Propranolol"],"Mood Stabilizers":["Lithium","Valproic Acid (Depakote)","Lamotrigine (Lamictal)","Carbamazepine (Tegretol)","Oxcarbazepine (Trileptal)","Topiramate (Topamax)"],Stimulants:["Methylphenidate (Ritalin)","Amphetamine (Adderall)","Lisdexamfetamine (Vyvanse)","Atomoxetine (Strattera)","Modafinil (Provigil)","Armodafinil (Nuvigil)"],Anticonvulsants:["Phenytoin (Dilantin)","Levetiracetam (Keppra)","Gabapentin (Neurontin)","Pregabalin (Lyrica)","Clonazepam (Klonopin)"],Antihypertensives:["Lisinopril","Amlodipine","Metoprolol","Losartan","Hydrochlorothiazide","Atenolol"],"Diabetes Medications":["Metformin","Insulin","Glipizide","Glyburide","Pioglitazone","Sitagliptin"],Other:["Custom medication (specify in notes)"]},n9=l.forwardRef(({className:e,...t},s)=>r.jsx("textarea",{className:(0,k.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n9.displayName="Textarea";var ae=s(9815),at="Checkbox",[as,an]=(0,u.b)(at),[aa,ai]=as(at);function ar(e){let{__scopeCheckbox:t,checked:s,children:n,defaultChecked:a,disabled:i,form:o,name:c,onCheckedChange:d,required:u,value:m="on",internal_do_not_use_render:h}=e,[p,x]=(0,t7.T)({prop:s,defaultProp:a??!1,onChange:d,caller:at}),[f,g]=l.useState(null),[v,y]=l.useState(null),j=l.useRef(!1),b=!f||!!o||!!f.closest("form"),N={checked:p,disabled:i,setChecked:x,control:f,setControl:g,name:c,form:o,value:m,hasConsumerStoppedPropagationRef:j,required:u,defaultChecked:!ap(a)&&a,isFormControl:b,bubbleInput:v,setBubbleInput:y};return(0,r.jsx)(aa,{scope:t,...N,children:"function"==typeof h?h(N):n})}var al="CheckboxTrigger",ao=l.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:s,...n},a)=>{let{control:i,value:o,disabled:c,checked:d,required:u,setControl:h,setChecked:p,hasConsumerStoppedPropagationRef:x,isFormControl:f,bubbleInput:g}=ai(al,e),v=(0,X.e)(a,h),y=l.useRef(d);return l.useEffect(()=>{let e=i?.form;if(e){let t=()=>p(y.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,p]),(0,r.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":ap(d)?"mixed":d,"aria-required":u,"data-state":ax(d),"data-disabled":c?"":void 0,disabled:c,value:o,...n,ref:v,onKeyDown:(0,$.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,$.M)(s,e=>{p(e=>!!ap(e)||!e),g&&f&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});ao.displayName=al;var ac=l.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:n,checked:a,defaultChecked:i,required:l,disabled:o,value:c,onCheckedChange:d,form:u,...m}=e;return(0,r.jsx)(ar,{__scopeCheckbox:s,checked:a,defaultChecked:i,disabled:o,required:l,onCheckedChange:d,name:n,form:u,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(ao,{...m,ref:t,__scopeCheckbox:s}),e&&(0,r.jsx)(ah,{__scopeCheckbox:s})]})})});ac.displayName=at;var ad="CheckboxIndicator",au=l.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:n,...a}=e,i=ai(ad,s);return(0,r.jsx)(ae.z,{present:n||ap(i.checked)||!0===i.checked,children:(0,r.jsx)(m.WV.span,{"data-state":ax(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});au.displayName=ad;var am="CheckboxBubbleInput",ah=l.forwardRef(({__scopeCheckbox:e,...t},s)=>{let{control:n,hasConsumerStoppedPropagationRef:a,checked:i,defaultChecked:o,required:c,disabled:d,name:u,value:h,form:p,bubbleInput:x,setBubbleInput:f}=ai(am,e),g=(0,X.e)(s,f),v=t9(i),y=tZ(n);l.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(v!==i&&e){let s=new Event("click",{bubbles:t});x.indeterminate=ap(i),e.call(x,!ap(i)&&i),x.dispatchEvent(s)}},[x,v,i,a]);let j=l.useRef(!ap(i)&&i);return(0,r.jsx)(m.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??j.current,required:c,disabled:d,name:u,value:h,form:p,...t,tabIndex:-1,ref:g,style:{...t.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function ap(e){return"indeterminate"===e}function ax(e){return ap(e)?"indeterminate":e?"checked":"unchecked"}ah.displayName=am;let af=l.forwardRef(({className:e,...t},s)=>r.jsx(ac,{ref:s,className:(0,k.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:r.jsx(au,{className:(0,k.cn)("flex items-center justify-center text-current"),children:r.jsx(nW,{className:"h-4 w-4"})})}));af.displayName=ac.displayName;let ag={Mood:["Depressed mood","Anhedonia (loss of interest)","Irritability","Mood swings","Hopelessness","Worthlessness","Guilt","Appetite changes","Fatigue","Pain complaints","Elevated mood (mania/hypomania)","Grandiosity","Decreased need for sleep","Increased goal-directed activity","Distractibility","Impulsivity","Social withdrawal"],Anxiety:["Anxiety","Panic attacks","Excessive worry","Restlessness","Hypervigilance","Avoidance"],Psychotic:["Hallucinations","Delusions","Paranoia","Ideas of reference","Disorganized speech","Isolation"],Cognitive:["Concentration difficulties","Memory problems","Disorientation","Slowed thinking","Racing thoughts"],Sleep:["Sleep disturbances","Insomnia","Hypersomnia","Nightmares"],Substance:["Substance use","Cravings","Withdrawal symptoms"],ObsessiveCompulsive:["Obsessive thoughts","Compulsive behaviors","Checking behaviors","Cleaning rituals"],Risk:["Suicidal thoughts","Self-harm behaviors","Aggression/violence"]};var av=s(5594),ay="Radio",[aj,ab]=(0,u.b)(ay),[aN,aw]=aj(ay),aC=l.forwardRef((e,t)=>{let{__scopeRadio:s,name:n,checked:a=!1,required:i,disabled:o,value:c="on",onCheck:d,form:u,...h}=e,[p,x]=l.useState(null),f=(0,X.e)(t,e=>x(e)),g=l.useRef(!1),v=!p||u||!!p.closest("form");return(0,r.jsxs)(aN,{scope:s,checked:a,disabled:o,children:[(0,r.jsx)(m.WV.button,{type:"button",role:"radio","aria-checked":a,"data-state":aP(a),"data-disabled":o?"":void 0,disabled:o,value:c,...h,ref:f,onClick:(0,$.M)(e.onClick,e=>{a||d?.(),v&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),v&&(0,r.jsx)(aE,{control:p,bubbles:!g.current,name:n,value:c,checked:a,required:i,disabled:o,form:u,style:{transform:"translateX(-100%)"}})]})});aC.displayName=ay;var aS="RadioIndicator",ak=l.forwardRef((e,t)=>{let{__scopeRadio:s,forceMount:n,...a}=e,i=aw(aS,s);return(0,r.jsx)(ae.z,{present:n||i.checked,children:(0,r.jsx)(m.WV.span,{"data-state":aP(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t})})});ak.displayName=aS;var aE=l.forwardRef(({__scopeRadio:e,control:t,checked:s,bubbles:n=!0,...a},i)=>{let o=l.useRef(null),c=(0,X.e)(o,i),d=t9(s),u=tZ(t);return l.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==s&&t){let a=new Event("click",{bubbles:n});t.call(e,s),e.dispatchEvent(a)}},[d,s,n]),(0,r.jsx)(m.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:s,...a,tabIndex:-1,ref:c,style:{...a.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function aP(e){return e?"checked":"unchecked"}aE.displayName="RadioBubbleInput";var aA=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],aR="RadioGroup",[aD,aM]=(0,u.b)(aR,[av.Pc,ab]),aT=(0,av.Pc)(),aL=ab(),[aO,aI]=aD(aR),aF=l.forwardRef((e,t)=>{let{__scopeRadioGroup:s,name:n,defaultValue:a,value:i,required:l=!1,disabled:o=!1,orientation:c,dir:d,loop:u=!0,onValueChange:h,...p}=e,x=aT(s),f=(0,J.gm)(d),[g,v]=(0,t7.T)({prop:i,defaultProp:a??null,onChange:h,caller:aR});return(0,r.jsx)(aO,{scope:s,name:n,required:l,disabled:o,value:g,onValueChange:v,children:(0,r.jsx)(av.fC,{asChild:!0,...x,orientation:c,dir:f,loop:u,children:(0,r.jsx)(m.WV.div,{role:"radiogroup","aria-required":l,"aria-orientation":c,"data-disabled":o?"":void 0,dir:f,...p,ref:t})})})});aF.displayName=aR;var aH="RadioGroupItem",aV=l.forwardRef((e,t)=>{let{__scopeRadioGroup:s,disabled:n,...a}=e,i=aI(aH,s),o=i.disabled||n,c=aT(s),d=aL(s),u=l.useRef(null),m=(0,X.e)(t,u),h=i.value===a.value,p=l.useRef(!1);return l.useEffect(()=>{let e=e=>{aA.includes(e.key)&&(p.current=!0)},t=()=>p.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,r.jsx)(av.ck,{asChild:!0,...c,focusable:!o,active:h,children:(0,r.jsx)(aC,{disabled:o,required:i.required,checked:h,...d,...a,name:i.name,ref:m,onCheck:()=>i.onValueChange(a.value),onKeyDown:(0,$.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,$.M)(a.onFocus,()=>{p.current&&u.current?.click()})})})});aV.displayName=aH;var aB=l.forwardRef((e,t)=>{let{__scopeRadioGroup:s,...n}=e,a=aL(s);return(0,r.jsx)(ak,{...a,...n,ref:t})});aB.displayName="RadioGroupIndicator";let aZ=(0,F.Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),aW=l.forwardRef(({className:e,...t},s)=>r.jsx(aF,{className:(0,k.cn)("grid gap-2",e),...t,ref:s}));aW.displayName=aF.displayName;let az=l.forwardRef(({className:e,...t},s)=>r.jsx(aV,{ref:s,className:(0,k.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:r.jsx(aB,{className:"flex items-center justify-center",children:r.jsx(aZ,{className:"h-2.5 w-2.5 fill-current text-current"})})}));az.displayName=aV.displayName;let aU=(0,F.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var aY=s(8443),aq=s(3855);let a_=(0,F.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),aG={Alcohol:["Beer","Wine","Spirits/Liquor","Mixed Drinks","Other Alcohol"],Cannabis:["Marijuana/THC","Hashish","CBD Products","Synthetic Cannabis (K2/Spice)","Other Cannabis"],Hallucinogens:["LSD","PCP","Psilocybin (Mushrooms)","MDMA/Ecstasy","Mescaline","DMT","Other Hallucinogens"],Inhalants:["Nitrous Oxide","Glue/Solvents","Gasoline","Paint Thinner","Aerosols","Other Inhalants"],Opioids:["Heroin","Fentanyl","Oxycodone","Hydrocodone","Morphine","Codeine","Methadone","Buprenorphine","Other Opioids"],"Sedatives/Hypnotics/Anxiolytics":["Benzodiazepines (Xanax, Valium, etc.)","Barbiturates","Sleep Medications (Ambien, etc.)","Other Sedatives"],Stimulants:["Cocaine","Crack Cocaine","Amphetamines","Methamphetamine","ADHD Medications (Adderall, etc.)","Other Stimulants"],Tobacco:["Cigarettes","Cigars","Pipe Tobacco","Chewing Tobacco","E-cigarettes/Vaping","Other Tobacco"],Caffeine:["Coffee","Tea","Energy Drinks","Caffeine Pills","Other Caffeine"],"Other/Unknown":["Prescription Drugs (Misused)","Over-the-Counter Drugs (Misused)","Unknown Substance","Other"]},a$=["Oral","Intravenous (IV)","Intranasal (Snorting)","Smoking/Inhalation","Sublingual","Transdermal","Intramuscular","Subcutaneous","Other"],aK=["Daily","Multiple times per day","Weekly","Multiple times per week","Monthly","Occasionally","Binges/Episodes","As needed","Other"];function aX({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||[]),[a,i]=(0,l.useState)(""),[o,d]=(0,l.useState)(""),u=e=>{n(t=>t.filter(t=>t.id!==e))},m=(e,t,s)=>{n(n=>n.map(n=>n.id===e?{...n,[t]:s}:n))},h=a&&aG[a]||[];return(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Enhanced Substance Use History"}),r.jsx(c.SZ,{children:"Detailed substance use assessment using DSM-5 categories for comprehensive evaluation"})]}),(0,r.jsxs)(c.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4 bg-slate-50",children:[r.jsx("h4",{className:"font-medium mb-3",children:"Add Substance Use History"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"DSM-5 Category"}),(0,r.jsxs)(s8,{value:a,onValueChange:i,children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select category"})}),r.jsx(nq,{children:Object.keys(aG).map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Specific Substance"}),(0,r.jsxs)(s8,{value:o,onValueChange:d,disabled:!a,children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select substance"})}),r.jsx(nq,{children:h.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),r.jsx("div",{className:"flex items-end",children:(0,r.jsxs)(P.z,{onClick:()=>{if(!a||!o)return;let e={id:Date.now().toString(),category:a,specificSubstance:o,duration:"",route:"",frequency:"",ageOfFirstUse:"",treatmentHistory:"",notes:""};n(t=>[...t,e]),i(""),d("")},disabled:!a||!o,className:"w-full",children:[r.jsx(aq.Z,{className:"h-4 w-4 mr-2"}),"Add Substance"]})})]})]}),s.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h4",{className:"font-medium",children:"Current Substance Use History"}),s.map(e=>(0,r.jsxs)(c.Zb,{className:"border-l-4 border-l-blue-500",children:[r.jsx(c.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(aY.C,{variant:"outline",children:e.category}),r.jsx("span",{className:"font-medium",children:e.specificSubstance})]}),r.jsx(P.z,{variant:"ghost",size:"sm",onClick:()=>u(e.id),className:"text-red-600 hover:text-red-800",children:r.jsx(a_,{className:"h-4 w-4"})})]})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Duration of Use"}),r.jsx(z.I,{value:e.duration,onChange:t=>m(e.id,"duration",t.target.value),placeholder:"e.g., 2 years, 6 months"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Route of Administration"}),(0,r.jsxs)(s8,{value:e.route,onValueChange:t=>m(e.id,"route",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select route"})}),r.jsx(nq,{children:a$.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Frequency/Pattern"}),(0,r.jsxs)(s8,{value:e.frequency,onValueChange:t=>m(e.id,"frequency",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select frequency"})}),r.jsx(nq,{children:aK.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Age of First Use"}),r.jsx(z.I,{value:e.ageOfFirstUse,onChange:t=>m(e.id,"ageOfFirstUse",t.target.value),placeholder:"e.g., 16",type:"number"})]}),(0,r.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[r.jsx(q,{children:"Treatment History"}),r.jsx(z.I,{value:e.treatmentHistory,onChange:t=>m(e.id,"treatmentHistory",t.target.value),placeholder:"Previous treatment attempts, rehab, etc."})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:e.notes,onChange:t=>m(e.id,"notes",t.target.value),placeholder:"Additional details about use patterns, triggers, consequences, etc.",rows:2})]})]})]},e.id))]}),0===s.length&&(0,r.jsxs)("div",{className:"text-center py-8 text-slate-500",children:[r.jsx("p",{children:"No substance use history recorded."}),r.jsx("p",{className:"text-sm",children:"Use the form above to add substance use information."})]})]})]})}let aJ=["Very Effective","Moderately Effective","Minimally Effective","Ineffective","Unknown"],aQ=["Ineffective","Side effects","Patient preference","Cost","Other"];function a0({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||[]),[a,i]=(0,l.useState)(""),[o,d]=(0,l.useState)(""),u=e=>{n(t=>t.filter(t=>t.id!==e))},m=(e,t,s)=>{n(n=>n.map(n=>n.id===e?{...n,[t]:s}:n))};return(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Medication History"}),r.jsx(c.SZ,{children:"Track psychiatric medications with effectiveness, side effects, and treatment outcomes"})]}),(0,r.jsxs)(c.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4 bg-slate-50",children:[r.jsx("h4",{className:"font-medium mb-3",children:"Add Medication"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Medication Category"}),(0,r.jsxs)(s8,{value:o,onValueChange:e=>{d(e),i("")},children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select category first"})}),r.jsx(nq,{children:n8.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Specific Medication"}),(0,r.jsxs)(s8,{value:a,onValueChange:i,disabled:!o,children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:o?"Select medication":"Select category first"})}),r.jsx(nq,{children:o&&n7[o]?.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),r.jsx("div",{className:"flex items-end",children:(0,r.jsxs)(P.z,{onClick:()=>{if(!a||!o)return;let e={id:Date.now().toString(),medicationName:a,category:o,dosage:"",startDate:"",endDate:"",effectiveness:"",sideEffects:"",discontinuationReason:"",discontinuationOther:"",notes:""};n(t=>[...t,e]),i(""),d("")},disabled:!a||!o,className:"w-full",children:[r.jsx(aq.Z,{className:"h-4 w-4 mr-2"}),"Add Medication"]})})]})]}),s.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h4",{className:"font-medium",children:"Medication History"}),s.map(e=>(0,r.jsxs)(c.Zb,{className:"border-l-4 border-l-green-500",children:[r.jsx(c.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(aY.C,{variant:"outline",children:e.category}),r.jsx("span",{className:"font-medium",children:e.medicationName}),e.dosage&&r.jsx(aY.C,{variant:"secondary",children:e.dosage})]}),r.jsx(P.z,{variant:"ghost",size:"sm",onClick:()=>u(e.id),className:"text-red-600 hover:text-red-800",children:r.jsx(a_,{className:"h-4 w-4"})})]})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Dosage"}),r.jsx(z.I,{value:e.dosage,onChange:t=>m(e.id,"dosage",t.target.value),placeholder:"e.g., 10mg daily"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Start Date (Month/Year)"}),r.jsx(z.I,{value:e.startDate,onChange:t=>m(e.id,"startDate",t.target.value),placeholder:"e.g., 03/2023",type:"month"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"End Date (Month/Year)"}),r.jsx(z.I,{value:e.endDate,onChange:t=>m(e.id,"endDate",t.target.value),placeholder:"Leave empty if current",type:"month"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Effectiveness"}),(0,r.jsxs)(s8,{value:e.effectiveness,onValueChange:t=>m(e.id,"effectiveness",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select effectiveness"})}),r.jsx(nq,{children:aJ.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[r.jsx(q,{children:"Reason for Discontinuation"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(s8,{value:e.discontinuationReason,onValueChange:t=>m(e.id,"discontinuationReason",t),children:[r.jsx(nz,{className:"flex-1",children:r.jsx(nt,{placeholder:"Select reason (if discontinued)"})}),r.jsx(nq,{children:aQ.map(e=>r.jsx(n_,{value:e,children:e},e))})]}),"Other"===e.discontinuationReason&&r.jsx(z.I,{value:e.discontinuationOther,onChange:t=>m(e.id,"discontinuationOther",t.target.value),placeholder:"Specify other reason",className:"flex-1"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Side Effects"}),r.jsx(n9,{value:e.sideEffects,onChange:t=>m(e.id,"sideEffects",t.target.value),placeholder:"Describe any side effects experienced",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:e.notes,onChange:t=>m(e.id,"notes",t.target.value),placeholder:"Additional details about medication use, response, etc.",rows:2})]})]})]},e.id))]}),0===s.length&&(0,r.jsxs)("div",{className:"text-center py-8 text-slate-500",children:[r.jsx("p",{children:"No medication history recorded."}),r.jsx("p",{className:"text-sm",children:"Use the form above to add medication information."})]})]})]})}let a1=["Diabetes Mellitus","Hypertension","Epilepsy/Seizure Disorder","Head trauma with loss of consciousness","Hypothyroidism","Hyperthyroidism","Cardiovascular disease","Chronic kidney disease","Liver disease","Autoimmune disorders"],a2=["Major Depressive Episode","Manic Episode","Hypomanic Episode","Mixed Episode","Psychotic Episode","Anxiety Episode","Other"],a4=["days","weeks","months","years"],a5=["Mild","Moderate","Severe"],a3=["Medication","Therapy","Hospitalization","None"],a6=["Good","Partial","Poor","Unknown"],a8=["Well-groomed","Disheveled","Appropriate dress","Inappropriate dress","Poor hygiene"],a7=["Calm","Agitated","Restless","Withdrawn","Cooperative","Uncooperative"],a9=["Normal","Rapid","Slow","Pressured"],ie=["Normal","Loud","Soft","Whispered"],it=["Euthymic","Depressed","Anxious","Irritable","Euphoric","Angry"],is=["Appropriate","Flat","Blunted","Labile","Inappropriate"],ia=["Linear","Tangential","Circumstantial","Flight of ideas","Loose associations"],ii=["Oriented x3","Oriented x2","Oriented x1","Disoriented"],ir=["Good","Fair","Poor","Absent"],il=["Good","Fair","Poor","Impaired"];var io=s(8307);let ic=(0,F.Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),id=[{code:"F32.9",name:"Major Depressive Disorder, Single Episode, Unspecified"},{code:"F33.9",name:"Major Depressive Disorder, Recurrent, Unspecified"},{code:"F41.1",name:"Generalized Anxiety Disorder"},{code:"F41.0",name:"Panic Disorder"},{code:"F43.10",name:"Post-Traumatic Stress Disorder, Unspecified"},{code:"F31.9",name:"Bipolar Disorder, Unspecified"},{code:"F20.9",name:"Schizophrenia, Unspecified"},{code:"F42.2",name:"Mixed Obsessional Thoughts and Acts"},{code:"F10.20",name:"Alcohol Use Disorder, Moderate"},{code:"F43.25",name:"Adjustment Disorder with Mixed Anxiety and Depressed Mood"},{code:"F60.3",name:"Borderline Personality Disorder"},{code:"F90.9",name:"Attention-Deficit/Hyperactivity Disorder, Unspecified"}],iu=[{value:"primary",label:"Primary"},{value:"secondary",label:"Secondary"},{value:"rule_out",label:"Rule Out"}],im=(0,F.Z)("Microscope",[["path",{d:"M6 18h8",key:"1borvv"}],["path",{d:"M3 22h18",key:"8prr45"}],["path",{d:"M14 22a7 7 0 1 0 0-14h-1",key:"1jwaiy"}],["path",{d:"M9 14h2",key:"197e7h"}],["path",{d:"M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z",key:"1bmzmy"}],["path",{d:"M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3",key:"1drr47"}]]),ih=(0,F.Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);"undefined"!=typeof window&&window.document&&window.document.createElement;var ip=e=>{let{present:t,children:s}=e,n=function(e){var t,s;let[n,a]=l.useState(),i=l.useRef(null),r=l.useRef(e),o=l.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>s[e][t]??e,t));return l.useEffect(()=>{let e=ix(i.current);o.current="mounted"===c?e:"none"},[c]),(0,tB.b)(()=>{let t=i.current,s=r.current;if(s!==e){let n=o.current,a=ix(t);e?d("MOUNT"):"none"===a||t?.display==="none"?d("UNMOUNT"):s&&n!==a?d("ANIMATION_OUT"):d("UNMOUNT"),r.current=e}},[e,d]),(0,tB.b)(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,s=s=>{let a=ix(i.current).includes(CSS.escape(s.animationName));if(s.target===n&&a&&(d("ANIMATION_END"),!r.current)){let s=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=s)})}},a=e=>{e.target===n&&(o.current=ix(i.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",s),n.addEventListener("animationend",s),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",s),n.removeEventListener("animationend",s)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:l.useCallback(e=>{i.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof s?s({present:n.isPresent}):l.Children.only(s),i=(0,X.e)(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(s=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof s||n.isPresent?l.cloneElement(a,{ref:i}):null};function ix(e){return e?.animationName||"none"}ip.displayName="Presence";var ig="Collapsible",[iv,iy]=(0,u.b)(ig),[ij,ib]=iv(ig),iN=l.forwardRef((e,t)=>{let{__scopeCollapsible:s,open:n,defaultOpen:a,disabled:i,onOpenChange:o,...c}=e,[d,u]=(0,t7.T)({prop:n,defaultProp:a??!1,onChange:o,caller:ig});return(0,r.jsx)(ij,{scope:s,disabled:i,contentId:(0,ef.M)(),open:d,onOpenToggle:l.useCallback(()=>u(e=>!e),[u]),children:(0,r.jsx)(m.WV.div,{"data-state":iP(d),"data-disabled":i?"":void 0,...c,ref:t})})});iN.displayName=ig;var iw="CollapsibleTrigger",iC=l.forwardRef((e,t)=>{let{__scopeCollapsible:s,...n}=e,a=ib(iw,s);return(0,r.jsx)(m.WV.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":iP(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:t,onClick:function(e,t,{checkForDefaultPrevented:s=!0}={}){return function(n){if(e?.(n),!1===s||!n.defaultPrevented)return t?.(n)}}(e.onClick,a.onOpenToggle)})});iC.displayName=iw;var iS="CollapsibleContent",ik=l.forwardRef((e,t)=>{let{forceMount:s,...n}=e,a=ib(iS,e.__scopeCollapsible);return(0,r.jsx)(ip,{present:s||a.open,children:({present:e})=>(0,r.jsx)(iE,{...n,ref:t,present:e})})});ik.displayName=iS;var iE=l.forwardRef((e,t)=>{let{__scopeCollapsible:s,present:n,children:a,...i}=e,o=ib(iS,s),[c,d]=l.useState(n),u=l.useRef(null),h=(0,X.e)(t,u),p=l.useRef(0),x=p.current,f=l.useRef(0),g=f.current,v=o.open||c,y=l.useRef(v),j=l.useRef(void 0);return l.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,tB.b)(()=>{let e=u.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();p.current=t.height,f.current=t.width,y.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),d(n)}},[o.open,n]),(0,r.jsx)(m.WV.div,{"data-state":iP(o.open),"data-disabled":o.disabled?"":void 0,id:o.contentId,hidden:!v,...i,ref:h,style:{"--radix-collapsible-content-height":x?`${x}px`:void 0,"--radix-collapsible-content-width":g?`${g}px`:void 0,...e.style},children:v&&a})});function iP(e){return e?"open":"closed"}let iA=(0,F.Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function iR({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{}),[a,i]=(0,l.useState)({thyroid:!1,vitamins:!1,liver:!1,cbc:!1,metabolic:!1,lipid:!1,inflammatory:!1,hormonal:!1}),o=(e,t,s)=>{n(n=>({...n,[e]:{...n[e],[t]:s}}))},d=e=>{i(t=>({...t,[e]:!t[e]}))},u=(e,t)=>{if(!e||!t)return!1;let s=parseFloat(e);if(isNaN(s))return!1;if(t.includes("-")){let[e,n]=t.split("-").map(e=>parseFloat(e));return s<e||s>n}return t.startsWith("<")?s>=parseFloat(t.substring(1)):!!t.startsWith(">")&&s<=parseFloat(t.substring(1))},m=(e,t,s,n,a,i)=>{let l=u(i||"",a);return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx(q,{className:"text-sm font-medium",children:s}),l&&r.jsx(aU,{className:"h-4 w-4 text-red-500"}),i&&!l&&r.jsx(B.Z,{className:"h-4 w-4 text-green-500"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(z.I,{value:i||"",onChange:s=>o(e,t,s.target.value),placeholder:"Enter value",className:l?"border-red-300 focus:border-red-500":""}),r.jsx(aY.C,{variant:"outline",className:"text-xs whitespace-nowrap",children:n})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Normal: ",a," ",n]})]})};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Comprehensive Blood Work Panel"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Enter laboratory test results with automatic abnormal value detection"})]}),r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a.cbc,onOpenChange:()=>d("cbc"),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(c.ll,{className:"text-lg",children:"Complete Blood Count (CBC)"}),r.jsx(c.SZ,{children:"Hemoglobin, Hematocrit, WBC with differential, Platelets, RBC indices"})]}),a.cbc?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.completeBloodCount?.datePerformed||"",onChange:e=>o("completeBloodCount","datePerformed",e.target.value)})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Basic Parameters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[m("completeBloodCount","hemoglobin","Hemoglobin","g/dL","12.0-16.0",s.completeBloodCount?.hemoglobin),m("completeBloodCount","hematocrit","Hematocrit","%","36-46",s.completeBloodCount?.hematocrit),m("completeBloodCount","wbc","WBC Count","K/uL","4.5-11.0",s.completeBloodCount?.wbc),m("completeBloodCount","platelets","Platelets","K/uL","150-450",s.completeBloodCount?.platelets)]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"WBC Differential"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[m("completeBloodCount","neutrophils","Neutrophils","%","50-70",s.completeBloodCount?.neutrophils),m("completeBloodCount","lymphocytes","Lymphocytes","%","20-40",s.completeBloodCount?.lymphocytes),m("completeBloodCount","monocytes","Monocytes","%","2-8",s.completeBloodCount?.monocytes),m("completeBloodCount","eosinophils","Eosinophils","%","1-4",s.completeBloodCount?.eosinophils),m("completeBloodCount","basophils","Basophils","%","0.5-1",s.completeBloodCount?.basophils)]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"RBC Indices"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[m("completeBloodCount","mcv","MCV","fL","80-100",s.completeBloodCount?.mcv),m("completeBloodCount","mch","MCH","pg","27-32",s.completeBloodCount?.mch),m("completeBloodCount","mchc","MCHC","g/dL","32-36",s.completeBloodCount?.mchc)]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:s.completeBloodCount?.interpretation||"",onChange:e=>o("completeBloodCount","interpretation",e.target.value),placeholder:"Enter clinical interpretation of CBC results",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.completeBloodCount?.notes||"",onChange:e=>o("completeBloodCount","notes",e.target.value),placeholder:"Additional notes about CBC",rows:2})]})]})})]})}),r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a.metabolic,onOpenChange:()=>d("metabolic"),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(c.ll,{className:"text-lg",children:"Metabolic Panel (CMP)"}),r.jsx(c.SZ,{children:"Glucose, HbA1c, Electrolytes, Kidney function, Lipids"})]}),a.metabolic?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.metabolicPanel?.datePerformed||"",onChange:e=>o("metabolicPanel","datePerformed",e.target.value)})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Glucose & Diabetes Markers"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[m("metabolicPanel","glucose","Fasting Glucose","mg/dL","70-100",s.metabolicPanel?.glucose),m("metabolicPanel","hba1c","HbA1c","%","<5.7",s.metabolicPanel?.hba1c)]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Kidney Function"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[m("metabolicPanel","creatinine","Creatinine","mg/dL","0.6-1.2",s.metabolicPanel?.creatinine),m("metabolicPanel","bun","BUN","mg/dL","7-20",s.metabolicPanel?.bun),m("metabolicPanel","egfr","eGFR","mL/min/1.73m\xb2",">60",s.metabolicPanel?.egfr)]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm text-slate-700 mb-3",children:"Electrolytes"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[m("metabolicPanel","sodium","Sodium","mEq/L","136-145",s.metabolicPanel?.sodium),m("metabolicPanel","potassium","Potassium","mEq/L","3.5-5.0",s.metabolicPanel?.potassium),m("metabolicPanel","chloride","Chloride","mEq/L","98-107",s.metabolicPanel?.chloride),m("metabolicPanel","co2","CO2","mEq/L","22-28",s.metabolicPanel?.co2)]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:m("metabolicPanel","anionGap","Anion Gap","mEq/L","8-16",s.metabolicPanel?.anionGap)})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:s.metabolicPanel?.interpretation||"",onChange:e=>o("metabolicPanel","interpretation",e.target.value),placeholder:"Enter clinical interpretation of metabolic panel results",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.metabolicPanel?.notes||"",onChange:e=>o("metabolicPanel","notes",e.target.value),placeholder:"Additional notes about metabolic panel",rows:2})]})]})})]})}),r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a.lipid,onOpenChange:()=>d("lipid"),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(c.ll,{className:"text-lg",children:"Lipid Panel"}),r.jsx(c.SZ,{children:"Total Cholesterol, LDL, HDL, Triglycerides"})]}),a.lipid?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.lipidPanel?.datePerformed||"",onChange:e=>o("lipidPanel","datePerformed",e.target.value)})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[m("lipidPanel","totalCholesterol","Total Cholesterol","mg/dL","<200",s.lipidPanel?.totalCholesterol),m("lipidPanel","ldl","LDL Cholesterol","mg/dL","<100",s.lipidPanel?.ldl),m("lipidPanel","hdl","HDL Cholesterol","mg/dL",">40",s.lipidPanel?.hdl),m("lipidPanel","triglycerides","Triglycerides","mg/dL","<150",s.lipidPanel?.triglycerides)]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:s.lipidPanel?.interpretation||"",onChange:e=>o("lipidPanel","interpretation",e.target.value),placeholder:"Enter clinical interpretation of lipid panel results",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.lipidPanel?.notes||"",onChange:e=>o("lipidPanel","notes",e.target.value),placeholder:"Additional notes about lipid panel",rows:2})]})]})})]})}),r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a.liver,onOpenChange:()=>d("liver"),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(c.ll,{className:"text-lg",children:"Liver Function Tests (LFT)"}),r.jsx(c.SZ,{children:"ALT, AST, Bilirubin, Alkaline Phosphatase, GGT, Albumin, PT/INR"})]}),a.liver?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.liverFunction?.datePerformed||"",onChange:e=>o("liverFunction","datePerformed",e.target.value)})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[m("liverFunction","alt","ALT","U/L","7-56",s.liverFunction?.alt),m("liverFunction","ast","AST","U/L","10-40",s.liverFunction?.ast),m("liverFunction","totalBilirubin","Total Bilirubin","mg/dL","0.2-1.2",s.liverFunction?.totalBilirubin),m("liverFunction","directBilirubin","Direct Bilirubin","mg/dL","0.0-0.3",s.liverFunction?.directBilirubin),m("liverFunction","alkalinePhosphatase","Alkaline Phosphatase","U/L","44-147",s.liverFunction?.alkalinePhosphatase),m("liverFunction","ggt","GGT","U/L","9-48",s.liverFunction?.ggt),m("liverFunction","albumin","Albumin","g/dL","3.5-5.0",s.liverFunction?.albumin),m("liverFunction","ptInr","PT/INR","ratio","0.8-1.1",s.liverFunction?.ptInr)]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:s.liverFunction?.interpretation||"",onChange:e=>o("liverFunction","interpretation",e.target.value),placeholder:"Enter clinical interpretation of liver function results",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.liverFunction?.notes||"",onChange:e=>o("liverFunction","notes",e.target.value),placeholder:"Additional notes about liver function tests",rows:2})]})]})})]})}),r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a.thyroid,onOpenChange:()=>d("thyroid"),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(c.ll,{className:"text-lg",children:"Thyroid Function Tests"}),r.jsx(c.SZ,{children:"TSH, Free T3, Free T4, Reverse T3"})]}),a.thyroid?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.thyroidFunction?.datePerformed||"",onChange:e=>o("thyroidFunction","datePerformed",e.target.value)})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[m("thyroidFunction","tsh","TSH","mIU/L","0.4-4.0",s.thyroidFunction?.tsh),m("thyroidFunction","freeT3","Free T3","pg/mL","2.3-4.2",s.thyroidFunction?.freeT3),m("thyroidFunction","freeT4","Free T4","ng/dL","0.8-1.8",s.thyroidFunction?.freeT4),m("thyroidFunction","reverseT3","Reverse T3","ng/dL","8-25",s.thyroidFunction?.reverseT3)]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:s.thyroidFunction?.interpretation||"",onChange:e=>o("thyroidFunction","interpretation",e.target.value),placeholder:"Enter clinical interpretation of thyroid function results",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.thyroidFunction?.notes||"",onChange:e=>o("thyroidFunction","notes",e.target.value),placeholder:"Additional notes about thyroid function tests",rows:2})]})]})})]})}),r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a.vitamins,onOpenChange:()=>d("vitamins"),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(c.ll,{className:"text-lg",children:"Vitamin Levels"}),r.jsx(c.SZ,{children:"B12, D3, Folate, B1 (Thiamine), B6"})]}),a.vitamins?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.vitaminLevels?.datePerformed||"",onChange:e=>o("vitaminLevels","datePerformed",e.target.value)})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[m("vitaminLevels","vitaminB12","Vitamin B12","pg/mL","300-900",s.vitaminLevels?.vitaminB12),m("vitaminLevels","vitaminD3","Vitamin D3","ng/mL","30-100",s.vitaminLevels?.vitaminD3),m("vitaminLevels","folate","Folate","ng/mL","3-17",s.vitaminLevels?.folate),m("vitaminLevels","thiamine","Thiamine (B1)","nmol/L","70-180",s.vitaminLevels?.thiamine),m("vitaminLevels","vitaminB6","Vitamin B6","ng/mL","5-50",s.vitaminLevels?.vitaminB6)]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:s.vitaminLevels?.interpretation||"",onChange:e=>o("vitaminLevels","interpretation",e.target.value),placeholder:"Enter clinical interpretation of vitamin level results",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.vitaminLevels?.notes||"",onChange:e=>o("vitaminLevels","notes",e.target.value),placeholder:"Additional notes about vitamin levels",rows:2})]})]})})]})})]})}let iD=[{key:"cannabis",label:"Cannabis (THC/CBD)"},{key:"cocaine",label:"Cocaine"},{key:"amphetamines",label:"Amphetamines/Methamphetamines"},{key:"opiates",label:"Opiates (Morphine, Codeine, Heroin)"},{key:"oxycodone",label:"Oxycodone/Oxymorphone"},{key:"benzodiazepines",label:"Benzodiazepines"},{key:"barbiturates",label:"Barbiturates"},{key:"pcp",label:"Phencyclidine (PCP)"},{key:"alcohol",label:"Alcohol/Ethanol"},{key:"syntheticDrugs",label:"Synthetic Drugs (K2/Spice, Bath salts)"},{key:"prescriptionDrugs",label:"Prescription Drug Misuse"},{key:"other",label:"Other Substances"}];function iM({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{substances:{}}),a=(e,t)=>{n(s=>({...s,substances:{...s.substances,[e]:t}}))},i=(e,t)=>{n(s=>({...s,[e]:t}))},o=()=>s.substances?Object.values(s.substances).filter(Boolean).length:0;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Toxicology Screening"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Simple substance screening checklist"})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Substance Screening"}),(0,r.jsxs)(c.SZ,{children:["Check substances that were tested or are of clinical concern",o()>0&&(0,r.jsxs)(aY.C,{variant:"outline",className:"ml-2",children:[o()," selected"]})]})]}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:s.datePerformed||"",onChange:e=>i("datePerformed",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(q,{className:"text-base font-medium",children:"Substances"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:iD.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{id:e.key,checked:s.substances?.[e.key]||!1,onCheckedChange:t=>a(e.key,t)}),r.jsx(q,{htmlFor:e.key,className:"text-sm font-normal cursor-pointer",children:e.label})]},e.key))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Comments & Notes"}),r.jsx(n9,{value:s.comments||"",onChange:e=>i("comments",e.target.value),placeholder:"Enter any additional comments, test results, levels detected, or clinical notes...",rows:4})]})]})]})]})}let iT=(0,F.Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),iL=[{name:"PHQ-9",fullName:"Patient Health Questionnaire-9",category:"Depression",description:"9-item depression screening tool",scoringRange:"0-27",interpretation:{"0-4":"Minimal depression","5-9":"Mild depression","10-14":"Moderate depression","15-19":"Moderately severe depression","20-27":"Severe depression"}},{name:"GAD-7",fullName:"Generalized Anxiety Disorder 7-item",category:"Anxiety",description:"7-item anxiety screening tool",scoringRange:"0-21",interpretation:{"0-4":"Minimal anxiety","5-9":"Mild anxiety","10-14":"Moderate anxiety","15-21":"Severe anxiety"}},{name:"MMSE",fullName:"Mini-Mental State Examination",category:"Cognitive",description:"Cognitive function screening",scoringRange:"0-30",interpretation:{"24-30":"Normal cognition","18-23":"Mild cognitive impairment","0-17":"Severe cognitive impairment"}},{name:"Beck Depression Inventory",fullName:"Beck Depression Inventory (BDI-II)",category:"Depression",description:"21-item depression assessment",scoringRange:"0-63",interpretation:{"0-13":"Minimal depression","14-19":"Mild depression","20-28":"Moderate depression","29-63":"Severe depression"}},{name:"Beck Anxiety Inventory",fullName:"Beck Anxiety Inventory (BAI)",category:"Anxiety",description:"21-item anxiety assessment",scoringRange:"0-63",interpretation:{"0-7":"Minimal anxiety","8-15":"Mild anxiety","16-25":"Moderate anxiety","26-63":"Severe anxiety"}},{name:"DASS-21",fullName:"Depression, Anxiety and Stress Scale",category:"Multi-domain",description:"21-item assessment of depression, anxiety, and stress",scoringRange:"0-63 (each subscale 0-21)",interpretation:{Depression:"Normal: 0-4, Mild: 5-6, Moderate: 7-10, Severe: 11-13, Extremely severe: 14+",Anxiety:"Normal: 0-3, Mild: 4-5, Moderate: 6-7, Severe: 8-9, Extremely severe: 10+",Stress:"Normal: 0-7, Mild: 8-9, Moderate: 10-12, Severe: 13-16, Extremely severe: 17+"}},{name:"MOCA",fullName:"Montreal Cognitive Assessment",category:"Cognitive",description:"Cognitive screening for mild cognitive impairment",scoringRange:"0-30",interpretation:{"26-30":"Normal cognition","18-25":"Mild cognitive impairment","0-17":"Moderate to severe cognitive impairment"}},{name:"Y-BOCS",fullName:"Yale-Brown Obsessive Compulsive Scale",category:"OCD",description:"10-item OCD severity assessment",scoringRange:"0-40",interpretation:{"0-7":"Subclinical","8-15":"Mild","16-23":"Moderate","24-31":"Severe","32-40":"Extreme"}}];function iO({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{psychologicalAssessments:{}}),[a,i]=(0,l.useState)({}),o=(e,t,s)=>{n(n=>({...n,psychologicalAssessments:{...n.psychologicalAssessments,[e]:{...n.psychologicalAssessments[e],testName:e,[t]:s}}}))},d=e=>{n(t=>{let s={...t.psychologicalAssessments};return delete s[e],{...t,psychologicalAssessments:s}}),i(t=>{let s={...t};return delete s[e],s})},u=e=>{i(t=>({...t,[e]:!t[e]}))},m=(e,t)=>{let s=iL.find(t=>t.name===e);if(!s||!s.interpretation)return"";for(let[e,n]of Object.entries(s.interpretation))if(e.includes("-")){let[s,a]=e.split("-").map(Number);if(t>=s&&t<=a)return n}else if(e.includes("+")&&t>=parseInt(e.replace("+","")))return n;return""},h=e=>{let t=e.toLowerCase();return t.includes("severe")||t.includes("extreme")?"text-red-600":t.includes("moderate")?"text-orange-600":t.includes("mild")?"text-yellow-600":t.includes("minimal")||t.includes("normal")?"text-green-600":"text-gray-600"},p=iL.filter(e=>!s.psychologicalAssessments[e.name]);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Psychological Assessment Tools"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Standardized psychological testing and assessment instruments"})]}),p.length>0&&(0,r.jsxs)(s8,{onValueChange:e=>{iL.find(t=>t.name===e),n(t=>({...t,psychologicalAssessments:{...t.psychologicalAssessments,[e]:{testName:e,isCompleted:!1,datePerformed:new Date().toISOString().split("T")[0]}}})),i(t=>({...t,[e]:!0}))},children:[r.jsx(nz,{className:"w-64",children:r.jsx(nt,{placeholder:"Add assessment tool..."})}),r.jsx(nq,{children:p.map(e=>r.jsx(n_,{value:e.name,children:(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx("span",{className:"font-medium",children:e.name}),r.jsx("span",{className:"text-xs text-slate-500",children:e.fullName})]})},e.name))})]})]}),0===Object.keys(s.psychologicalAssessments).length&&r.jsx(c.Zb,{children:(0,r.jsxs)(c.aY,{className:"text-center py-8 text-slate-500",children:[r.jsx(R.Z,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),r.jsx("p",{children:"No psychological assessments added yet."}),r.jsx("p",{className:"text-sm mt-2",children:"Use the dropdown above to add standardized assessment tools."})]})}),Object.entries(s.psychologicalAssessments).map(([e,t])=>{let s=iL.find(t=>t.name===e),n=t.totalScore?m(e,t.totalScore):"";return r.jsx(c.Zb,{children:(0,r.jsxs)(iN,{open:a[e],onOpenChange:()=>u(e),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)(c.ll,{className:"text-lg flex items-center gap-2",children:[e,t.isCompleted&&r.jsx(B.Z,{className:"h-5 w-5 text-green-500"})]}),(0,r.jsxs)(c.SZ,{children:[s?.fullName," • ",s?.category,t.totalScore&&(0,r.jsxs)("span",{className:"ml-2",children:["Score: ",t.totalScore,n&&(0,r.jsxs)("span",{className:`ml-1 font-medium ${h(n)}`,children:["(",n,")"]})]})]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(P.z,{variant:"ghost",size:"sm",onClick:t=>{t.stopPropagation(),d(e)},className:"text-red-500 hover:text-red-700",children:r.jsx(a_,{className:"h-4 w-4"})}),a[e]?r.jsx(nB,{className:"h-5 w-5"}):r.jsx(iA,{className:"h-5 w-5"})]})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:t.datePerformed||"",onChange:t=>o(e,"datePerformed",t.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Total Score"}),r.jsx(z.I,{type:"number",value:t.totalScore||"",onChange:t=>o(e,"totalScore",parseInt(t.target.value)||0),placeholder:`Range: ${s?.scoringRange||"N/A"}`})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Completion Status"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 pt-2",children:[r.jsx(af,{checked:t.isCompleted,onCheckedChange:t=>o(e,"isCompleted",t)}),r.jsx("span",{className:"text-sm",children:"Assessment completed"})]})]})]}),n&&r.jsx("div",{className:"p-3 bg-slate-50 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(iT,{className:"h-4 w-4 text-slate-600"}),r.jsx("span",{className:"text-sm font-medium",children:"Interpretation:"}),r.jsx("span",{className:`text-sm font-medium ${h(n)}`,children:n})]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Clinical Interpretation"}),r.jsx(n9,{value:t.interpretation||"",onChange:t=>o(e,"interpretation",t.target.value),placeholder:"Enter clinical interpretation and context",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:t.notes||"",onChange:t=>o(e,"notes",t.target.value),placeholder:"Additional notes, observations, or context",rows:2})]}),s&&r.jsx("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm",children:[r.jsx("div",{className:"font-medium text-blue-900 mb-1",children:"Assessment Information"}),(0,r.jsxs)("div",{className:"text-blue-700",children:[r.jsx("div",{children:s.description}),(0,r.jsxs)("div",{className:"mt-1",children:["Scoring Range: ",s.scoringRange]})]})]})})]})})]})},e)})]})}var iI=s(3634);let iF=[{name:"Brain MRI",category:"Neuroimaging",description:"Magnetic Resonance Imaging of the brain",commonFindings:["Normal","White matter changes","Atrophy","Lesions","Vascular changes"]},{name:"Brain CT",category:"Neuroimaging",description:"Computed Tomography of the brain",commonFindings:["Normal","Atrophy","Hemorrhage","Infarct","Mass effect"]},{name:"EEG",category:"Neurophysiology",description:"Electroencephalogram",commonFindings:["Normal","Generalized slowing","Focal abnormalities","Epileptiform activity","Sleep abnormalities"]},{name:"SPECT",category:"Nuclear Medicine",description:"Single Photon Emission Computed Tomography",commonFindings:["Normal perfusion","Hypoperfusion","Hyperperfusion","Asymmetric perfusion"]},{name:"PET Scan",category:"Nuclear Medicine",description:"Positron Emission Tomography",commonFindings:["Normal metabolism","Hypometabolism","Hypermetabolism","Alzheimer pattern"]},{name:"fMRI",category:"Functional Imaging",description:"Functional Magnetic Resonance Imaging",commonFindings:["Normal activation","Altered activation patterns","Connectivity changes"]},{name:"DTI",category:"Advanced MRI",description:"Diffusion Tensor Imaging",commonFindings:["Normal white matter integrity","Reduced FA","Increased diffusivity","Tract disruption"]},{name:"Neuropsychological Testing",category:"Cognitive Assessment",description:"Comprehensive cognitive evaluation",commonFindings:["Normal cognition","Memory impairment","Executive dysfunction","Language deficits","Attention deficits"]}],iH=[{name:"EMG",category:"Neurophysiology",description:"Electromyography",commonFindings:["Normal","Myopathic changes","Neuropathic changes","Denervation"]},{name:"Nerve Conduction Study",category:"Neurophysiology",description:"Peripheral nerve function assessment",commonFindings:["Normal","Demyelinating","Axonal","Mixed pattern"]},{name:"Evoked Potentials",category:"Neurophysiology",description:"Visual, auditory, or somatosensory evoked potentials",commonFindings:["Normal latencies","Prolonged latencies","Reduced amplitudes","Absent responses"]},{name:"Sleep Study",category:"Sleep Medicine",description:"Polysomnography",commonFindings:["Normal sleep","Sleep apnea","Periodic limb movements","REM abnormalities","Insomnia"]}];function iV({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{imagingStudies:{},neurologicalTests:{}}),[a,i]=(0,l.useState)({}),o=(e,t,s)=>{n(n=>({...n,imagingStudies:{...n.imagingStudies,[e]:{...n.imagingStudies[e],testName:e,[t]:s}}}))},d=(e,t,s)=>{n(n=>({...n,neurologicalTests:{...n.neurologicalTests,[e]:{...n.neurologicalTests[e],testName:e,[t]:s}}}))},u=e=>{n(t=>{let s={...t.imagingStudies};return delete s[e],{...t,imagingStudies:s}}),i(t=>{let s={...t};return delete s[`imaging-${e}`],s})},m=e=>{n(t=>{let s={...t.neurologicalTests};return delete s[e],{...t,neurologicalTests:s}}),i(t=>{let s={...t};return delete s[`neuro-${e}`],s})},h=e=>{i(t=>({...t,[e]:!t[e]}))},p=iF.filter(e=>!s.imagingStudies[e.name]),x=iH.filter(e=>!s.neurologicalTests[e.name]);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-bold text-slate-900 mb-2",children:"Imaging Studies & Neurological Tests"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Neuroimaging, EEG, and other diagnostic procedures"})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(c.ll,{className:"flex items-center gap-2",children:[r.jsx(R.Z,{className:"h-5 w-5"}),"Imaging Studies"]}),r.jsx(c.SZ,{children:"Neuroimaging and brain imaging studies"})]}),p.length>0&&(0,r.jsxs)(s8,{onValueChange:e=>{let t=iF.find(t=>t.name===e);n(s=>({...s,imagingStudies:{...s.imagingStudies,[e]:{testName:e,category:t?.category||"",isNormal:!0,isUrgent:!1,followUpRequired:!1,datePerformed:new Date().toISOString().split("T")[0]}}})),i(t=>({...t,[`imaging-${e}`]:!0}))},children:[r.jsx(nz,{className:"w-48",children:r.jsx(nt,{placeholder:"Add imaging study..."})}),r.jsx(nq,{children:p.map(e=>r.jsx(n_,{value:e.name,children:(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx("span",{className:"font-medium",children:e.name}),r.jsx("span",{className:"text-xs text-slate-500",children:e.category})]})},e.name))})]})]})}),r.jsx(c.aY,{children:0===Object.keys(s.imagingStudies).length?(0,r.jsxs)("div",{className:"text-center py-6 text-slate-500",children:[r.jsx(ih,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),r.jsx("p",{className:"text-sm",children:"No imaging studies added yet."})]}):r.jsx("div",{className:"space-y-4",children:Object.entries(s.imagingStudies).map(([e,t])=>{let s=`imaging-${e}`,n=iF.find(t=>t.name===e);return r.jsx(c.Zb,{className:"border-l-4 border-l-blue-500",children:(0,r.jsxs)(iN,{open:a[s],onOpenChange:()=>h(s),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors py-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)(c.ll,{className:"text-base flex items-center gap-2",children:[e,t.isUrgent&&r.jsx(aU,{className:"h-4 w-4 text-red-500"}),!t.isNormal&&r.jsx(aY.C,{variant:"destructive",className:"text-xs",children:"Abnormal"}),t.followUpRequired&&r.jsx(aY.C,{variant:"outline",className:"text-xs",children:"Follow-up"})]}),(0,r.jsxs)(c.SZ,{className:"text-sm",children:[t.category," • ",t.datePerformed||"Date not set"]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(P.z,{variant:"ghost",size:"sm",onClick:t=>{t.stopPropagation(),u(e)},className:"text-red-500 hover:text-red-700",children:r.jsx(a_,{className:"h-4 w-4"})}),a[s]?r.jsx(nB,{className:"h-4 w-4"}):r.jsx(iA,{className:"h-4 w-4"})]})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:t.datePerformed||"",onChange:t=>o(e,"datePerformed",t.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Facility/Location"}),r.jsx(z.I,{value:t.facility||"",onChange:t=>o(e,"facility",t.target.value),placeholder:"Hospital/clinic name"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{checked:t.isNormal,onCheckedChange:t=>o(e,"isNormal",t)}),r.jsx(q,{className:"text-sm",children:"Normal findings"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{checked:t.isUrgent,onCheckedChange:t=>o(e,"isUrgent",t)}),r.jsx(q,{className:"text-sm",children:"Urgent/Critical"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{checked:t.followUpRequired,onCheckedChange:t=>o(e,"followUpRequired",t)}),r.jsx(q,{className:"text-sm",children:"Follow-up required"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Findings"}),r.jsx(n9,{value:t.findings||"",onChange:t=>o(e,"findings",t.target.value),placeholder:"Detailed findings and observations",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Impression/Conclusion"}),r.jsx(n9,{value:t.impression||"",onChange:t=>o(e,"impression",t.target.value),placeholder:"Radiologist's impression and clinical correlation",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:t.notes||"",onChange:t=>o(e,"notes",t.target.value),placeholder:"Additional clinical notes or context",rows:2})]}),n&&r.jsx("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm",children:[r.jsx("div",{className:"font-medium text-blue-900 mb-1",children:"Study Information"}),r.jsx("div",{className:"text-blue-700",children:n.description}),n.commonFindings&&(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx("span",{className:"font-medium",children:"Common findings: "}),n.commonFindings.join(", ")]})]})})]})})]})},e)})})})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(c.ll,{className:"flex items-center gap-2",children:[r.jsx(iI.Z,{className:"h-5 w-5"}),"Neurological Tests"]}),r.jsx(c.SZ,{children:"Neurophysiology and specialized neurological assessments"})]}),x.length>0&&(0,r.jsxs)(s8,{onValueChange:e=>{let t=iH.find(t=>t.name===e);n(s=>({...s,neurologicalTests:{...s.neurologicalTests,[e]:{testName:e,category:t?.category||"",isNormal:!0,followUpRequired:!1,datePerformed:new Date().toISOString().split("T")[0]}}})),i(t=>({...t,[`neuro-${e}`]:!0}))},children:[r.jsx(nz,{className:"w-48",children:r.jsx(nt,{placeholder:"Add neurological test..."})}),r.jsx(nq,{children:x.map(e=>r.jsx(n_,{value:e.name,children:(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx("span",{className:"font-medium",children:e.name}),r.jsx("span",{className:"text-xs text-slate-500",children:e.category})]})},e.name))})]})]})}),r.jsx(c.aY,{children:0===Object.keys(s.neurologicalTests).length?(0,r.jsxs)("div",{className:"text-center py-6 text-slate-500",children:[r.jsx(iI.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),r.jsx("p",{className:"text-sm",children:"No neurological tests added yet."})]}):r.jsx("div",{className:"space-y-4",children:Object.entries(s.neurologicalTests).map(([e,t])=>{let s=`neuro-${e}`,n=iH.find(t=>t.name===e);return r.jsx(c.Zb,{className:"border-l-4 border-l-green-500",children:(0,r.jsxs)(iN,{open:a[s],onOpenChange:()=>h(s),children:[r.jsx(iC,{asChild:!0,children:r.jsx(c.Ol,{className:"cursor-pointer hover:bg-slate-50 transition-colors py-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)(c.ll,{className:"text-base flex items-center gap-2",children:[e,!t.isNormal&&r.jsx(aY.C,{variant:"destructive",className:"text-xs",children:"Abnormal"}),t.followUpRequired&&r.jsx(aY.C,{variant:"outline",className:"text-xs",children:"Follow-up"})]}),(0,r.jsxs)(c.SZ,{className:"text-sm",children:[t.category," • ",t.datePerformed||"Date not set"]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(P.z,{variant:"ghost",size:"sm",onClick:t=>{t.stopPropagation(),m(e)},className:"text-red-500 hover:text-red-700",children:r.jsx(a_,{className:"h-4 w-4"})}),a[s]?r.jsx(nB,{className:"h-4 w-4"}):r.jsx(iA,{className:"h-4 w-4"})]})]})})}),r.jsx(ik,{children:(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Date Performed"}),r.jsx(z.I,{type:"date",value:t.datePerformed||"",onChange:t=>d(e,"datePerformed",t.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Facility/Location"}),r.jsx(z.I,{value:t.facility||"",onChange:t=>d(e,"facility",t.target.value),placeholder:"Hospital/clinic name"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{checked:t.isNormal,onCheckedChange:t=>d(e,"isNormal",t)}),r.jsx(q,{className:"text-sm",children:"Normal results"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{checked:t.followUpRequired,onCheckedChange:t=>d(e,"followUpRequired",t)}),r.jsx(q,{className:"text-sm",children:"Follow-up required"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Results"}),r.jsx(n9,{value:t.results||"",onChange:t=>d(e,"results",t.target.value),placeholder:"Detailed test results and measurements",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Interpretation"}),r.jsx(n9,{value:t.interpretation||"",onChange:t=>d(e,"interpretation",t.target.value),placeholder:"Clinical interpretation and significance",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:t.notes||"",onChange:t=>d(e,"notes",t.target.value),placeholder:"Additional clinical notes or context",rows:2})]}),n&&r.jsx("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm",children:[r.jsx("div",{className:"font-medium text-green-900 mb-1",children:"Test Information"}),r.jsx("div",{className:"text-green-700",children:n.description}),n.commonFindings&&(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx("span",{className:"font-medium",children:"Common findings: "}),n.commonFindings.join(", ")]})]})})]})})]})},e)})})})]})]})}let iB=[{id:"demographics",label:"Demographics",icon:A.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{}),a=(e,t)=>{n(s=>({...s,[e]:t}))},i=e=>{let t=new Date,s=new Date(e),n=t.getFullYear()-s.getFullYear(),a=t.getMonth()-s.getMonth();return(a<0||0===a&&t.getDate()<s.getDate())&&n--,n},o=e=>{a("dateOfBirth",e),e&&a("age",i(e))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Demographics Information"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Please provide basic demographic information using anonymous patient codes for privacy."})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Patient Identification"}),r.jsx(c.SZ,{children:"Use anonymous patient codes to maintain privacy"})]}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"patientCode",children:"Anonymous Patient Code"}),r.jsx(z.I,{id:"patientCode",value:s.patientCode||"",onChange:e=>a("patientCode",e.target.value),placeholder:"Enter anonymous patient code (e.g., PT-2024-001)",className:"font-mono"}),r.jsx("p",{className:"text-xs text-slate-500",children:"Use a pre-generated anonymous code to identify this patient while maintaining privacy"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"dateOfBirth",children:"Date of Birth"}),r.jsx(z.I,{id:"dateOfBirth",type:"date",value:s.dateOfBirth||"",onChange:e=>o(e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"age",children:"Age"}),r.jsx(z.I,{id:"age",type:"number",value:s.age||"",onChange:e=>a("age",parseInt(e.target.value)||0),placeholder:"Age",readOnly:!!s.dateOfBirth})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"gender",children:"Gender"}),(0,r.jsxs)(s8,{value:s.gender||"",onValueChange:e=>a("gender",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select gender"})}),r.jsx(nq,{children:nJ.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"General Location"}),r.jsx(c.SZ,{children:"General location information for demographic analysis (no specific addresses)"})]}),r.jsx(c.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"generalLocation",children:"General Location Type"}),(0,r.jsxs)(s8,{value:s.generalLocation||"",onValueChange:e=>a("generalLocation",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select location type"})}),(0,r.jsxs)(nq,{children:[r.jsx(n_,{value:"urban",children:"Urban"}),r.jsx(n_,{value:"suburban",children:"Suburban"}),r.jsx(n_,{value:"rural",children:"Rural"}),r.jsx(n_,{value:"other",children:"Other"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"region",children:"General Region"}),(0,r.jsxs)(s8,{value:s.region||"",onValueChange:e=>a("region",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select region"})}),(0,r.jsxs)(nq,{children:[r.jsx(n_,{value:"northeast",children:"Northeast"}),r.jsx(n_,{value:"southeast",children:"Southeast"}),r.jsx(n_,{value:"midwest",children:"Midwest"}),r.jsx(n_,{value:"southwest",children:"Southwest"}),r.jsx(n_,{value:"west",children:"West"}),r.jsx(n_,{value:"other",children:"Other"})]})]})]})]})})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Demographics"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"ethnicity",children:"Ethnicity"}),(0,r.jsxs)(s8,{value:s.ethnicity||"",onValueChange:e=>a("ethnicity",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select ethnicity"})}),r.jsx(nq,{children:nQ.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"race",children:"Race"}),(0,r.jsxs)(s8,{value:s.race||"",onValueChange:e=>a("race",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select race"})}),r.jsx(nq,{children:n0.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"primaryLanguage",children:"Primary Language"}),r.jsx(z.I,{id:"primaryLanguage",value:s.primaryLanguage||"",onChange:e=>a("primaryLanguage",e.target.value),placeholder:"English"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"maritalStatus",children:"Marital Status"}),(0,r.jsxs)(s8,{value:s.maritalStatus||"",onValueChange:e=>a("maritalStatus",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select marital status"})}),r.jsx(nq,{children:nX.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Social Information"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"education",children:"Education Level"}),(0,r.jsxs)(s8,{value:s.education||"",onValueChange:e=>a("education",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select education level"})}),r.jsx(nq,{children:nG.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"occupation",children:"Occupation"}),(0,r.jsxs)(s8,{value:s.occupation||"",onValueChange:e=>a("occupation",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select occupation"})}),r.jsx(nq,{children:n$.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"employmentStatus",children:"Employment Status"}),(0,r.jsxs)(s8,{value:s.employmentStatus||"",onValueChange:e=>a("employmentStatus",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select employment status"})}),r.jsx(nq,{children:n2.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"livingArrangement",children:"Living Arrangement"}),(0,r.jsxs)(s8,{value:s.livingArrangement||"",onValueChange:e=>a("livingArrangement",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select living arrangement"})}),r.jsx(nq,{children:nK.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Insurance Information"})}),r.jsx(c.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{htmlFor:"insuranceType",children:"Insurance Type"}),(0,r.jsxs)(s8,{value:s.insuranceType||"",onValueChange:e=>a("insuranceType",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select insurance type"})}),r.jsx(nq,{children:n1.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})})]})]})}},{id:"symptoms",label:"Symptoms",icon:R.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{selectedSymptoms:[],symptomDetails:{},additionalSymptoms:""}),a=(e,t)=>{let a=t?[...s.selectedSymptoms||[],e]:(s.selectedSymptoms||[]).filter(t=>t!==e);n(e=>({...e,selectedSymptoms:a}))},i=(e,t,s)=>{n(n=>({...n,symptomDetails:{...n.symptomDetails,[e]:{...n.symptomDetails?.[e],[t]:s}}}))};return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h2",{className:"text-2xl font-bold text-slate-900 mb-3",children:"Symptom Assessment"}),r.jsx("p",{className:"text-base text-slate-600 leading-relaxed",children:"Select all symptoms that apply and provide additional details for comprehensive evaluation."})]}),Object.entries(ag).map(([e,t])=>(0,r.jsxs)(c.Zb,{className:"symptom-category-card",children:[(0,r.jsxs)(c.Ol,{className:"pb-4",children:[(0,r.jsxs)(c.ll,{className:"text-xl font-semibold text-slate-800",children:[e," Symptoms"]}),r.jsx(c.SZ,{className:"text-slate-600",children:"Select all that apply to the patient's current condition"})]}),r.jsx(c.aY,{children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(t=>{let n=s.selectedSymptoms?.includes(t)||!1;return(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-md hover:bg-white/50 transition-colors duration-200",children:[r.jsx(af,{id:`${e}-${t}`,checked:n,onCheckedChange:e=>a(t,e),className:"symptom-checkbox"}),r.jsx(q,{htmlFor:`${e}-${t}`,className:"symptom-label flex-1",children:t})]},t)})})})]},e)),s.selectedSymptoms&&s.selectedSymptoms.length>0&&(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Symptom Details"}),r.jsx(c.SZ,{children:"Provide additional details for selected symptoms"})]}),r.jsx(c.aY,{className:"space-y-6",children:s.selectedSymptoms.map(e=>(0,r.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[r.jsx("h4",{className:"font-medium text-slate-900",children:e}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Severity"}),(0,r.jsxs)(s8,{value:s.symptomDetails?.[e]?.severity||"",onValueChange:t=>i(e,"severity",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select severity"})}),r.jsx(nq,{children:n4.map(e=>r.jsx(n_,{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Duration"}),(0,r.jsxs)(s8,{value:s.symptomDetails?.[e]?.duration||"",onValueChange:t=>i(e,"duration",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select duration"})}),r.jsx(nq,{children:n5.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Frequency"}),(0,r.jsxs)(s8,{value:s.symptomDetails?.[e]?.frequency||"",onValueChange:t=>i(e,"frequency",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select frequency"})}),r.jsx(nq,{children:n3.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:s.symptomDetails?.[e]?.notes||"",onChange:t=>i(e,"notes",t.target.value),placeholder:"Any additional details about this symptom...",rows:2})]})]},e))})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Additional Symptoms"}),r.jsx(c.SZ,{children:"Describe any other symptoms not listed above"})]}),r.jsx(c.aY,{children:r.jsx(n9,{value:s.additionalSymptoms||"",onChange:e=>n(t=>({...t,additionalSymptoms:e.target.value})),placeholder:"Describe any additional symptoms, their severity, duration, and impact...",rows:4})})]})]})}},{id:"risk",label:"Risk Assessment",icon:D.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{}),a=(e,t)=>{n(s=>({...s,[e]:"true"===t}))},i=(e,t)=>{n(s=>({...s,[e]:t}))},o="high"===s.suicidalRiskLevel||"high"===s.violenceRiskLevel||"high"===s.selfHarmRisk;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Risk Assessment"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Evaluate potential risks for safety and intervention planning."}),o&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[r.jsx(aU,{className:"h-5 w-5 text-red-600"}),r.jsx("span",{className:"text-sm text-red-800 font-medium",children:"High risk factors identified - immediate intervention may be required"})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Suicide Risk Assessment"})}),r.jsx(c.aY,{className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"Suicidal Ideation"}),(0,r.jsxs)(aW,{value:s.suicidalIdeation?.toString()||"",onValueChange:e=>a("suicidalIdeation",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"si-no"}),r.jsx(q,{htmlFor:"si-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"si-yes"}),r.jsx(q,{htmlFor:"si-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"Suicidal Plan"}),(0,r.jsxs)(aW,{value:s.suicidalPlan?.toString()||"",onValueChange:e=>a("suicidalPlan",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"sp-no"}),r.jsx(q,{htmlFor:"sp-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"sp-yes"}),r.jsx(q,{htmlFor:"sp-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"Access to Means"}),(0,r.jsxs)(aW,{value:s.suicidalMeans?.toString()||"",onValueChange:e=>a("suicidalMeans",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"sm-no"}),r.jsx(q,{htmlFor:"sm-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"sm-yes"}),r.jsx(q,{htmlFor:"sm-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"History of Suicide Attempts"}),(0,r.jsxs)(aW,{value:s.suicidalAttemptHistory?.toString()||"",onValueChange:e=>a("suicidalAttemptHistory",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"sah-no"}),r.jsx(q,{htmlFor:"sah-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"sah-yes"}),r.jsx(q,{htmlFor:"sah-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Overall Suicide Risk Level"}),(0,r.jsxs)(s8,{value:s.suicidalRiskLevel||"",onValueChange:e=>i("suicidalRiskLevel",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select risk level"})}),r.jsx(nq,{children:n6.map(e=>r.jsx(n_,{value:e.value,children:e.label},e.value))})]})]})]})})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Violence Risk Assessment"})}),(0,r.jsxs)(c.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"Homicidal Ideation"}),(0,r.jsxs)(aW,{value:s.homicidalIdeation?.toString()||"",onValueChange:e=>a("homicidalIdeation",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"hi-no"}),r.jsx(q,{htmlFor:"hi-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"hi-yes"}),r.jsx(q,{htmlFor:"hi-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"History of Violence"}),(0,r.jsxs)(aW,{value:s.violenceHistory?.toString()||"",onValueChange:e=>a("violenceHistory",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"vh-no"}),r.jsx(q,{htmlFor:"vh-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"vh-yes"}),r.jsx(q,{htmlFor:"vh-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Violence Risk Level"}),(0,r.jsxs)(s8,{value:s.violenceRiskLevel||"",onValueChange:e=>i("violenceRiskLevel",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select risk level"})}),r.jsx(nq,{children:n6.map(e=>r.jsx(n_,{value:e.value,children:e.label},e.value))})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Self-Harm & Substance Use Risk"})}),(0,r.jsxs)(c.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"History of Self-Harm"}),(0,r.jsxs)(aW,{value:s.selfHarmHistory?.toString()||"",onValueChange:e=>a("selfHarmHistory",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"shh-no"}),r.jsx(q,{htmlFor:"shh-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"shh-yes"}),r.jsx(q,{htmlFor:"shh-yes",children:"Yes"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Self-Harm Risk Level"}),(0,r.jsxs)(s8,{value:s.selfHarmRisk||"",onValueChange:e=>i("selfHarmRisk",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select risk level"})}),r.jsx(nq,{children:n6.map(e=>r.jsx(n_,{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Substance Use Risk Level"}),(0,r.jsxs)(s8,{value:s.substanceUseRisk||"",onValueChange:e=>i("substanceUseRisk",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select risk level"})}),r.jsx(nq,{children:n6.map(e=>r.jsx(n_,{value:e.value,children:e.label},e.value))})]})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Risk & Protective Factors"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Risk Factors"}),r.jsx(n9,{value:s.riskFactors||"",onChange:e=>i("riskFactors",e.target.value),placeholder:"Describe specific risk factors (e.g., social isolation, recent losses, substance use, mental illness, access to means, etc.)",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Protective Factors"}),r.jsx(n9,{value:s.protectiveFactors||"",onChange:e=>i("protectiveFactors",e.target.value),placeholder:"Describe protective factors (e.g., social support, coping skills, treatment engagement, religious beliefs, etc.)",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Recommended Interventions"}),r.jsx(n9,{value:s.interventions||"",onChange:e=>i("interventions",e.target.value),placeholder:"Describe recommended interventions and safety planning measures",rows:3})]})]})]})]})}},{id:"history",label:"Medical History",icon:M.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{}),[a,i]=(0,l.useState)(""),o=(e,t)=>{n(s=>({...s,[e]:t}))},d=(0,l.useCallback)(e=>{n(t=>({...t,substanceUseHistory:e}))},[]),u=(0,l.useCallback)(e=>{n(t=>({...t,medicationHistory:e}))},[]),m=(e,t)=>{n(s=>({...s,structuredMedicalConditions:{...s.structuredMedicalConditions,[e]:t}}))},h=e=>{n(t=>({...t,psychiatricEpisodes:t.psychiatricEpisodes?.filter(t=>t.id!==e)||[]}))},p=(e,t,s)=>{n(n=>({...n,psychiatricEpisodes:n.psychiatricEpisodes?.map(n=>n.id===e?{...n,[t]:s}:n)||[]}))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Medical History"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Provide comprehensive medical and psychiatric history information."})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Current Medical Information"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Allergies"}),r.jsx(n9,{value:s.allergies||"",onChange:e=>o("allergies",e.target.value),placeholder:"List any known allergies to medications, foods, or other substances",rows:2})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Surgical History"}),r.jsx(n9,{value:s.surgicalHistory||"",onChange:e=>o("surgicalHistory",e.target.value),placeholder:"List any past surgeries and dates",rows:2})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Medical Conditions"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:a1.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{id:e,checked:s.structuredMedicalConditions?.[e]||!1,onCheckedChange:t=>m(e,t)}),r.jsx(q,{htmlFor:e,className:"text-sm font-normal cursor-pointer",children:e})]},e))}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Other Medical Conditions"}),r.jsx(n9,{value:s.otherMedicalConditions||"",onChange:e=>o("otherMedicalConditions",e.target.value),placeholder:"List any additional medical conditions not covered above",rows:2})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Current Medications"})}),r.jsx(c.aY,{children:r.jsx("div",{className:"space-y-2",children:r.jsx(n9,{value:s.currentMedications||"",onChange:e=>o("currentMedications",e.target.value),placeholder:"List all current medications, dosages, and frequency",rows:3})})})]}),(0,r.jsxs)(c.Zb,{children:[(0,r.jsxs)(c.Ol,{children:[r.jsx(c.ll,{className:"text-lg",children:"Psychiatric History Episodes"}),r.jsx(c.SZ,{children:"Track specific psychiatric episodes with detailed information for comprehensive assessment"})]}),(0,r.jsxs)(c.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4 bg-slate-50",children:[r.jsx("h4",{className:"font-medium mb-3",children:"Add Psychiatric Episode"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Episode Type"}),(0,r.jsxs)(s8,{value:a,onValueChange:i,children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select episode type"})}),r.jsx(nq,{children:a2.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),r.jsx("div",{className:"flex items-end",children:(0,r.jsxs)(P.z,{onClick:()=>{if(!a)return;let e={id:Date.now().toString(),episodeType:a,duration:"",durationUnit:"",startDate:"",endDate:"",severity:"",treatmentReceived:[],treatmentResponse:"",notes:""};n(t=>({...t,psychiatricEpisodes:[...t.psychiatricEpisodes||[],e]})),i("")},disabled:!a,className:"w-full",children:[r.jsx(aq.Z,{className:"h-4 w-4 mr-2"}),"Add Episode"]})})]})]}),s.psychiatricEpisodes&&s.psychiatricEpisodes.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h4",{className:"font-medium",children:"Psychiatric Episodes"}),s.psychiatricEpisodes.map(e=>(0,r.jsxs)(c.Zb,{className:"border-l-4 border-l-purple-500",children:[r.jsx(c.Ol,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(aY.C,{variant:"outline",children:e.episodeType}),e.severity&&r.jsx(aY.C,{variant:"secondary",children:e.severity})]}),r.jsx(P.z,{variant:"ghost",size:"sm",onClick:()=>h(e.id),className:"text-red-600 hover:text-red-800",children:r.jsx(a_,{className:"h-4 w-4"})})]})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Duration"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(z.I,{value:e.duration,onChange:t=>p(e.id,"duration",t.target.value),placeholder:"e.g., 2",type:"number",className:"flex-1"}),(0,r.jsxs)(s8,{value:e.durationUnit,onValueChange:t=>p(e.id,"durationUnit",t),children:[r.jsx(nz,{className:"w-24",children:r.jsx(nt,{placeholder:"Unit"})}),r.jsx(nq,{children:a4.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Start Date (Month/Year)"}),r.jsx(z.I,{value:e.startDate,onChange:t=>p(e.id,"startDate",t.target.value),placeholder:"e.g., 03/2023",type:"month"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"End Date (Month/Year)"}),r.jsx(z.I,{value:e.endDate,onChange:t=>p(e.id,"endDate",t.target.value),placeholder:"Leave empty if ongoing",type:"month"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Severity"}),(0,r.jsxs)(s8,{value:e.severity,onValueChange:t=>p(e.id,"severity",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select severity"})}),r.jsx(nq,{children:a5.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Treatment Response"}),(0,r.jsxs)(s8,{value:e.treatmentResponse,onValueChange:t=>p(e.id,"treatmentResponse",t),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select response"})}),r.jsx(nq,{children:a6.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Treatment Received"}),r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:a3.map(t=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(af,{id:`${e.id}-${t}`,checked:e.treatmentReceived.includes(t),onCheckedChange:s=>{let n=e.treatmentReceived,a=s?[...n,t]:n.filter(e=>e!==t);p(e.id,"treatmentReceived",a)}}),r.jsx(q,{htmlFor:`${e.id}-${t}`,className:"text-sm",children:t})]},t))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Additional Notes"}),r.jsx(n9,{value:e.notes,onChange:t=>p(e.id,"notes",t.target.value),placeholder:"Additional details about the episode, triggers, symptoms, etc.",rows:2})]})]})]},e.id))]}),(!s.psychiatricEpisodes||0===s.psychiatricEpisodes.length)&&(0,r.jsxs)("div",{className:"text-center py-8 text-slate-500",children:[r.jsx("p",{children:"No psychiatric episodes recorded."}),r.jsx("p",{className:"text-sm",children:"Use the form above to add episode information."})]}),(0,r.jsxs)("div",{className:"space-y-2 pt-4 border-t",children:[r.jsx(q,{children:"Family Psychiatric History"}),r.jsx(n9,{value:s.familyPsychiatricHistory||"",onChange:e=>o("familyPsychiatricHistory",e.target.value),placeholder:"Describe any family history of mental illness, suicide, or substance abuse",rows:3})]})]})]}),r.jsx(aX,{data:s.substanceUseHistory||[],onUpdate:d}),r.jsx(a0,{data:s.medicationHistory||[],onUpdate:u})]})}},{id:"mental-status",label:"Mental Status",icon:T.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{}),a=(e,t)=>{n(s=>({...s,[e]:"true"===t}))},i=(e,t)=>{n(s=>({...s,[e]:t}))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Mental Status Examination"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Systematic evaluation of the patient's current mental state."})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Appearance & Behavior"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Appearance"}),(0,r.jsxs)(s8,{value:s.appearance||"",onValueChange:e=>i("appearance",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select appearance"})}),r.jsx(nq,{children:a8.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Behavior"}),(0,r.jsxs)(s8,{value:s.behavior||"",onValueChange:e=>i("behavior",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select behavior"})}),r.jsx(nq,{children:a7.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Attitude"}),r.jsx(n9,{value:s.attitude||"",onChange:e=>i("attitude",e.target.value),placeholder:"Describe patient's attitude toward the interview",rows:2})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Speech"})}),r.jsx(c.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Rate"}),(0,r.jsxs)(s8,{value:s.speechRate||"",onValueChange:e=>i("speechRate",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select rate"})}),r.jsx(nq,{children:a9.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Volume"}),(0,r.jsxs)(s8,{value:s.speechVolume||"",onValueChange:e=>i("speechVolume",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select volume"})}),r.jsx(nq,{children:ie.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Tone"}),r.jsx(n9,{value:s.speechTone||"",onChange:e=>i("speechTone",e.target.value),placeholder:"Describe tone",rows:1})]})]})})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Mood & Affect"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Mood"}),(0,r.jsxs)(s8,{value:s.mood||"",onValueChange:e=>i("mood",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select mood"})}),r.jsx(nq,{children:it.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Affect"}),(0,r.jsxs)(s8,{value:s.affect||"",onValueChange:e=>i("affect",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select affect"})}),r.jsx(nq,{children:is.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Affect Range"}),r.jsx(n9,{value:s.affectRange||"",onChange:e=>i("affectRange",e.target.value),placeholder:"Describe range (e.g., full, restricted)",rows:1})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Affect Intensity"}),r.jsx(n9,{value:s.affectIntensity||"",onChange:e=>i("affectIntensity",e.target.value),placeholder:"Describe intensity",rows:1})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Thought Process & Content"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Thought Process"}),(0,r.jsxs)(s8,{value:s.thoughtProcess||"",onValueChange:e=>i("thoughtProcess",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select thought process"})}),r.jsx(nq,{children:ia.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Thought Content"}),r.jsx(n9,{value:s.thoughtContent||"",onChange:e=>i("thoughtContent",e.target.value),placeholder:"Describe thought content, preoccupations, obsessions",rows:3})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Perceptual Disturbances"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"Hallucinations"}),(0,r.jsxs)(aW,{value:s.hallucinations?.toString()||"",onValueChange:e=>a("hallucinations",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"hall-no"}),r.jsx(q,{htmlFor:"hall-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"hall-yes"}),r.jsx(q,{htmlFor:"hall-yes",children:"Yes"})]})]})]}),s.hallucinations&&(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Type of Hallucinations"}),r.jsx(n9,{value:s.hallucinationType||"",onChange:e=>i("hallucinationType",e.target.value),placeholder:"Describe type (auditory, visual, tactile, etc.)",rows:2})]}),(0,r.jsxs)("div",{children:[r.jsx(q,{className:"text-base font-medium",children:"Delusions"}),(0,r.jsxs)(aW,{value:s.delusions?.toString()||"",onValueChange:e=>a("delusions",e),className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"false",id:"del-no"}),r.jsx(q,{htmlFor:"del-no",children:"No"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(az,{value:"true",id:"del-yes"}),r.jsx(q,{htmlFor:"del-yes",children:"Yes"})]})]})]}),s.delusions&&(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Type of Delusions"}),r.jsx(n9,{value:s.delusionType||"",onChange:e=>i("delusionType",e.target.value),placeholder:"Describe type (paranoid, grandiose, somatic, etc.)",rows:2})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Cognitive Function"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Orientation"}),(0,r.jsxs)(s8,{value:s.orientation||"",onValueChange:e=>i("orientation",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select orientation"})}),r.jsx(nq,{children:ii.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Attention"}),r.jsx(n9,{value:s.attention||"",onChange:e=>i("attention",e.target.value),placeholder:"Describe attention span",rows:1})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Concentration"}),r.jsx(n9,{value:s.concentration||"",onChange:e=>i("concentration",e.target.value),placeholder:"Describe concentration ability",rows:1})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Memory"}),r.jsx(n9,{value:s.memory||"",onChange:e=>i("memory",e.target.value),placeholder:"Describe memory function",rows:1})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Insight & Judgment"})}),r.jsx(c.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Insight"}),(0,r.jsxs)(s8,{value:s.insight||"",onValueChange:e=>i("insight",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select insight level"})}),r.jsx(nq,{children:ir.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Judgment"}),(0,r.jsxs)(s8,{value:s.judgment||"",onValueChange:e=>i("judgment",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select judgment level"})}),r.jsx(nq,{children:il.map(e=>r.jsx(n_,{value:e,children:e},e))})]})]})]})})]})]})}},{id:"diagnosis",label:"Diagnosis",icon:L.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{secondaryDiagnoses:[]}),[a,i]=(0,l.useState)(""),[o,d]=(0,l.useState)(id),u=(e,t)=>{n(s=>({...s,[e]:t}))},m=e=>{n(t=>({...t,primaryDiagnosis:e.name,primaryDiagnosisCode:e.code})),i("")},h=(e,t,s)=>{n(n=>({...n,secondaryDiagnoses:n.secondaryDiagnoses?.map((n,a)=>a===e?{...n,[t]:s}:n)}))},p=e=>{n(t=>({...t,secondaryDiagnoses:t.secondaryDiagnoses?.filter((t,s)=>s!==e)}))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Diagnosis"}),r.jsx("p",{className:"text-sm text-slate-600",children:"Provide diagnostic formulation and treatment recommendations."})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Primary Diagnosis"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Search Diagnoses"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(io.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),r.jsx(z.I,{placeholder:"Search by diagnosis name or ICD-10 code...",value:a,onChange:e=>i(e.target.value),className:"pl-10"})]})]}),a&&r.jsx("div",{className:"border rounded-lg max-h-48 overflow-y-auto",children:o.map((e,t)=>(0,r.jsxs)("div",{className:"p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0",onClick:()=>m(e),children:[r.jsx("div",{className:"font-medium text-sm",children:e.code}),r.jsx("div",{className:"text-sm text-gray-600",children:e.name})]},t))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Primary Diagnosis"}),r.jsx(z.I,{value:s.primaryDiagnosis||"",onChange:e=>u("primaryDiagnosis",e.target.value),placeholder:"Enter primary diagnosis"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"ICD-10 Code"}),r.jsx(z.I,{value:s.primaryDiagnosisCode||"",onChange:e=>u("primaryDiagnosisCode",e.target.value),placeholder:"Enter ICD-10 code"})]})]})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx(c.ll,{className:"text-lg",children:"Secondary Diagnoses"}),(0,r.jsxs)(P.z,{onClick:()=>{n(e=>({...e,secondaryDiagnoses:[...e.secondaryDiagnoses||[],{diagnosis:"",code:"",type:"secondary"}]}))},size:"sm",children:[r.jsx(aq.Z,{className:"h-4 w-4 mr-2"}),"Add Diagnosis"]})]})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[s.secondaryDiagnoses?.map((e,t)=>r.jsxs("div",{className:"border rounded-lg p-4 space-y-4",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("h4",{className:"font-medium",children:["Secondary Diagnosis ",t+1]}),r.jsx(P.z,{onClick:()=>p(t),variant:"outline",size:"sm",children:r.jsx(ic,{className:"h-4 w-4"})})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsx(q,{children:"Diagnosis"}),r.jsx(z.I,{value:e.diagnosis,onChange:e=>h(t,"diagnosis",e.target.value),placeholder:"Enter diagnosis"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(q,{children:"ICD-10 Code"}),r.jsx(z.I,{value:e.code,onChange:e=>h(t,"code",e.target.value),placeholder:"Enter code"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(q,{children:"Type"}),r.jsxs(s8,{value:e.type,onValueChange:e=>h(t,"type",e),children:[r.jsx(nz,{children:r.jsx(nt,{placeholder:"Select type"})}),r.jsx(nq,{children:iu.map(e=>r.jsx(n_,{value:e.value,children:e.label},e.value))})]})]})]})]},t)),(!s.secondaryDiagnoses||0===s.secondaryDiagnoses.length)&&r.jsx("div",{className:"text-center py-8 text-gray-500",children:'No secondary diagnoses added. Click "Add Diagnosis" to add one.'})]})]}),(0,r.jsxs)(c.Zb,{children:[r.jsx(c.Ol,{children:r.jsx(c.ll,{className:"text-lg",children:"Clinical Impression & Recommendations"})}),(0,r.jsxs)(c.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Differential Diagnoses"}),r.jsx(n9,{value:s.differentialDiagnoses||"",onChange:e=>u("differentialDiagnoses",e.target.value),placeholder:"List other diagnoses considered and ruled out",rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Diagnostic Impression"}),r.jsx(n9,{value:s.diagnosticImpression||"",onChange:e=>u("diagnosticImpression",e.target.value),placeholder:"Provide overall diagnostic impression and rationale",rows:4})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(q,{children:"Treatment Recommendations"}),r.jsx(n9,{value:s.treatmentRecommendations||"",onChange:e=>u("treatmentRecommendations",e.target.value),placeholder:"Provide treatment recommendations including medications, therapy, follow-up care",rows:4})]})]})]})]})}},{id:"laboratory-tests",label:"Laboratory and Assessment Tests",icon:O.Z,component:function({data:e,onUpdate:t}){let[s,n]=(0,l.useState)(()=>e||{bloodWork:{},toxicologyScreen:{},psychologicalTests:{psychologicalAssessments:{}},imagingAndNeurological:{imagingStudies:{},neurologicalTests:{}},selectedTests:{},bloodTestComponents:{},bloodTestNotes:{},psychologicalAssessments:{},imagingStudies:{},neurologicalTests:{}});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h2",{className:"text-2xl font-bold text-slate-900 mb-3",children:"Laboratory Tests & Medical Screening"}),r.jsx("p",{className:"text-base text-slate-600 leading-relaxed",children:"Comprehensive laboratory testing with clinical-grade documentation and interpretation."})]}),(0,r.jsxs)(d.mQ,{defaultValue:"bloodwork",className:"w-full",children:[(0,r.jsxs)(d.dr,{className:"grid w-full grid-cols-4",children:[(0,r.jsxs)(d.SP,{value:"bloodwork",className:"flex items-center gap-2",children:[r.jsx(O.Z,{className:"h-4 w-4"}),"Blood Work"]}),(0,r.jsxs)(d.SP,{value:"toxicology",className:"flex items-center gap-2",children:[r.jsx(im,{className:"h-4 w-4"}),"Toxicology"]}),(0,r.jsxs)(d.SP,{value:"psychological",className:"flex items-center gap-2",children:[r.jsx(R.Z,{className:"h-4 w-4"}),"Psychological"]}),(0,r.jsxs)(d.SP,{value:"imaging",className:"flex items-center gap-2",children:[r.jsx(ih,{className:"h-4 w-4"}),"Imaging & Other"]})]}),r.jsx(d.nU,{value:"bloodwork",className:"mt-6",children:r.jsx(iR,{data:s.bloodWork||{},onUpdate:e=>{n(t=>({...t,bloodWork:e}))}})}),r.jsx(d.nU,{value:"toxicology",className:"mt-6",children:r.jsx(iM,{data:s.toxicologyScreen||{},onUpdate:e=>{n(t=>({...t,toxicologyScreen:e}))}})}),r.jsx(d.nU,{value:"psychological",className:"mt-6",children:r.jsx(iO,{data:s.psychologicalTests||{psychologicalAssessments:{}},onUpdate:e=>{n(t=>({...t,psychologicalTests:e}))}})}),r.jsx(d.nU,{value:"imaging",className:"mt-6",children:r.jsx(iV,{data:s.imagingAndNeurological||{imagingStudies:{},neurologicalTests:{}},onUpdate:e=>{n(t=>({...t,imagingAndNeurological:e}))}})})]})]})}}];function iZ(){let e=(0,o.useSearchParams)(),t=(0,o.useRouter)(),s=e.get("id"),[n,a]=(0,l.useState)("demographics"),[i,u]=(0,l.useState)({demographics:{},symptoms:{},riskAssessment:{},medicalHistory:{},mentalStatusExam:{},diagnosis:{},laboratoryTests:{}}),[m,h]=(0,l.useState)(new Set),[p,x]=(0,l.useState)(null),[f,g]=(0,l.useState)(!1),[v,y]=(0,l.useState)(!1),[j,b]=(0,l.useState)(!1),[N,w]=(0,l.useState)(null),[C,S]=(0,l.useState)(s),k=m.size/iB.length*100,A=(0,l.useRef)(null),R=(0,l.useCallback)(async(e,t,s)=>{g(!0);try{let n={data:e,completedSections:Array.from(t),lastSaved:new Date().toISOString(),assessmentId:s};localStorage.setItem("psychiatric-assessment-data",JSON.stringify(n));let a={...e,assessmentId:s},i=await fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok)throw Error("Failed to save to database");let r=await i.json();!s&&r.assessmentId&&S(r.assessmentId),x(new Date),w(null)}catch(e){console.error("Error saving data:",e),w(e instanceof Error?e.message:"Failed to save assessment"),x(new Date)}finally{g(!1)}},[]),D=(0,l.useCallback)(()=>{u({demographics:{},symptoms:{},riskAssessment:{},medicalHistory:{},mentalStatusExam:{},diagnosis:{},laboratoryTests:{}}),h(new Set),x(null),S(null),w(null),localStorage.removeItem("psychiatric-assessment-data")},[]),M=(0,l.useCallback)((e,t)=>{u(s=>({...s,[e]:t})),t&&Object.keys(t).length>0&&h(t=>new Set(Array.from(t).concat(e)))},[]);(0,l.useCallback)(async()=>{await R(i,m,C)},[R,i,m,C]),(0,l.useCallback)(()=>{A.current&&clearTimeout(A.current),window.location.href="/assessment?new=true"},[]);let T=(0,l.useMemo)(()=>{let e={};return iB.forEach(t=>{e[t.id]=e=>M(t.id,e)}),e},[M]),L=async e=>{try{let t=await fetch(`/api/export?format=${e}`);if(!t.ok)throw Error("Failed to export data");let s=await t.blob(),n=URL.createObjectURL(s),a=document.createElement("a");a.href=n,a.download=`psychiatric-assessments-${new Date().toISOString().split("T")[0]}.${e}`,a.click(),URL.revokeObjectURL(n)}catch(s){console.error("Error exporting data:",s);let t={...i,metadata:{exportDate:new Date().toISOString(),completedSections:Array.from(m),progress:k}};if("json"===e){let e=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),s=URL.createObjectURL(e),n=document.createElement("a");n.href=s,n.download=`psychiatric-assessment-${new Date().toISOString().split("T")[0]}.json`,n.click(),URL.revokeObjectURL(s)}}},O=async()=>{y(!0),w(null);try{A.current&&clearTimeout(A.current);let e={...i,assessmentId:C,status:"completed"},s=await fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.error||"Failed to complete assessment")}localStorage.removeItem("psychiatric-assessment-data"),D(),t.push("/patients")}catch(t){console.error("Error completing assessment:",t);let e=t instanceof Error?t.message:"Failed to complete assessment";w(e),alert(`Error: ${e}. Please try again.`)}finally{y(!1)}};return r.jsx("div",{className:"assessment-page-container",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[r.jsx("div",{className:"assessment-header-enhanced fade-in",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[r.jsx(W.default,{href:"/patients",children:(0,r.jsxs)(P.z,{variant:"outline",size:"sm",className:"button-modern-secondary",children:[r.jsx(I.Z,{className:"h-4 w-4 mr-2"}),"Back to Patients"]})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"assessment-title-enhanced mb-2",children:s?"Edit Assessment":"New Assessment"}),r.jsx("p",{className:"assessment-subtitle-enhanced",children:s?"Continue or modify existing assessment":"Complete all sections for comprehensive evaluation"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"autosave-indicator",children:f?(0,r.jsxs)(r.Fragment,{children:[r.jsx(H,{className:"h-4 w-4 animate-spin"}),r.jsx("span",{children:"Saving..."})]}):N?(0,r.jsxs)(r.Fragment,{children:[r.jsx(V.Z,{className:"h-4 w-4 text-red-500"}),r.jsx("span",{className:"text-red-600",children:N})]}):p?(0,r.jsxs)(r.Fragment,{children:[r.jsx(B.Z,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("span",{children:["Saved ",p.toLocaleTimeString()]})]}):null}),r.jsx(P.z,{onClick:O,disabled:v||k<100,className:"button-modern-primary",children:v?(0,r.jsxs)(r.Fragment,{children:[r.jsx(H,{className:"h-5 w-5 animate-spin mr-3"}),"Completing..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(B.Z,{className:"h-5 w-5 mr-3"}),"Save & Complete"]})}),(0,r.jsxs)(P.z,{variant:"outline",size:"sm",onClick:()=>L("json"),className:"button-modern-secondary",children:[r.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"Export JSON"]}),(0,r.jsxs)(P.z,{variant:"outline",size:"sm",onClick:()=>L("csv"),className:"hover:bg-slate-50 border-slate-300 hover:border-slate-400 transition-all duration-200",children:[r.jsx(Z.Z,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]})]})}),(0,r.jsxs)(c.Zb,{className:"mb-8 progress-card-enhanced slide-in-right",children:[r.jsx(c.Ol,{className:"pb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx(c.ll,{className:"text-2xl font-bold text-slate-800",children:"Assessment Progress"}),(0,r.jsxs)("span",{className:"badge-modern badge-info",children:[m.size," of ",iB.length," sections completed"]})]})}),r.jsx(c.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(E,{value:k,className:"w-full h-4 mb-4"}),(0,r.jsxs)("div",{className:"flex justify-between text-sm font-semibold text-slate-600",children:[r.jsx("span",{children:"0%"}),(0,r.jsxs)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:[Math.round(k),"% Complete"]}),r.jsx("span",{children:"100%"})]})]})})]}),r.jsx(c.Zb,{className:"card-modern fade-in",children:r.jsx(c.aY,{className:"p-0",children:(0,r.jsxs)(d.mQ,{value:n,onValueChange:a,className:"w-full",children:[r.jsx("div",{className:"border-b border-slate-200/50 bg-gradient-to-r from-slate-50 to-blue-50/30",children:r.jsx(d.dr,{className:"grid w-full grid-cols-7 h-auto p-3 bg-transparent",children:iB.map(e=>{let t=e.icon,s=m.has(e.id);return(0,r.jsxs)(d.SP,{value:e.id,className:"section-tab-enhanced flex flex-col items-center space-y-3 p-5 rounded-xl mx-1 transition-all duration-300 hover:bg-white/50 data-[state=active]:section-tab-active",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(t,{className:"h-6 w-6"}),s&&r.jsx(B.Z,{className:"h-5 w-5 text-emerald-500 pulse-glow"})]}),r.jsx("span",{className:"text-sm font-bold leading-tight text-center",children:e.label})]},e.id)})})}),iB.map(e=>{let t=e.component;return r.jsx(d.nU,{value:e.id,className:"form-section-modern fade-in",children:r.jsx(t,{data:i[e.id],onUpdate:T[e.id]})},e.id)})]})})})]})})}function iW(){return r.jsx(l.Suspense,{fallback:r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:"Loading assessment..."}),children:r.jsx(iZ,{})})}},8443:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var n=s(326);s(7577);var a=s(9360),i=s(1223);let r=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return n.jsx("div",{className:(0,i.cn)(r({variant:t}),e),...s})}},1664:(e,t,s)=>{"use strict";s.d(t,{z:()=>c});var n=s(326),a=s(7577),i=s(4214),r=s(9360),l=s(1223);let o=(0,r.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...r},c)=>{let d=a?i.g7:"button";return n.jsx(d,{className:(0,l.cn)(o({variant:t,size:s,className:e})),ref:c,...r})});c.displayName="Button"},9752:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>r,aY:()=>d,ll:()=>o});var n=s(326),a=s(7577),i=s(1223);let r=a.forwardRef(({className:e,...t},s)=>n.jsx("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));r.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>n.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},s)=>n.jsx("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},s)=>n.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>n.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>n.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},1190:(e,t,s)=>{"use strict";s.d(t,{I:()=>r});var n=s(326),a=s(7577),i=s(1223);let r=a.forwardRef(({className:e,type:t,...s},a)=>n.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));r.displayName="Input"},384:(e,t,s)=>{"use strict";s.d(t,{SP:()=>c,dr:()=>o,mQ:()=>l,nU:()=>d});var n=s(326),a=s(7577),i=s(8407),r=s(1223);let l=i.fC,o=a.forwardRef(({className:e,...t},s)=>n.jsx(i.aV,{ref:s,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));o.displayName=i.aV.displayName;let c=a.forwardRef(({className:e,...t},s)=>n.jsx(i.xz,{ref:s,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=i.xz.displayName;let d=a.forwardRef(({className:e,...t},s)=>n.jsx(i.VY,{ref:s,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=i.VY.displayName},1223:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var n=s(1135),a=s(1009);function i(...e){return(0,a.m6)((0,n.W)(e))}},8200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n=(0,s(8570).createProxy)(String.raw`C:\Users\<USER>\projects\psychiatric-assessment\src\app\assessment\page.tsx#default`)},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>r});var n=s(9510),a=s(5384),i=s.n(a);s(5023);let r={title:"Psychiatric Assessment System",description:"Fast and reliable psychiatric assessment system optimized for ML training data collection"};function l({children:e}){return n.jsx("html",{lang:"en",children:n.jsx("body",{className:i().className,children:n.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:e})})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[276,349,566,503],()=>s(4528));module.exports=n})();