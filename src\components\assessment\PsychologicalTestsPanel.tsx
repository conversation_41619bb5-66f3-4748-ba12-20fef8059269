"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Brain, 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  Trash2, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  Calculator
} from "lucide-react"

// Common psychological assessment instruments
const PSYCHOLOGICAL_TESTS = [
  {
    name: "PHQ-9",
    fullName: "Patient Health Questionnaire-9",
    category: "Depression",
    description: "9-item depression screening tool",
    scoringRange: "0-27",
    interpretation: {
      "0-4": "Minimal depression",
      "5-9": "Mild depression", 
      "10-14": "Moderate depression",
      "15-19": "Moderately severe depression",
      "20-27": "Severe depression"
    }
  },
  {
    name: "GAD-7",
    fullName: "Generalized Anxiety Disorder 7-item",
    category: "Anxiety",
    description: "7-item anxiety screening tool",
    scoringRange: "0-21",
    interpretation: {
      "0-4": "Minimal anxiety",
      "5-9": "Mild anxiety",
      "10-14": "Moderate anxiety", 
      "15-21": "Severe anxiety"
    }
  },
  {
    name: "MMSE",
    fullName: "Mini-Mental State Examination",
    category: "Cognitive",
    description: "Cognitive function screening",
    scoringRange: "0-30",
    interpretation: {
      "24-30": "Normal cognition",
      "18-23": "Mild cognitive impairment",
      "0-17": "Severe cognitive impairment"
    }
  },
  {
    name: "Beck Depression Inventory",
    fullName: "Beck Depression Inventory (BDI-II)",
    category: "Depression",
    description: "21-item depression assessment",
    scoringRange: "0-63",
    interpretation: {
      "0-13": "Minimal depression",
      "14-19": "Mild depression",
      "20-28": "Moderate depression",
      "29-63": "Severe depression"
    }
  },
  {
    name: "Beck Anxiety Inventory",
    fullName: "Beck Anxiety Inventory (BAI)",
    category: "Anxiety", 
    description: "21-item anxiety assessment",
    scoringRange: "0-63",
    interpretation: {
      "0-7": "Minimal anxiety",
      "8-15": "Mild anxiety",
      "16-25": "Moderate anxiety",
      "26-63": "Severe anxiety"
    }
  },
  {
    name: "DASS-21",
    fullName: "Depression, Anxiety and Stress Scale",
    category: "Multi-domain",
    description: "21-item assessment of depression, anxiety, and stress",
    scoringRange: "0-63 (each subscale 0-21)",
    interpretation: {
      "Depression": "Normal: 0-4, Mild: 5-6, Moderate: 7-10, Severe: 11-13, Extremely severe: 14+",
      "Anxiety": "Normal: 0-3, Mild: 4-5, Moderate: 6-7, Severe: 8-9, Extremely severe: 10+", 
      "Stress": "Normal: 0-7, Mild: 8-9, Moderate: 10-12, Severe: 13-16, Extremely severe: 17+"
    }
  },
  {
    name: "MOCA",
    fullName: "Montreal Cognitive Assessment",
    category: "Cognitive",
    description: "Cognitive screening for mild cognitive impairment",
    scoringRange: "0-30",
    interpretation: {
      "26-30": "Normal cognition",
      "18-25": "Mild cognitive impairment",
      "0-17": "Moderate to severe cognitive impairment"
    }
  },
  {
    name: "Y-BOCS",
    fullName: "Yale-Brown Obsessive Compulsive Scale",
    category: "OCD",
    description: "10-item OCD severity assessment",
    scoringRange: "0-40",
    interpretation: {
      "0-7": "Subclinical",
      "8-15": "Mild",
      "16-23": "Moderate",
      "24-31": "Severe",
      "32-40": "Extreme"
    }
  }
]

interface PsychologicalTest {
  id?: string
  testName: string
  datePerformed?: string
  totalScore?: number
  score?: string
  interpretation?: string
  notes?: string
  responses?: string
  subscaleScores?: string
  isCompleted: boolean
}

interface PsychologicalTestsData {
  psychologicalAssessments: Record<string, PsychologicalTest>
}

interface PsychologicalTestsPanelProps {
  data: PsychologicalTestsData
  onUpdate: (data: PsychologicalTestsData) => void
}

export default function PsychologicalTestsPanel({ data, onUpdate }: PsychologicalTestsPanelProps) {
  const [formData, setFormData] = useState<PsychologicalTestsData>(() => data || {
    psychologicalAssessments: {}
  })
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  // Update formData when data prop changes
  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      setFormData(data)
    }
  }, [data])

  // Notify parent of changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onUpdate(formData)
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [formData, onUpdate])

  const updateTest = (testName: string, field: keyof PsychologicalTest, value: any) => {
    setFormData(prev => ({
      ...prev,
      psychologicalAssessments: {
        ...prev.psychologicalAssessments,
        [testName]: {
          ...prev.psychologicalAssessments[testName],
          testName,
          [field]: value
        }
      }
    }))
  }

  const addTest = (testName: string) => {
    const testInfo = PSYCHOLOGICAL_TESTS.find(t => t.name === testName)
    setFormData(prev => ({
      ...prev,
      psychologicalAssessments: {
        ...prev.psychologicalAssessments,
        [testName]: {
          testName,
          isCompleted: false,
          datePerformed: new Date().toISOString().split('T')[0]
        }
      }
    }))
    setOpenSections(prev => ({ ...prev, [testName]: true }))
  }

  const removeTest = (testName: string) => {
    setFormData(prev => {
      const newAssessments = { ...prev.psychologicalAssessments }
      delete newAssessments[testName]
      return {
        ...prev,
        psychologicalAssessments: newAssessments
      }
    })
    setOpenSections(prev => {
      const newSections = { ...prev }
      delete newSections[testName]
      return newSections
    })
  }

  const toggleSection = (testName: string) => {
    setOpenSections(prev => ({
      ...prev,
      [testName]: !prev[testName]
    }))
  }

  const getInterpretation = (testName: string, score: number): string => {
    const test = PSYCHOLOGICAL_TESTS.find(t => t.name === testName)
    if (!test || !test.interpretation) return ""
    
    for (const [range, interpretation] of Object.entries(test.interpretation)) {
      if (range.includes('-')) {
        const [min, max] = range.split('-').map(Number)
        if (score >= min && score <= max) return interpretation
      } else if (range.includes('+')) {
        const min = parseInt(range.replace('+', ''))
        if (score >= min) return interpretation
      }
    }
    return ""
  }

  const getSeverityColor = (interpretation: string): string => {
    const lower = interpretation.toLowerCase()
    if (lower.includes('severe') || lower.includes('extreme')) return "text-red-600"
    if (lower.includes('moderate')) return "text-orange-600"
    if (lower.includes('mild')) return "text-yellow-600"
    if (lower.includes('minimal') || lower.includes('normal')) return "text-green-600"
    return "text-gray-600"
  }

  const availableTests = PSYCHOLOGICAL_TESTS.filter(test => 
    !formData.psychologicalAssessments[test.name]
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold text-slate-900 mb-2">Psychological Assessment Tools</h3>
          <p className="text-sm text-slate-600">Standardized psychological testing and assessment instruments</p>
        </div>
        
        {availableTests.length > 0 && (
          <Select onValueChange={addTest}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Add assessment tool..." />
            </SelectTrigger>
            <SelectContent>
              {availableTests.map(test => (
                <SelectItem key={test.name} value={test.name}>
                  <div className="flex flex-col">
                    <span className="font-medium">{test.name}</span>
                    <span className="text-xs text-slate-500">{test.fullName}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {Object.keys(formData.psychologicalAssessments).length === 0 && (
        <Card>
          <CardContent className="text-center py-8 text-slate-500">
            <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No psychological assessments added yet.</p>
            <p className="text-sm mt-2">Use the dropdown above to add standardized assessment tools.</p>
          </CardContent>
        </Card>
      )}

      {Object.entries(formData.psychologicalAssessments).map(([testName, testData]) => {
        const testInfo = PSYCHOLOGICAL_TESTS.find(t => t.name === testName)
        const interpretation = testData.totalScore ? getInterpretation(testName, testData.totalScore) : ""
        
        return (
          <Card key={testName}>
            <Collapsible open={openSections[testName]} onOpenChange={() => toggleSection(testName)}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div>
                        <CardTitle className="text-lg flex items-center gap-2">
                          {testName}
                          {testData.isCompleted && <CheckCircle className="h-5 w-5 text-green-500" />}
                        </CardTitle>
                        <CardDescription>
                          {testInfo?.fullName} • {testInfo?.category}
                          {testData.totalScore && (
                            <span className="ml-2">
                              Score: {testData.totalScore} 
                              {interpretation && (
                                <span className={`ml-1 font-medium ${getSeverityColor(interpretation)}`}>
                                  ({interpretation})
                                </span>
                              )}
                            </span>
                          )}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          removeTest(testName)
                        }}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      {openSections[testName] ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Date Performed</Label>
                      <Input
                        type="date"
                        value={testData.datePerformed || ''}
                        onChange={(e) => updateTest(testName, 'datePerformed', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Total Score</Label>
                      <Input
                        type="number"
                        value={testData.totalScore || ''}
                        onChange={(e) => updateTest(testName, 'totalScore', parseInt(e.target.value) || 0)}
                        placeholder={`Range: ${testInfo?.scoringRange || 'N/A'}`}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Completion Status</Label>
                      <div className="flex items-center space-x-2 pt-2">
                        <Checkbox
                          checked={testData.isCompleted}
                          onCheckedChange={(checked) => updateTest(testName, 'isCompleted', checked)}
                        />
                        <span className="text-sm">Assessment completed</span>
                      </div>
                    </div>
                  </div>

                  {interpretation && (
                    <div className="p-3 bg-slate-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Calculator className="h-4 w-4 text-slate-600" />
                        <span className="text-sm font-medium">Interpretation:</span>
                        <span className={`text-sm font-medium ${getSeverityColor(interpretation)}`}>
                          {interpretation}
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label>Clinical Interpretation</Label>
                    <Textarea
                      value={testData.interpretation || ''}
                      onChange={(e) => updateTest(testName, 'interpretation', e.target.value)}
                      placeholder="Enter clinical interpretation and context"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Notes</Label>
                    <Textarea
                      value={testData.notes || ''}
                      onChange={(e) => updateTest(testName, 'notes', e.target.value)}
                      placeholder="Additional notes, observations, or context"
                      rows={2}
                    />
                  </div>

                  {testInfo && (
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="text-sm">
                        <div className="font-medium text-blue-900 mb-1">Assessment Information</div>
                        <div className="text-blue-700">
                          <div>{testInfo.description}</div>
                          <div className="mt-1">Scoring Range: {testInfo.scoringRange}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        )
      })}
    </div>
  )
}

export type { PsychologicalTestsData }
