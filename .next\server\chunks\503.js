"use strict";exports.id=503,exports.ids=[503],exports.modules={6464:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},8360:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]])},4659:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8319:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},165:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},2978:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},4061:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},3634:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(6557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},2561:(e,t,r)=>{r.d(t,{M:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},545:(e,t,r)=>{r.d(t,{B:()=>u});var n=r(7577),o=r(3095),a=r(8051),i=r(4214),l=r(326);function u(e){let t=e+"CollectionProvider",[r,u]=(0,o.b)(t),[s,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,l.jsx)(s,{scope:t,itemMap:a,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,i.Z8)(f),m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),i=(0,a.e)(t,o.collectionRef);return(0,l.jsx)(p,{ref:i,children:n})});m.displayName=f;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=(0,i.Z8)(v),b=n.forwardRef((e,t)=>{let{scope:r,children:o,...i}=e,u=n.useRef(null),s=(0,a.e)(t,u),d=c(v,r);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...i}),()=>void d.itemMap.delete(u))),(0,l.jsx)(h,{[y]:"",ref:s,children:o})});return b.displayName=v,[{Provider:d,Slot:m,ItemSlot:b},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}},3095:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(7577),o=r(326);function a(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let i=n.createContext(a),l=r.length;r=[...r,a];let u=t=>{let{scope:r,children:a,...u}=t,s=r?.[e]?.[l]||i,c=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:a})};return u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e]?.[l]||i,s=n.useContext(u);if(s)return s;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},7124:(e,t,r)=>{r.d(t,{gm:()=>a});var n=r(7577);r(326);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},8957:(e,t,r)=>{r.d(t,{M:()=>u});var n,o=r(7577),a=r(5819),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,r]=o.useState(i());return(0,a.b)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},9815:(e,t,r)=>{r.d(t,{z:()=>i});var n=r(7577),o=r(8051),a=r(5819),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),u=n.useRef(null),s=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=u.current,r=s.current;if(r!==e){let n=c.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=l(u.current).includes(r.animationName);if(r.target===o&&n&&(f("ANIMATION_END"),!s.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(t),u="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),s=(0,o.e)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||i.isPresent?n.cloneElement(u,{ref:s}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},5226:(e,t,r)=>{r.d(t,{WV:()=>l,jH:()=>u});var n=r(7577),o=r(962),a=r(4214),i=r(326),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e,l=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5594:(e,t,r)=>{r.d(t,{Pc:()=>g,ck:()=>S,fC:()=>E});var n=r(7577),o=r(2561),a=r(545),i=r(8051),l=r(3095),u=r(8957),s=r(5226),c=r(5049),d=r(2067),f=r(7124),p=r(326),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[h,b,M]=(0,a.B)(y),[w,g]=(0,l.b)(y,[M]),[x,N]=w(y),R=n.forwardRef((e,t)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(k,{...e,ref:t})})}));R.displayName=y;var k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:u,currentTabStopId:h,defaultCurrentTabStopId:M,onCurrentTabStopIdChange:w,onEntryFocus:g,preventScrollOnEntryFocus:N=!1,...R}=e,k=n.useRef(null),T=(0,i.e)(t,k),C=(0,f.gm)(u),[A,E]=(0,d.T)({prop:h,defaultProp:M??null,onChange:w,caller:y}),[S,D]=n.useState(!1),j=(0,c.W)(g),F=b(r),P=n.useRef(!1),[Z,O]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(m,j),()=>e.removeEventListener(m,j)},[j]),(0,p.jsx)(x,{scope:r,orientation:a,dir:C,loop:l,currentTabStopId:A,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>O(e=>e-1),[]),children:(0,p.jsx)(s.WV.div,{tabIndex:S||0===Z?-1:0,"data-orientation":a,...R,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),N)}}P.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>D(!1))})})}),T="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:c,...d}=e,f=(0,u.M)(),m=l||f,v=N(T,r),y=v.currentTabStopId===m,M=b(r),{onFocusableItemAdd:w,onFocusableItemRemove:g,currentTabStopId:x}=v;return n.useEffect(()=>{if(a)return w(),()=>g()},[a,w,g]),(0,p.jsx)(h.ItemSlot,{scope:r,id:m,focusable:a,active:i,children:(0,p.jsx)(s.WV.span,{tabIndex:y?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=M().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>I(r))}}),children:"function"==typeof c?c({isCurrentTabStop:y,hasTabStop:null!=x}):c})})});C.displayName=T;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var E=R,S=C},8407:(e,t,r)=>{r.d(t,{VY:()=>S,aV:()=>I,fC:()=>A,xz:()=>E});var n=r(7577),o=r(2561),a=r(3095),i=r(5594),l=r(9815),u=r(5226),s=r(7124),c=r(2067),d=r(8957),f=r(326),p="Tabs",[m,v]=(0,a.b)(p,[i.Pc]),y=(0,i.Pc)(),[h,b]=m(p),M=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:m="automatic",...v}=e,y=(0,s.gm)(l),[b,M]=(0,c.T)({prop:n,onChange:o,defaultProp:a??"",caller:p});return(0,f.jsx)(h,{scope:r,baseId:(0,d.M)(),value:b,onValueChange:M,orientation:i,dir:y,activationMode:m,children:(0,f.jsx)(u.WV.div,{dir:y,"data-orientation":i,...v,ref:t})})});M.displayName=p;var w="TabsList",g=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=b(w,r),l=y(r);return(0,f.jsx)(i.fC,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});g.displayName=w;var x="TabsTrigger",N=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...l}=e,s=b(x,r),c=y(r),d=T(s.baseId,n),p=C(s.baseId,n),m=n===s.value;return(0,f.jsx)(i.ck,{asChild:!0,...c,focusable:!a,active:m,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...l,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(n)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(n)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==s.activationMode;m||a||!e||s.onValueChange(n)})})})});N.displayName=x;var R="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...s}=e,c=b(R,r),d=T(c.baseId,o),p=C(c.baseId,o),m=o===c.value,v=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.z,{present:a||m,children:({present:r})=>(0,f.jsx)(u.WV.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:p,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&i})})});function T(e,t){return`${e}-trigger-${t}`}function C(e,t){return`${e}-content-${t}`}k.displayName=R;var A=M,I=g,E=N,S=k},5049:(e,t,r)=>{r.d(t,{W:()=>o});var n=r(7577);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},2067:(e,t,r)=>{r.d(t,{T:()=>l});var n,o=r(7577),a=r(5819),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.b;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,l,u]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{a.current!==r&&(l.current?.(r),a.current=r)},[r,a]),[r,n,l]}({defaultProp:t,onChange:r}),s=void 0!==e,c=s?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[c,o.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&u.current?.(r)}else l(t)},[s,e,l,u])]}Symbol("RADIX:SYNC_STATE")},5819:(e,t,r)=>{r.d(t,{b:()=>o});var n=r(7577),o=globalThis?.document?n.useLayoutEffect:()=>{}}};