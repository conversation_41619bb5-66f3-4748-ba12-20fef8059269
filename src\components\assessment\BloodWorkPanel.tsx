"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronRight, AlertTriangle, CheckCircle } from "lucide-react"

interface BloodWorkData {
  thyroidFunction?: {
    datePerformed?: string
    tsh?: string
    freeT3?: string
    freeT4?: string
    reverseT3?: string
    interpretation?: string
    notes?: string
  }
  vitaminLevels?: {
    datePerformed?: string
    vitaminB12?: string
    vitaminD3?: string
    folate?: string
    thiamine?: string
    vitaminB6?: string
    interpretation?: string
    notes?: string
  }
  liverFunction?: {
    datePerformed?: string
    alt?: string
    ast?: string
    totalBilirubin?: string
    directBilirubin?: string
    alkalinePhosphatase?: string
    ggt?: string
    albumin?: string
    ptInr?: string
    interpretation?: string
    notes?: string
  }
  completeBloodCount?: {
    datePerformed?: string
    hemoglobin?: string
    hematocrit?: string
    wbc?: string
    neutrophils?: string
    lymphocytes?: string
    monocytes?: string
    eosinophils?: string
    basophils?: string
    platelets?: string
    mcv?: string
    mch?: string
    mchc?: string
    interpretation?: string
    notes?: string
  }
  metabolicPanel?: {
    datePerformed?: string
    glucose?: string
    hba1c?: string
    creatinine?: string
    bun?: string
    egfr?: string
    sodium?: string
    potassium?: string
    chloride?: string
    co2?: string
    anionGap?: string
    interpretation?: string
    notes?: string
  }
  lipidPanel?: {
    datePerformed?: string
    totalCholesterol?: string
    ldl?: string
    hdl?: string
    triglycerides?: string
    interpretation?: string
    notes?: string
  }
  inflammatoryMarkers?: {
    datePerformed?: string
    esr?: string
    crp?: string
    interpretation?: string
    notes?: string
  }
  hormonalTests?: {
    datePerformed?: string
    cortisol?: string
    testosterone?: string
    prolactin?: string
    interpretation?: string
    notes?: string
  }
}

interface BloodWorkPanelProps {
  data: BloodWorkData
  onUpdate: (data: BloodWorkData) => void
}

export default function BloodWorkPanel({ data, onUpdate }: BloodWorkPanelProps) {
  const [formData, setFormData] = useState<BloodWorkData>(() => data || {})
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    thyroid: false,
    vitamins: false,
    liver: false,
    cbc: false,
    metabolic: false,
    lipid: false,
    inflammatory: false,
    hormonal: false
  })

  // Update formData when data prop changes
  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      setFormData(data)
    }
  }, [data])

  // Notify parent of changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onUpdate(formData)
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [formData, onUpdate])

  const updateSection = (section: keyof BloodWorkData, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
  }

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const isAbnormal = (value: string, normalRange: string): boolean => {
    if (!value || !normalRange) return false
    const numValue = parseFloat(value)
    if (isNaN(numValue)) return false
    
    // Parse normal range (e.g., "0.4-4.0", "<200", ">40")
    if (normalRange.includes('-')) {
      const [min, max] = normalRange.split('-').map(v => parseFloat(v))
      return numValue < min || numValue > max
    } else if (normalRange.startsWith('<')) {
      const max = parseFloat(normalRange.substring(1))
      return numValue >= max
    } else if (normalRange.startsWith('>')) {
      const min = parseFloat(normalRange.substring(1))
      return numValue <= min
    }
    return false
  }

  const renderTestField = (
    section: keyof BloodWorkData,
    field: string,
    label: string,
    unit: string,
    normalRange: string,
    value?: string
  ) => {
    const abnormal = isAbnormal(value || '', normalRange)
    
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">{label}</Label>
          {abnormal && <AlertTriangle className="h-4 w-4 text-red-500" />}
          {value && !abnormal && <CheckCircle className="h-4 w-4 text-green-500" />}
        </div>
        <div className="flex items-center space-x-2">
          <Input
            value={value || ''}
            onChange={(e) => updateSection(section, field, e.target.value)}
            placeholder="Enter value"
            className={abnormal ? "border-red-300 focus:border-red-500" : ""}
          />
          <Badge variant="outline" className="text-xs whitespace-nowrap">
            {unit}
          </Badge>
        </div>
        <p className="text-xs text-gray-500">Normal: {normalRange} {unit}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-xl font-bold text-slate-900 mb-2">Comprehensive Blood Work Panel</h3>
        <p className="text-sm text-slate-600">Enter laboratory test results with automatic abnormal value detection</p>
      </div>

      {/* Complete Blood Count */}
      <Card>
        <Collapsible open={openSections.cbc} onOpenChange={() => toggleSection('cbc')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Complete Blood Count (CBC)</CardTitle>
                  <CardDescription>Hemoglobin, Hematocrit, WBC with differential, Platelets, RBC indices</CardDescription>
                </div>
                {openSections.cbc ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Performed</Label>
                  <Input
                    type="date"
                    value={formData.completeBloodCount?.datePerformed || ''}
                    onChange={(e) => updateSection('completeBloodCount', 'datePerformed', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-3">Basic Parameters</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {renderTestField('completeBloodCount', 'hemoglobin', 'Hemoglobin', 'g/dL', '12.0-16.0', formData.completeBloodCount?.hemoglobin)}
                    {renderTestField('completeBloodCount', 'hematocrit', 'Hematocrit', '%', '36-46', formData.completeBloodCount?.hematocrit)}
                    {renderTestField('completeBloodCount', 'wbc', 'WBC Count', 'K/uL', '4.5-11.0', formData.completeBloodCount?.wbc)}
                    {renderTestField('completeBloodCount', 'platelets', 'Platelets', 'K/uL', '150-450', formData.completeBloodCount?.platelets)}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-3">WBC Differential</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    {renderTestField('completeBloodCount', 'neutrophils', 'Neutrophils', '%', '50-70', formData.completeBloodCount?.neutrophils)}
                    {renderTestField('completeBloodCount', 'lymphocytes', 'Lymphocytes', '%', '20-40', formData.completeBloodCount?.lymphocytes)}
                    {renderTestField('completeBloodCount', 'monocytes', 'Monocytes', '%', '2-8', formData.completeBloodCount?.monocytes)}
                    {renderTestField('completeBloodCount', 'eosinophils', 'Eosinophils', '%', '1-4', formData.completeBloodCount?.eosinophils)}
                    {renderTestField('completeBloodCount', 'basophils', 'Basophils', '%', '0.5-1', formData.completeBloodCount?.basophils)}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-3">RBC Indices</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {renderTestField('completeBloodCount', 'mcv', 'MCV', 'fL', '80-100', formData.completeBloodCount?.mcv)}
                    {renderTestField('completeBloodCount', 'mch', 'MCH', 'pg', '27-32', formData.completeBloodCount?.mch)}
                    {renderTestField('completeBloodCount', 'mchc', 'MCHC', 'g/dL', '32-36', formData.completeBloodCount?.mchc)}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Clinical Interpretation</Label>
                <Textarea
                  value={formData.completeBloodCount?.interpretation || ''}
                  onChange={(e) => updateSection('completeBloodCount', 'interpretation', e.target.value)}
                  placeholder="Enter clinical interpretation of CBC results"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Additional Notes</Label>
                <Textarea
                  value={formData.completeBloodCount?.notes || ''}
                  onChange={(e) => updateSection('completeBloodCount', 'notes', e.target.value)}
                  placeholder="Additional notes about CBC"
                  rows={2}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Metabolic Panel */}
      <Card>
        <Collapsible open={openSections.metabolic} onOpenChange={() => toggleSection('metabolic')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Metabolic Panel (CMP)</CardTitle>
                  <CardDescription>Glucose, HbA1c, Electrolytes, Kidney function, Lipids</CardDescription>
                </div>
                {openSections.metabolic ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Performed</Label>
                  <Input
                    type="date"
                    value={formData.metabolicPanel?.datePerformed || ''}
                    onChange={(e) => updateSection('metabolicPanel', 'datePerformed', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-3">Glucose & Diabetes Markers</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {renderTestField('metabolicPanel', 'glucose', 'Fasting Glucose', 'mg/dL', '70-100', formData.metabolicPanel?.glucose)}
                    {renderTestField('metabolicPanel', 'hba1c', 'HbA1c', '%', '<5.7', formData.metabolicPanel?.hba1c)}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-3">Kidney Function</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {renderTestField('metabolicPanel', 'creatinine', 'Creatinine', 'mg/dL', '0.6-1.2', formData.metabolicPanel?.creatinine)}
                    {renderTestField('metabolicPanel', 'bun', 'BUN', 'mg/dL', '7-20', formData.metabolicPanel?.bun)}
                    {renderTestField('metabolicPanel', 'egfr', 'eGFR', 'mL/min/1.73m²', '>60', formData.metabolicPanel?.egfr)}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-slate-700 mb-3">Electrolytes</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {renderTestField('metabolicPanel', 'sodium', 'Sodium', 'mEq/L', '136-145', formData.metabolicPanel?.sodium)}
                    {renderTestField('metabolicPanel', 'potassium', 'Potassium', 'mEq/L', '3.5-5.0', formData.metabolicPanel?.potassium)}
                    {renderTestField('metabolicPanel', 'chloride', 'Chloride', 'mEq/L', '98-107', formData.metabolicPanel?.chloride)}
                    {renderTestField('metabolicPanel', 'co2', 'CO2', 'mEq/L', '22-28', formData.metabolicPanel?.co2)}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    {renderTestField('metabolicPanel', 'anionGap', 'Anion Gap', 'mEq/L', '8-16', formData.metabolicPanel?.anionGap)}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Clinical Interpretation</Label>
                <Textarea
                  value={formData.metabolicPanel?.interpretation || ''}
                  onChange={(e) => updateSection('metabolicPanel', 'interpretation', e.target.value)}
                  placeholder="Enter clinical interpretation of metabolic panel results"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Additional Notes</Label>
                <Textarea
                  value={formData.metabolicPanel?.notes || ''}
                  onChange={(e) => updateSection('metabolicPanel', 'notes', e.target.value)}
                  placeholder="Additional notes about metabolic panel"
                  rows={2}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Lipid Panel */}
      <Card>
        <Collapsible open={openSections.lipid} onOpenChange={() => toggleSection('lipid')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Lipid Panel</CardTitle>
                  <CardDescription>Total Cholesterol, LDL, HDL, Triglycerides</CardDescription>
                </div>
                {openSections.lipid ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Performed</Label>
                  <Input
                    type="date"
                    value={formData.lipidPanel?.datePerformed || ''}
                    onChange={(e) => updateSection('lipidPanel', 'datePerformed', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderTestField('lipidPanel', 'totalCholesterol', 'Total Cholesterol', 'mg/dL', '<200', formData.lipidPanel?.totalCholesterol)}
                {renderTestField('lipidPanel', 'ldl', 'LDL Cholesterol', 'mg/dL', '<100', formData.lipidPanel?.ldl)}
                {renderTestField('lipidPanel', 'hdl', 'HDL Cholesterol', 'mg/dL', '>40', formData.lipidPanel?.hdl)}
                {renderTestField('lipidPanel', 'triglycerides', 'Triglycerides', 'mg/dL', '<150', formData.lipidPanel?.triglycerides)}
              </div>

              <div className="space-y-2">
                <Label>Clinical Interpretation</Label>
                <Textarea
                  value={formData.lipidPanel?.interpretation || ''}
                  onChange={(e) => updateSection('lipidPanel', 'interpretation', e.target.value)}
                  placeholder="Enter clinical interpretation of lipid panel results"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Additional Notes</Label>
                <Textarea
                  value={formData.lipidPanel?.notes || ''}
                  onChange={(e) => updateSection('lipidPanel', 'notes', e.target.value)}
                  placeholder="Additional notes about lipid panel"
                  rows={2}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Liver Function Tests */}
      <Card>
        <Collapsible open={openSections.liver} onOpenChange={() => toggleSection('liver')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Liver Function Tests (LFT)</CardTitle>
                  <CardDescription>ALT, AST, Bilirubin, Alkaline Phosphatase, GGT, Albumin, PT/INR</CardDescription>
                </div>
                {openSections.liver ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Performed</Label>
                  <Input
                    type="date"
                    value={formData.liverFunction?.datePerformed || ''}
                    onChange={(e) => updateSection('liverFunction', 'datePerformed', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderTestField('liverFunction', 'alt', 'ALT', 'U/L', '7-56', formData.liverFunction?.alt)}
                {renderTestField('liverFunction', 'ast', 'AST', 'U/L', '10-40', formData.liverFunction?.ast)}
                {renderTestField('liverFunction', 'totalBilirubin', 'Total Bilirubin', 'mg/dL', '0.2-1.2', formData.liverFunction?.totalBilirubin)}
                {renderTestField('liverFunction', 'directBilirubin', 'Direct Bilirubin', 'mg/dL', '0.0-0.3', formData.liverFunction?.directBilirubin)}
                {renderTestField('liverFunction', 'alkalinePhosphatase', 'Alkaline Phosphatase', 'U/L', '44-147', formData.liverFunction?.alkalinePhosphatase)}
                {renderTestField('liverFunction', 'ggt', 'GGT', 'U/L', '9-48', formData.liverFunction?.ggt)}
                {renderTestField('liverFunction', 'albumin', 'Albumin', 'g/dL', '3.5-5.0', formData.liverFunction?.albumin)}
                {renderTestField('liverFunction', 'ptInr', 'PT/INR', 'ratio', '0.8-1.1', formData.liverFunction?.ptInr)}
              </div>

              <div className="space-y-2">
                <Label>Clinical Interpretation</Label>
                <Textarea
                  value={formData.liverFunction?.interpretation || ''}
                  onChange={(e) => updateSection('liverFunction', 'interpretation', e.target.value)}
                  placeholder="Enter clinical interpretation of liver function results"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Additional Notes</Label>
                <Textarea
                  value={formData.liverFunction?.notes || ''}
                  onChange={(e) => updateSection('liverFunction', 'notes', e.target.value)}
                  placeholder="Additional notes about liver function tests"
                  rows={2}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Thyroid Function Tests */}
      <Card>
        <Collapsible open={openSections.thyroid} onOpenChange={() => toggleSection('thyroid')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Thyroid Function Tests</CardTitle>
                  <CardDescription>TSH, Free T3, Free T4, Reverse T3</CardDescription>
                </div>
                {openSections.thyroid ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Performed</Label>
                  <Input
                    type="date"
                    value={formData.thyroidFunction?.datePerformed || ''}
                    onChange={(e) => updateSection('thyroidFunction', 'datePerformed', e.target.value)}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderTestField('thyroidFunction', 'tsh', 'TSH', 'mIU/L', '0.4-4.0', formData.thyroidFunction?.tsh)}
                {renderTestField('thyroidFunction', 'freeT3', 'Free T3', 'pg/mL', '2.3-4.2', formData.thyroidFunction?.freeT3)}
                {renderTestField('thyroidFunction', 'freeT4', 'Free T4', 'ng/dL', '0.8-1.8', formData.thyroidFunction?.freeT4)}
                {renderTestField('thyroidFunction', 'reverseT3', 'Reverse T3', 'ng/dL', '8-25', formData.thyroidFunction?.reverseT3)}
              </div>

              <div className="space-y-2">
                <Label>Clinical Interpretation</Label>
                <Textarea
                  value={formData.thyroidFunction?.interpretation || ''}
                  onChange={(e) => updateSection('thyroidFunction', 'interpretation', e.target.value)}
                  placeholder="Enter clinical interpretation of thyroid function results"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Additional Notes</Label>
                <Textarea
                  value={formData.thyroidFunction?.notes || ''}
                  onChange={(e) => updateSection('thyroidFunction', 'notes', e.target.value)}
                  placeholder="Additional notes about thyroid function tests"
                  rows={2}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Vitamin Levels */}
      <Card>
        <Collapsible open={openSections.vitamins} onOpenChange={() => toggleSection('vitamins')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Vitamin Levels</CardTitle>
                  <CardDescription>B12, D3, Folate, B1 (Thiamine), B6</CardDescription>
                </div>
                {openSections.vitamins ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Performed</Label>
                  <Input
                    type="date"
                    value={formData.vitaminLevels?.datePerformed || ''}
                    onChange={(e) => updateSection('vitaminLevels', 'datePerformed', e.target.value)}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderTestField('vitaminLevels', 'vitaminB12', 'Vitamin B12', 'pg/mL', '300-900', formData.vitaminLevels?.vitaminB12)}
                {renderTestField('vitaminLevels', 'vitaminD3', 'Vitamin D3', 'ng/mL', '30-100', formData.vitaminLevels?.vitaminD3)}
                {renderTestField('vitaminLevels', 'folate', 'Folate', 'ng/mL', '3-17', formData.vitaminLevels?.folate)}
                {renderTestField('vitaminLevels', 'thiamine', 'Thiamine (B1)', 'nmol/L', '70-180', formData.vitaminLevels?.thiamine)}
                {renderTestField('vitaminLevels', 'vitaminB6', 'Vitamin B6', 'ng/mL', '5-50', formData.vitaminLevels?.vitaminB6)}
              </div>

              <div className="space-y-2">
                <Label>Clinical Interpretation</Label>
                <Textarea
                  value={formData.vitaminLevels?.interpretation || ''}
                  onChange={(e) => updateSection('vitaminLevels', 'interpretation', e.target.value)}
                  placeholder="Enter clinical interpretation of vitamin level results"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Additional Notes</Label>
                <Textarea
                  value={formData.vitaminLevels?.notes || ''}
                  onChange={(e) => updateSection('vitaminLevels', 'notes', e.target.value)}
                  placeholder="Additional notes about vitamin levels"
                  rows={2}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

    </div>
  )
}
