"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Activity, 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  Trash2, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  Zap,
  Brain,
  Eye
} from "lucide-react"

// Common imaging and neurological tests
const IMAGING_TESTS = [
  {
    name: "Brain MRI",
    category: "Neuroimaging",
    description: "Magnetic Resonance Imaging of the brain",
    commonFindings: ["Normal", "White matter changes", "Atrophy", "Lesions", "Vascular changes"]
  },
  {
    name: "Brain CT",
    category: "Neuroimaging", 
    description: "Computed Tomography of the brain",
    commonFindings: ["Normal", "Atrophy", "Hemorrhage", "Infarct", "Mass effect"]
  },
  {
    name: "EEG",
    category: "Neurophysiology",
    description: "Electroencephalogram",
    commonFindings: ["Normal", "Generalized slowing", "Focal abnormalities", "Epileptiform activity", "Sleep abnormalities"]
  },
  {
    name: "SPECT",
    category: "Nuclear Medicine",
    description: "Single Photon Emission Computed Tomography",
    commonFindings: ["Normal perfusion", "Hypoperfusion", "Hyperperfusion", "Asymmetric perfusion"]
  },
  {
    name: "PET Scan",
    category: "Nuclear Medicine", 
    description: "Positron Emission Tomography",
    commonFindings: ["Normal metabolism", "Hypometabolism", "Hypermetabolism", "Alzheimer pattern"]
  },
  {
    name: "fMRI",
    category: "Functional Imaging",
    description: "Functional Magnetic Resonance Imaging",
    commonFindings: ["Normal activation", "Altered activation patterns", "Connectivity changes"]
  },
  {
    name: "DTI",
    category: "Advanced MRI",
    description: "Diffusion Tensor Imaging",
    commonFindings: ["Normal white matter integrity", "Reduced FA", "Increased diffusivity", "Tract disruption"]
  },
  {
    name: "Neuropsychological Testing",
    category: "Cognitive Assessment",
    description: "Comprehensive cognitive evaluation",
    commonFindings: ["Normal cognition", "Memory impairment", "Executive dysfunction", "Language deficits", "Attention deficits"]
  }
]

const NEUROLOGICAL_TESTS = [
  {
    name: "EMG",
    category: "Neurophysiology",
    description: "Electromyography",
    commonFindings: ["Normal", "Myopathic changes", "Neuropathic changes", "Denervation"]
  },
  {
    name: "Nerve Conduction Study",
    category: "Neurophysiology",
    description: "Peripheral nerve function assessment",
    commonFindings: ["Normal", "Demyelinating", "Axonal", "Mixed pattern"]
  },
  {
    name: "Evoked Potentials",
    category: "Neurophysiology", 
    description: "Visual, auditory, or somatosensory evoked potentials",
    commonFindings: ["Normal latencies", "Prolonged latencies", "Reduced amplitudes", "Absent responses"]
  },
  {
    name: "Sleep Study",
    category: "Sleep Medicine",
    description: "Polysomnography",
    commonFindings: ["Normal sleep", "Sleep apnea", "Periodic limb movements", "REM abnormalities", "Insomnia"]
  }
]

interface ImagingStudy {
  id?: string
  testName: string
  category: string
  datePerformed?: string
  facility?: string
  findings?: string
  impression?: string
  notes?: string
  isNormal: boolean
  isUrgent: boolean
  followUpRequired: boolean
}

interface NeurologicalTest {
  id?: string
  testName: string
  category: string
  datePerformed?: string
  facility?: string
  results?: string
  interpretation?: string
  notes?: string
  isNormal: boolean
  followUpRequired: boolean
}

interface ImagingStudiesData {
  imagingStudies: Record<string, ImagingStudy>
  neurologicalTests: Record<string, NeurologicalTest>
}

interface ImagingStudiesPanelProps {
  data: ImagingStudiesData
  onUpdate: (data: ImagingStudiesData) => void
}

export default function ImagingStudiesPanel({ data, onUpdate }: ImagingStudiesPanelProps) {
  const [formData, setFormData] = useState<ImagingStudiesData>(() => data || {
    imagingStudies: {},
    neurologicalTests: {}
  })
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  // Update formData when data prop changes
  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      setFormData(data)
    }
  }, [data])

  // Notify parent of changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onUpdate(formData)
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [formData, onUpdate])

  const updateImagingStudy = (testName: string, field: keyof ImagingStudy, value: any) => {
    setFormData(prev => ({
      ...prev,
      imagingStudies: {
        ...prev.imagingStudies,
        [testName]: {
          ...prev.imagingStudies[testName],
          testName,
          [field]: value
        }
      }
    }))
  }

  const updateNeurologicalTest = (testName: string, field: keyof NeurologicalTest, value: any) => {
    setFormData(prev => ({
      ...prev,
      neurologicalTests: {
        ...prev.neurologicalTests,
        [testName]: {
          ...prev.neurologicalTests[testName],
          testName,
          [field]: value
        }
      }
    }))
  }

  const addImagingStudy = (testName: string) => {
    const testInfo = IMAGING_TESTS.find(t => t.name === testName)
    setFormData(prev => ({
      ...prev,
      imagingStudies: {
        ...prev.imagingStudies,
        [testName]: {
          testName,
          category: testInfo?.category || "",
          isNormal: true,
          isUrgent: false,
          followUpRequired: false,
          datePerformed: new Date().toISOString().split('T')[0]
        }
      }
    }))
    setOpenSections(prev => ({ ...prev, [`imaging-${testName}`]: true }))
  }

  const addNeurologicalTest = (testName: string) => {
    const testInfo = NEUROLOGICAL_TESTS.find(t => t.name === testName)
    setFormData(prev => ({
      ...prev,
      neurologicalTests: {
        ...prev.neurologicalTests,
        [testName]: {
          testName,
          category: testInfo?.category || "",
          isNormal: true,
          followUpRequired: false,
          datePerformed: new Date().toISOString().split('T')[0]
        }
      }
    }))
    setOpenSections(prev => ({ ...prev, [`neuro-${testName}`]: true }))
  }

  const removeImagingStudy = (testName: string) => {
    setFormData(prev => {
      const newStudies = { ...prev.imagingStudies }
      delete newStudies[testName]
      return {
        ...prev,
        imagingStudies: newStudies
      }
    })
    setOpenSections(prev => {
      const newSections = { ...prev }
      delete newSections[`imaging-${testName}`]
      return newSections
    })
  }

  const removeNeurologicalTest = (testName: string) => {
    setFormData(prev => {
      const newTests = { ...prev.neurologicalTests }
      delete newTests[testName]
      return {
        ...prev,
        neurologicalTests: newTests
      }
    })
    setOpenSections(prev => {
      const newSections = { ...prev }
      delete newSections[`neuro-${testName}`]
      return newSections
    })
  }

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  const availableImagingTests = IMAGING_TESTS.filter(test => 
    !formData.imagingStudies[test.name]
  )

  const availableNeurologicalTests = NEUROLOGICAL_TESTS.filter(test => 
    !formData.neurologicalTests[test.name]
  )

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-bold text-slate-900 mb-2">Imaging Studies & Neurological Tests</h3>
        <p className="text-sm text-slate-600">Neuroimaging, EEG, and other diagnostic procedures</p>
      </div>

      {/* Imaging Studies Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Imaging Studies
              </CardTitle>
              <CardDescription>Neuroimaging and brain imaging studies</CardDescription>
            </div>
            {availableImagingTests.length > 0 && (
              <Select onValueChange={addImagingStudy}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Add imaging study..." />
                </SelectTrigger>
                <SelectContent>
                  {availableImagingTests.map(test => (
                    <SelectItem key={test.name} value={test.name}>
                      <div className="flex flex-col">
                        <span className="font-medium">{test.name}</span>
                        <span className="text-xs text-slate-500">{test.category}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {Object.keys(formData.imagingStudies).length === 0 ? (
            <div className="text-center py-6 text-slate-500">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No imaging studies added yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(formData.imagingStudies).map(([testName, studyData]) => {
                const sectionId = `imaging-${testName}`
                const testInfo = IMAGING_TESTS.find(t => t.name === testName)
                
                return (
                  <Card key={testName} className="border-l-4 border-l-blue-500">
                    <Collapsible open={openSections[sectionId]} onOpenChange={() => toggleSection(sectionId)}>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors py-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div>
                                <CardTitle className="text-base flex items-center gap-2">
                                  {testName}
                                  {studyData.isUrgent && <AlertTriangle className="h-4 w-4 text-red-500" />}
                                  {!studyData.isNormal && <Badge variant="destructive" className="text-xs">Abnormal</Badge>}
                                  {studyData.followUpRequired && <Badge variant="outline" className="text-xs">Follow-up</Badge>}
                                </CardTitle>
                                <CardDescription className="text-sm">
                                  {studyData.category} • {studyData.datePerformed || 'Date not set'}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  removeImagingStudy(testName)
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              {openSections[sectionId] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                            </div>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Date Performed</Label>
                              <Input
                                type="date"
                                value={studyData.datePerformed || ''}
                                onChange={(e) => updateImagingStudy(testName, 'datePerformed', e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Facility/Location</Label>
                              <Input
                                value={studyData.facility || ''}
                                onChange={(e) => updateImagingStudy(testName, 'facility', e.target.value)}
                                placeholder="Hospital/clinic name"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                checked={studyData.isNormal}
                                onCheckedChange={(checked) => updateImagingStudy(testName, 'isNormal', checked)}
                              />
                              <Label className="text-sm">Normal findings</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                checked={studyData.isUrgent}
                                onCheckedChange={(checked) => updateImagingStudy(testName, 'isUrgent', checked)}
                              />
                              <Label className="text-sm">Urgent/Critical</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                checked={studyData.followUpRequired}
                                onCheckedChange={(checked) => updateImagingStudy(testName, 'followUpRequired', checked)}
                              />
                              <Label className="text-sm">Follow-up required</Label>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Findings</Label>
                            <Textarea
                              value={studyData.findings || ''}
                              onChange={(e) => updateImagingStudy(testName, 'findings', e.target.value)}
                              placeholder="Detailed findings and observations"
                              rows={3}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Impression/Conclusion</Label>
                            <Textarea
                              value={studyData.impression || ''}
                              onChange={(e) => updateImagingStudy(testName, 'impression', e.target.value)}
                              placeholder="Radiologist's impression and clinical correlation"
                              rows={2}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Additional Notes</Label>
                            <Textarea
                              value={studyData.notes || ''}
                              onChange={(e) => updateImagingStudy(testName, 'notes', e.target.value)}
                              placeholder="Additional clinical notes or context"
                              rows={2}
                            />
                          </div>

                          {testInfo && (
                            <div className="p-3 bg-blue-50 rounded-lg">
                              <div className="text-sm">
                                <div className="font-medium text-blue-900 mb-1">Study Information</div>
                                <div className="text-blue-700">{testInfo.description}</div>
                                {testInfo.commonFindings && (
                                  <div className="mt-2">
                                    <span className="font-medium">Common findings: </span>
                                    {testInfo.commonFindings.join(', ')}
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </CollapsibleContent>
                    </Collapsible>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Neurological Tests Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Neurological Tests
              </CardTitle>
              <CardDescription>Neurophysiology and specialized neurological assessments</CardDescription>
            </div>
            {availableNeurologicalTests.length > 0 && (
              <Select onValueChange={addNeurologicalTest}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Add neurological test..." />
                </SelectTrigger>
                <SelectContent>
                  {availableNeurologicalTests.map(test => (
                    <SelectItem key={test.name} value={test.name}>
                      <div className="flex flex-col">
                        <span className="font-medium">{test.name}</span>
                        <span className="text-xs text-slate-500">{test.category}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {Object.keys(formData.neurologicalTests).length === 0 ? (
            <div className="text-center py-6 text-slate-500">
              <Zap className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No neurological tests added yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(formData.neurologicalTests).map(([testName, testData]) => {
                const sectionId = `neuro-${testName}`
                const testInfo = NEUROLOGICAL_TESTS.find(t => t.name === testName)
                
                return (
                  <Card key={testName} className="border-l-4 border-l-green-500">
                    <Collapsible open={openSections[sectionId]} onOpenChange={() => toggleSection(sectionId)}>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors py-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div>
                                <CardTitle className="text-base flex items-center gap-2">
                                  {testName}
                                  {!testData.isNormal && <Badge variant="destructive" className="text-xs">Abnormal</Badge>}
                                  {testData.followUpRequired && <Badge variant="outline" className="text-xs">Follow-up</Badge>}
                                </CardTitle>
                                <CardDescription className="text-sm">
                                  {testData.category} • {testData.datePerformed || 'Date not set'}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  removeNeurologicalTest(testName)
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              {openSections[sectionId] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                            </div>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Date Performed</Label>
                              <Input
                                type="date"
                                value={testData.datePerformed || ''}
                                onChange={(e) => updateNeurologicalTest(testName, 'datePerformed', e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Facility/Location</Label>
                              <Input
                                value={testData.facility || ''}
                                onChange={(e) => updateNeurologicalTest(testName, 'facility', e.target.value)}
                                placeholder="Hospital/clinic name"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                checked={testData.isNormal}
                                onCheckedChange={(checked) => updateNeurologicalTest(testName, 'isNormal', checked)}
                              />
                              <Label className="text-sm">Normal results</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                checked={testData.followUpRequired}
                                onCheckedChange={(checked) => updateNeurologicalTest(testName, 'followUpRequired', checked)}
                              />
                              <Label className="text-sm">Follow-up required</Label>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Results</Label>
                            <Textarea
                              value={testData.results || ''}
                              onChange={(e) => updateNeurologicalTest(testName, 'results', e.target.value)}
                              placeholder="Detailed test results and measurements"
                              rows={3}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Interpretation</Label>
                            <Textarea
                              value={testData.interpretation || ''}
                              onChange={(e) => updateNeurologicalTest(testName, 'interpretation', e.target.value)}
                              placeholder="Clinical interpretation and significance"
                              rows={2}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Additional Notes</Label>
                            <Textarea
                              value={testData.notes || ''}
                              onChange={(e) => updateNeurologicalTest(testName, 'notes', e.target.value)}
                              placeholder="Additional clinical notes or context"
                              rows={2}
                            />
                          </div>

                          {testInfo && (
                            <div className="p-3 bg-green-50 rounded-lg">
                              <div className="text-sm">
                                <div className="font-medium text-green-900 mb-1">Test Information</div>
                                <div className="text-green-700">{testInfo.description}</div>
                                {testInfo.commonFindings && (
                                  <div className="mt-2">
                                    <span className="font-medium">Common findings: </span>
                                    {testInfo.commonFindings.join(', ')}
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </CollapsibleContent>
                    </Collapsible>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export type { ImagingStudiesData }
